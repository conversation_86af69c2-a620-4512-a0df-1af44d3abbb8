{"name": "magento/project-enterprise-edition", "description": "eCommerce Platform for Growth (Enterprise Edition)", "type": "project", "license": ["proprietary"], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true}, "preferred-install": "dist", "sort-packages": true}, "version": "2.4.7-p2", "require": {"adyen/module-payment": "^9.9", "afterpay-global/module-afterpay": "^5.4", "algolia/algoliasearch-magento-2": " ^3.14", "blackbird/module-hyva-algolia-search": "^100.0", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-luma-checkout": "^1.1", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "^2.0.4", "magento/product-enterprise-edition": "2.4.7-p2", "magento/quality-patches": "^1.1", "mirasvit/module-blog-mx": "^3.1", "mirasvit/module-blog-mx-hyva": "^2.1", "zip/magento2": "^1.2"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2", "dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.22", "lusitanian/oauth": "^0.8", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.7", "pdepend/pdepend": "^2.12", "phpmd/phpmd": "^2.13", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "sebastian/phpcpd": "^6.0", "squizlabs/php_codesniffer": "^3.6", "symfony/finder": "^6.4"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"private-packagist": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/mycar-com-au-04fugc7"}, "mirasvit-blg2": {"type": "composer", "url": "https://54046:<EMAIL>/54046:WBMUFL6SAZ/"}, "0": {"type": "composer", "url": "https://repo.magento.com/"}}, "extra": {"magento-force": "override"}}