{"info": {"_postman_id": "b9c61def-81d0-413f-9038-1bd9fd56fef0", "name": "Mycar Stores API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "6216659"}, "item": [{"name": "Get Store Information", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/rest/V1/stores/{{store_id}}", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "{{store_id}}"]}}, "response": []}, {"name": "Create New Store", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"store\": {\n        \"store_code\": \"teststore\",\n        \"name\": \"Test Store\",\n        \"lat\": 21.027763,\n        \"long\": 105.834160,\n        \"description\": \"Test Store\",\n        \"phone\": \"984654654\",\n        \"address\": \"Test Address\",\n        \"state\": \"Test State\",\n        \"postcode\": 2000,\n        \"ev\": 1,\n        \"ev_charging\": 1,\n        \"enabled\": 1,\n        \"driving_instructions\": \"Test Driving Instruction\",\n        \"public_transport_instructions\": \"Test Public Transport Instructions\",\n        \"has_waiting_room\": 1,\n        \"has_key_dropoff\": 1,\n        \"store_manager\": \"Test Store Manager\",\n        \"is_link_store\": 1,\n        \"is_dark_store\": 1,\n        \"timezone\": \"Australia/Brisbane\",\n        \"is_mobile\": 1,\n        \"extension_attributes\": {\n            \"store_services\": [\n                {\n                    \"product_id\": 107488,\n                    \"available\": 1\n                }\n            ],\n            \"store_opening_time\": [\n                {\n                    \"day\": \"Monday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"03:00 PM\"\n                },\n                {\n                    \"day\": \"Tuesday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"06:00 PM\"\n                },\n                {\n                    \"day\": \"Wednesday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"05:00 PM\"\n                },\n                {\n                    \"day\": \"Thursday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"05:00 PM\"\n                },\n                {\n                    \"day\": \"Friday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"06:00 PM\"\n                }\n            ],\n            \"type\": {\n                \"type\": \"Test Type\"\n            },\n            \"store_mobile_postcode\": [\n                {\n                    \"postcode\": 123\n                },\n                {\n                    \"postcode\": 456\n                },\n                {\n                    \"postcode\": 3333\n                },\n                {\n                    \"postcode\": 4444\n                },\n                {\n                    \"postcode\": 5555\n                },\n                {\n                    \"postcode\": 6666\n                }\n            ]\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores", "host": ["{{URL}}"], "path": ["rest", "V1", "stores"]}}, "response": []}, {"name": "Delete Store By Store ID", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores/{{store_id}}", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "{{store_id}}"]}}, "response": []}, {"name": "Update Store Information By Store Code", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"store\": {\n        \"name\": \"Test Update Store\",\n        \"lat\": 21.027763,\n        \"long\": 105.834160,\n        \"description\": \"Test Update Store\",\n        \"phone\": \"984654654\",\n        \"address\": \"Test Update Address\",\n        \"state\": \"Test Update State\",\n        \"postcode\": 3000,\n        \"ev\": 1,\n        \"ev_charging\": 1,\n        \"enabled\": 1,\n        \"driving_instructions\": \"Test Update Driving Instruction\",\n        \"public_transport_instructions\": \"Test Update Public Transport Instructions\",\n        \"has_waiting_room\": 1,\n        \"has_key_dropoff\": 1,\n        \"store_manager\": \"Test Update Store Manager\",\n        \"is_link_store\": 1,\n        \"is_dark_store\": 1,\n        \"timezone\": \"Australia/Brisbane\",\n        \"is_mobile\": 1,\n        \"extension_attributes\": {\n            \"store_services\": [\n                {\n                    \"product_id\": 107488,\n                    \"available\": 1\n                }\n            ],\n            \"store_opening_time\": [\n                {\n                    \"day\": \"Monday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"03:00 PM\"\n                },\n                {\n                    \"day\": \"Tuesday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"06:00 PM\"\n                },\n                {\n                    \"day\": \"Wednesday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"05:00 PM\"\n                },\n                {\n                    \"day\": \"Thursday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"05:00 PM\"\n                },\n                {\n                    \"day\": \"Friday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"06:00 PM\"\n                }\n            ],\n            \"type\": {\n                \"type\": \"Test Type\"\n            },\n            \"store_mobile_postcode\": [\n                {\n                    \"postcode\": 123\n                },\n                {\n                    \"postcode\": 456\n                },\n                {\n                    \"postcode\": 3333\n                },\n                {\n                    \"postcode\": 4444\n                },\n                {\n                    \"postcode\": 5555\n                },\n                {\n                    \"postcode\": 6666\n                }\n            ]\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores/{{store_code}}", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "{{store_code}}"]}}, "response": []}, {"name": "Update Store Service By Store Code", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"store\": {\n        \"extension_attributes\": {\n            \"store_services\": [\n                {\n                    \"product_id\": 107488,\n                    \"available\": 0\n                },\n                {\n                    \"product_id\": 107489,\n                    \"available\": 0\n                }\n            ]\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores/test", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "test"]}}, "response": []}, {"name": "Update Store Type By Store Code", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"store\": {\n        \"extension_attributes\": {\n            \"type\": {\n                \"type\": \"Test Update Store Type3\"\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores/{{store_code}}", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "{{store_code}}"]}}, "response": []}, {"name": "Update Store Mobile Postcode By Store Code", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"store\": {\n        \"extension_attributes\": {\n            \"store_mobile_postcode\": [\n                {\n                    \"postcode\": 2000\n                },\n                {\n                    \"postcode\": 3000\n                }\n            ]\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores/{{store_code}}", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "{{store_code}}"]}}, "response": []}, {"name": "Update Store Opening Time By Store Code", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"store\": {\n        \"extension_attributes\": {\n            \"store_opening_time\": [\n                {\n                    \"day\": \"Monday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"03:00 PM\"\n                },\n                {\n                    \"day\": \"Tuesday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"06:00 PM\"\n                },\n                {\n                    \"day\": \"Wednesday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"07:00 PM\"\n                },\n                {\n                    \"day\": \"Thursday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"06:00 PM\"\n                },\n                {\n                    \"day\": \"Friday\",\n                    \"opening_time\": \"12:00 AM\",\n                    \"closing_time\": \"07:00 PM\"\n                }\n            ]\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/rest/V1/stores/{{store_code}}", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "{{store_code}}"]}}, "response": []}, {"name": "Get Nearest Store", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/rest/V1/stores/nearest/21.027763/105.834160", "host": ["{{URL}}"], "path": ["rest", "V1", "stores", "nearest", "21.027763", "105.834160"]}}, "response": []}]}