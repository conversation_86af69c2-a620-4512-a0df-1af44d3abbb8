<?php
namespace Webqem\Customer\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\App\RequestInterface;

class Benefit extends Template
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * Constructor
     *
     * @param Template\Context $context
     * @param RequestInterface $request
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        RequestInterface $request,
        array $data = []
    ) {
        $this->request = $request;
        parent::__construct($context, $data);
    }

    /**
     * Get vehicle data (hardcoded for now)
     */
    public function getVehicle()
    {
        if ($this->request->getParam('emptyData') === 'true') {
            return null;
        }

        return [
            'name' => 'Toyota Land Cruiser',
            'image' => $this->getViewFileUrl('Webqem_Customer::images/vehicle_sample.jpg'),
            'label' => 'Car 123',
            'info' => '4SP Auto 4.0L 6CYL Petrol 2009'
        ];
    }

    /**
     * Get benefits for vehicle (hardcoded for now)
     */
    public function getVehicleBenefits()
    {
        if ($this->request->getParam('emptyData') === 'true') {
            return null;
        }

        return [
            [
                'name' => '24h Free Roadside Assistance',
                'description' => "<ul>
                                <li>Towing (up to 20kms in metropolitan areas or up to 50kms for regional areas</li>
                                <li>Flat tyre assistance</li>
                                <li>Flat or faulty battery assistance</li>
                                <li>Lorem ipsum dolor sit amet, consectetur, adipisicing elit. Voluptas, quod?</li>
                                <li>Delectus impedit nulla obcaecati, architecto! Labore quia facere earum accusamus.</li>
                                <li>Minima, cumque officiis quidem maiores id veniam dicta non, quia.</li>
                                </ul>",
                'cover_expire_date' => '2025-12-31',
                'cover_status' => true,
                'learn_more_url' => '#',
                'cta_label' => 'Call Now 1900xxx',
                'cta_url' => '#'
            ],
            [
                'name' => 'Extended Warranty',
                'description' => '<p>Covers major mechanical and electrical components.</p>',
                'cover_expire_date' => '2025-12-31',
                'cover_status' => true,
                'learn_more_url' => '#',
                'cta_label' => 'Call Now 1900xxx',
                'cta_url' => '#'
            ],
            [
                'name' => 'Tyre & Rim Insurance',
                'description' => '<p>Covers damage to tyres and rims.</p>',
                'cover_expire_date' => null,
                'cover_status' => false,
                'learn_more_url' => '#',
                'cta_label' => 'Claim Now',
                'cta_url' => '#'
            ]
        ];
    }
}
