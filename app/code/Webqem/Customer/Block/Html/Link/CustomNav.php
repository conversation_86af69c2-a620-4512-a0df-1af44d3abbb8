<?php

namespace Webqem\Customer\Block\Html\Link;

use Magento\Framework\View\Element\Html\Link\Current;

/**
 * Enhanced navigation link with proper class handling and current URL detection
 */
class CustomNav extends Current
{
    /**
     * Render block HTML with improved class handling
     *
     * @return string
     */
    protected function _toHtml()
    {
        if (false != $this->getTemplate()) {
            return parent::_toHtml();
        }

        $attributes = $this->getAttributes() ?: [];
        $classes = isset($attributes['class']) ? $attributes['class'] : '';
        
        // Add "current" class if current URL
        if ($this->isCurrent()) {
            $classes .= ' current';
        }
        
        $html = '<li class="nav item ' . $this->escapeHtml($classes) . '">';
        
        // If current, we still create a link but with the current class
        $html .= '<a href="' . $this->escapeHtml($this->getHref()) . '"';
        $html .= $this->getTitle() ? ' title="' . $this->escapeHtml(__($this->getTitle())) . '"' : '';
        $html .= $this->getAttributesHtml() . '>';
        $html .= $this->escapeHtml(__($this->getLabel()));
        $html .= '</a>';
        
        $html .= '</li>';

        return $html;
    }

    /**
     * Generate attributes HTML code excluding class (handled separately)
     *
     * @return string
     */
    private function getAttributesHtml()
    {
        $attributesHtml = '';
        $attributes = $this->getAttributes();
        if ($attributes) {
            foreach ($attributes as $attribute => $value) {
                if ($attribute !== 'class') { // Skip class as we handle it separately
                    $attributesHtml .= ' ' . $attribute . '="' . $this->escapeHtml($value) . '"';
                }
            }
        }

        return $attributesHtml;
    }
}
