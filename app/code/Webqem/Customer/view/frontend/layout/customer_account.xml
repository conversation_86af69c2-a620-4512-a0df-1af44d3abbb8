<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="sidebar.main">
            <block class="Magento\Framework\View\Element\Template" name="sidebar.main.account_nav" template="Magento_Theme::html/container.phtml" before="-">
                <arguments>
                    <argument name="block_css" xsi:type="string">account-nav-container</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="customer_account_navigation">
            <!-- Remove all default navigation items -->
            <referenceBlock name="customer-account-navigation-delimiter-1" remove="true"/>
            <referenceBlock name="customer-account-navigation-delimiter-2" remove="true"/>
            <referenceBlock name="customer-account-navigation-account-link" remove="true"/>
            <referenceBlock name="customer.header.sign.out.link" remove="true"/>
            <referenceBlock name="customer-account-navigation-account-edit-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-address-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-orders-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-downloadable-products-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-wish-list-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-billing-agreements-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-product-reviews-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-newsletter-subscriptions-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-logout-link" remove="true"/>
            <referenceBlock name="customer-account-navigation-my-credit-cards-link" remove="true"/>

            <!-- Add Home link -->
            <block class="Webqem\Customer\Block\Html\Link\CustomNav" name="customer-account-navigation-home-link">
                <arguments>
                    <argument name="path" xsi:type="string">customer/account</argument>
                    <argument name="label" xsi:type="string">Home</argument>
                    <argument name="sortOrder" xsi:type="number">100</argument>
                    <argument name="attributes" xsi:type="array">
                        <item name="class" xsi:type="string">nav-home</item>
                    </argument>
                </arguments>
            </block>

            <!-- Bookings link -->
            <block class="Webqem\Customer\Block\Html\Link\CustomNav" name="customer-account-navigation-bookings-link">
                <arguments>
                    <argument name="path" xsi:type="string">#</argument>
                    <argument name="label" xsi:type="string">Bookings</argument>
                    <argument name="sortOrder" xsi:type="number">200</argument>
                    <argument name="attributes" xsi:type="array">
                        <item name="class" xsi:type="string">nav-bookings</item>
                    </argument>
                </arguments>
            </block>

            <!-- Benefits link -->
            <block class="Webqem\Customer\Block\Html\Link\CustomNav" name="customer-account-navigation-my-benefit-link">
                <arguments>
                    <argument name="path" xsi:type="string">customer/benefit</argument>
                    <argument name="label" xsi:type="string">Benefits</argument>
                    <argument name="sortOrder" xsi:type="number">300</argument>
                    <argument name="attributes" xsi:type="array">
                        <item name="class" xsi:type="string">nav-benefits</item>
                    </argument>
                </arguments>
            </block>

            <!-- Profile link -->
            <block class="Webqem\Customer\Block\Html\Link\CustomNav" name="customer-account-navigation-my-profile-link">
                <arguments>
                    <argument name="path" xsi:type="string">customer/account/profile</argument>
                    <argument name="label" xsi:type="string">Profile</argument>
                    <argument name="sortOrder" xsi:type="number">500</argument>
                    <argument name="attributes" xsi:type="array">
                        <item name="class" xsi:type="string">nav-profile</item>
                    </argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
