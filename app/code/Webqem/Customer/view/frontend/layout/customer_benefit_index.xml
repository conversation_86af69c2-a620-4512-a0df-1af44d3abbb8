<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">My Benefits</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Webqem\Customer\Block\Benefit" name="customer_benefit" template="Webqem_Customer::benefit/index.phtml"/>
        </referenceContainer>
    </body>
</page>
