<?php
/**
 * Webqem Customer - Customer menu override
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Customer;
use Magento\Framework\Escaper;

/** @var Customer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<div
    class="relative inline-block"
    x-data="{ open: false }"
    @keyup.escape="open = false"
    @click.outside="open = false"
>
    <button
        type="button"
        id="customer-menu"
        class="block rounded p-1 relative md:-mt-4"
        @click="open = !open"
        :aria-expanded="open ? 'true' : 'false'"
        aria-label="<?= $escaper->escapeHtmlAttr(__('My Account')) ?>"
        aria-haspopup="true"
    >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 21.8001C20 18.6351 18.1642 15.8992 15.5 14.6001M4 21.8001C4 18.6351 5.83576 15.8992 8.5 14.6001" stroke="#525455" stroke-width="1.4" stroke-linecap="round"/>
            <rect x="5.59961" y="2.6001" width="12.8" height="12.8" rx="6.4" stroke="#525455" stroke-width="1.4"/>
            <path d="M14.0354 10.9597C12.9507 12.0558 11.1921 12.0558 10.1074 10.9597" stroke="#525455" stroke-width="1.4" stroke-linecap="round"/>
        </svg>
        <?php if ($block->customerLoggedIn()): ?>
            <span class="absolute right-0 bottom-1 w-2.5 h-2.5 rounded-full bg-confirmation text-[0px]">Logged in</span>
        <?php endif; ?>
        <span class="absolute top-full left-[50%] translate-x-[-50%] text-xs hidden md:block">Account</span>
    </button>
    <nav
        class="
            absolute right-0 z-20 w-40 py-2 mt-2 -mr-4 px-1 overflow-auto origin-top-right rounded-sm
            shadow-lg border border-panel sm:w-48 lg:mt-3 bg-container-lighter
        "
        x-cloak
        x-show="open"
        aria-labelledby="customer-menu"
    >
        <?php if ($block->customerLoggedIn()): ?>
            <?= $block->getChildHtml('header.customer.logged.in.links') ?>
        <?php else: ?>
            <button type="button"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    @click="open = false; window.showLoginRegisterPopup && window.showLoginRegisterPopup();">
                <?= $escaper->escapeHtml(__('Sign In / Register')) ?>
            </button>
        <?php endif; ?>
    </nav>
</div>
