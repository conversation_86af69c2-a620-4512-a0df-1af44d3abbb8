<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\LoginButton;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Login;
use Magento\Framework\Escaper;

/**
 * @var \Magento\Customer\Block\Form\Login $block
 * @var \Magento\Framework\Escaper $escaper
 * @var \Hyva\Theme\ViewModel\ReCaptcha $recaptcha
 */

$recaptcha = $block->getData('viewModelRecaptcha');
$formId = 'login-form';
?>
<div class="block block-customer-login">
    <div class="block-content p-4">
        <form class="form form-login"
              id="<?= $escaper->escapeHtmlAttr($formId) ?>"
              x-data="ajaxLoginForm()"
              @submit.prevent="submitForm">
            <?= $recaptcha ? $recaptcha->getInputHtml(\Hyva\Theme\ViewModel\ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>

            <div class="field email required mt-0 mb-4">
                <label class="label form-label" for="email"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
                <div class="control">
                    <input name="username"
                           id="email"
                           type="email"
                           class="form-input w-full"
                           title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                           placeholder=" "
                           x-model="formData.username"
                           required>
                </div>
            </div>
            <div class="field password required mt-0 mb-4">
                <label for="pass" class="label form-label"><span><?= $escaper->escapeHtml(__('Password')) ?></span></label>
                <div class="control flex items-center relative">
                    <input name="password"
                           type="password"
                           class="form-input w-full pr-10"
                           id="pass"
                           title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                           placeholder=" "
                           x-model="formData.password"
                           :type="showPassword ? 'text' : 'password'"
                           required>
                    <button
                        type="button"
                        x-on:click="showPassword = !showPassword"
                        class="px-4 py-3 absolute right-0"
                        :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                    >
                        <svg x-show="!showPassword" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <svg x-show="showPassword" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="flex flex-wrap justify-between items-center">
                <?= $block->getChildHtml('login.persistent.remember.me') ?>
            </div>

            <div class="flex flex-col justify-center space-y-3">
                <?= $block->getChildHtml('form_additional_info') ?>
                <button type="button"
                        class="action remind text-center text-sm text-link underline underline-offset-8"
                        @click.prevent="showForgotPassword()">
                    <span><?= $escaper->escapeHtml(__('Forgot Your Password?')) ?></span>
                </button>
            </div>

            <div class="mt-4" x-show="errorMessage" x-cloak>
                <div class="message-error error message" role="alert">
                    <span x-text="errorMessage"></span>
                </div>
            </div>

            <div class="actions-toolbar flex flex-wrap justify-between items-center">
                <div class="primary flex-1">
                    <button type="submit"
                            class="action login primary btn btn-primary w-full"
                            :class="{'opacity-50 cursor-wait': isSubmitting}"
                            :disabled="isSubmitting"
                            name="send" id="send2">
                        <span x-show="!isSubmitting"><?= $escaper->escapeHtml(__('Login')) ?></span>
                        <span x-show="isSubmitting"><?= $escaper->escapeHtml(__('Logging In...')) ?></span>
                    </button>
                </div>
            </div>
            <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(\Hyva\Theme\ViewModel\ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>
            <?= $block->getChildHtml('form.additional.info.login') ?>
        </form>
    </div>
</div>

<script>
    function ajaxLoginForm() {
        return {
            formData: {
                username: '',
                password: '',
                persistent_remember_me: false
            },
            showPassword: false,
            isSubmitting: false,
            errorMessage: '',
            showForgotPassword() {
                // Hide login popup and show forgot password popup
                if (window.Alpine && Alpine.store) {
                    Alpine.store('loginPopup').hide();
                    // Add a short delay to ensure transitions work correctly
                    setTimeout(() => {
                        Alpine.store('forgotPasswordPopup').show();
                    }, 100);
                } else {
                    console.log('Alpine or Alpine.store not available');
                }
            },
            submitForm() {
                this.isSubmitting = true;
                this.errorMessage = '';

                // Check if remember me is checked
                const rememberMeCheckbox = document.querySelector('input[name="persistent[remember_me]"]');
                if (rememberMeCheckbox) {
                    this.formData.persistent_remember_me = rememberMeCheckbox.checked;
                }

                const data = {
                    ...this.formData
                };

                // Handle reCaptcha if present
                <?= $recaptcha ? $recaptcha->getValidationJsHtml(\Hyva\Theme\ViewModel\ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>

                // Convert data to JSON
                const jsonData = JSON.stringify(data);

                // Send AJAX request
                fetch('<?= $escaper->escapeJs($block->getUrl('customer/ajax/login')) ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: jsonData
                })
                    .then(response => response.json())
                    .then(response => {
                        if (response.errors === false) {
                            // If redirect URL is provided, redirect to that URL
                            if (response.redirectUrl) {
                                window.location.href = response.redirectUrl;
                            } else {
                                // Otherwise, reload the page
                                window.location.reload();
                            }
                        } else {
                            // Show error message
                            this.errorMessage = response.message;
                            this.isSubmitting = false;
                        }
                    })
                    .catch(error => {
                        this.errorMessage = '<?= $escaper->escapeJs(__('An error occurred. Please try again later.')) ?>';
                        this.isSubmitting = false;
                        console.error('Login error:', error);
                    });
            }
        }
    }
</script>
