<?php
/**
 * @var \Magento\Customer\Block\Form\Register $block
 * @var \Magento\Framework\Escaper $escaper
 * @var \Hyva\Theme\ViewModel\ReCaptcha $recaptcha
 * @var \Hyva\Theme\Model\ViewModelRegistry $viewModels
 */

use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Customer\Block\Widget\Name as NameWidget;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis

$formId = 'ajax-accountcreate';
$recaptcha = $block->getData('viewModelRecaptcha');
$heroicons = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();
$isAutocompleteEnabled = $block->getConfig('customer/password/autocomplete_on_storefront');

?>
<div class="mb-4" id="register-container">
    <div>
        <form class="form create account form-create-account p-4"
              id="<?= $escaper->escapeHtmlAttr($formId) ?>"
              x-data="ajaxRegisterForm()"
              @submit.prevent="submitForm"
              method="post"
              enctype="multipart/form-data"
              autocomplete="off">

            <div class="mb-3 text-sm" x-show="successMessage" x-cloak>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <span x-html="successMessage"></span>
                </div>
            </div>

            <div class="mb-3 text-sm" x-show="errorMessage" x-cloak>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <span x-html="errorMessage"></span>
                </div>
            </div>

            <?= $block->getChildHtml('form_fields_before') ?>
            <?= $recaptcha ? $recaptcha->getInputHtml('customer_create_account') : '' ?>

            <div class="space-y-4">
                <!-- First name and Last name in one line -->
                <div class="flex gap-4">
                    <div class="field required flex-1">
                        <label class="label form-label" for="firstname">
                            <span><?= $escaper->escapeHtml(__('First Name')) ?></span>
                        </label>
                        <div class="control">
                            <input type="text" id="firstname"
                                   name="firstname"
                                   value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getFirstname()) ?>"
                                   title="<?= $escaper->escapeHtmlAttr(__('First Name')) ?>"
                                   class="form-input w-full"
                                   placeholder=" "
                                   required>
                        </div>
                    </div>

                    <div class="field required flex-1">
                        <label class="label form-label" for="lastname">
                            <span><?= $escaper->escapeHtml(__('Last Name')) ?></span>
                        </label>
                        <div class="control">
                            <input type="text" id="lastname"
                                   name="lastname"
                                   value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getLastname()) ?>"
                                   title="<?= $escaper->escapeHtmlAttr(__('Last Name')) ?>"
                                   class="form-input w-full"
                                   placeholder=" "
                                   required>
                        </div>
                    </div>
                </div>

                <!-- Email -->
                <div class="field field-reserved required">
                    <label for="ajax_email_address" class="label form-label">
                        <span><?= $escaper->escapeHtml(__('Email')) ?></span>
                    </label>
                    <div class="control">
                        <input
                            type="email"
                            name="email"
                            autocomplete="email"
                            id="ajax_email_address"
                            required
                            value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getEmail()) ?>"
                            title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                            placeholder=" "
                            class="form-input w-full"
                        />
                    </div>
                </div>

                <!-- Mobile Number with Australian validation -->
                <div class="field field-reserved required">
                    <label class="label form-label" for="mobile_number">
                        <span><?= $escaper->escapeHtml(__('Mobile Number')) ?></span>
                    </label>
                    <div class="control">
                        <input type="tel"
                               id="mobile_number"
                               name="mobile_number"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getMobileNumber()) ?>"
                               title="<?= $escaper->escapeHtmlAttr(__('Mobile Number')) ?>"
                               class="form-input w-full"
                               placeholder=" "
                               required
                               pattern="^(?:\+614|04)\d{8}$"
                               @input="validateMobileNumber">
                        <div x-show="mobileNumberError" class="text-red-600 text-xs mt-1"
                             x-text="mobileNumberError"></div>
                    </div>
                </div>

                <!-- Password -->
                <div class="field field-reserved required">
                    <label for="ajax_password" class="label form-label">
                        <span><?= $escaper->escapeHtml(__('Password')) ?></span>
                    </label>
                    <div class="control flex items-center">
                        <input
                            :type="showPassword ? 'text' : 'password'"
                            id="ajax_password"
                            name="password"
                            title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                            minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                            class="form-input w-full"
                            required
                            @input="validatePassword"
                            placeholder=" "
                            autocomplete="<?= $isAutocompleteEnabled ? 'new-password' : 'off' ?>"
                        >
                        <button
                            type="button"
                            :aria-pressed="showPassword ? true : false"
                            x-on:click="showPassword = !showPassword"
                            class="px-4 py-3 -ml-12"
                            :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div class="field field-reserved required">
                    <label for="ajax-password-confirmation" class="label form-label">
                        <span><?= $escaper->escapeHtml(__('Confirm Password')) ?></span>
                    </label>
                    <div class="control flex items-center">
                        <input
                            :type="showPasswordConfirm ? 'text' : 'password'"
                            name="password_confirmation"
                            title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                            id="ajax-password-confirmation"
                            required
                            @input="validatePasswordConfirmation"
                            class="form-input w-full"
                            placeholder=" "
                            autocomplete="<?= $isAutocompleteEnabled ? 'new-password' : 'off' ?>"
                        >
                        <button
                            type="button"
                            x-on:click="showPasswordConfirm = !showPasswordConfirm"
                            :aria-pressed="showPasswordConfirm ? true : false"
                            class="px-4 py-3 -ml-12"
                            :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'">
                            <template x-if="!showPasswordConfirm">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPasswordConfirm">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                </div>

                <div x-show="passwordError" class="mt-2 text-red-600 text-sm" x-text="passwordError"></div>
            </div>

            <?php if ($block->isNewsletterEnabled()): ?>
                <div class="mt-4">
                    <div class="field choice newsletter">
                        <input type="checkbox" name="is_subscribed"
                            title="<?= $escaper->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1"
                            id="ajax_is_subscribed"
                            <?php if ($block->getFormData()->getIsSubscribed()): ?>checked<?php endif; ?>
                            class="checkbox">
                        <label for="ajax_is_subscribed" class="label text-sm">
                            <span><?= $escaper->escapeHtml(__('Sign Up for Newsletter')) ?></span>
                        </label>
                    </div>
                    <?= $block->getChildHtml('customer.form.register.newsletter') ?>
                </div>
            <?php endif ?>

            <?= $block->getChildHtml('form_additional_info') ?>
            <?= $recaptcha ? $recaptcha->getLegalNoticeHtml('customer_create_account') : '' ?>

            <div class="actions-toolbar mt-6">
                <div class="primary flex-1">
                    <button type="submit" class="action submit primary btn btn-primary disabled:opacity-75 w-full"
                            :disabled="loading || !formValid"
                            title="<?= $escaper->escapeHtmlAttr(__('Create an Account')) ?>">
                        <span x-show="!loading"><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
                        <span x-show="loading" x-cloak>
                            <?= $escaper->escapeHtml(__('Creating...')) ?>
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>
    <script>
        function ajaxRegisterForm() {
            return {
                loading: false,
                showPassword: false,
                showPasswordConfirm: false,
                errorMessage: '',
                successMessage: '',
                passwordError: '',
                mobileNumberError: '',
                formValid: false,

                init() {
                    // Add event listeners to required fields
                    this.$nextTick(() => {
                        const form = document.getElementById('<?= $escaper->escapeJs($formId) ?>');
                        const requiredFields = form.querySelectorAll('[required]');

                        requiredFields.forEach(field => {
                            field.addEventListener('input', () => this.checkFormValidity());
                        });

                        // Initial check
                        this.checkFormValidity();
                    });
                },

                checkFormValidity() {
                    const form = document.getElementById('<?= $escaper->escapeJs($formId) ?>');
                    const requiredFields = form.querySelectorAll('[required]');

                    // Check if all required fields have values
                    let allFieldsValid = true;
                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            allFieldsValid = false;
                        }
                    });

                    // Only consider the form valid if all fields are filled and there are no validation errors
                    this.formValid = allFieldsValid && !this.passwordError && !this.mobileNumberError;
                },

                validateMobileNumber(event) {
                    const mobileNumber = event.target.value;
                    this.mobileNumberError = '';

                    if (mobileNumber && !mobileNumber.match(/^(?:\+614|04)\d{8}$/)) {
                        this.mobileNumberError = '<?= $escaper->escapeJs(__('Please enter a valid mobile number (04XXXXXXXX or +614XXXXXXXX)')) ?>';
                    }

                    this.checkFormValidity();
                },

                validatePassword() {
                    this.passwordError = '';
                    const password = document.getElementById('ajax_password').value;
                    const confirmation = document.getElementById('ajax-password-confirmation').value;

                    if (confirmation && password !== confirmation) {
                        this.passwordError = '<?= $escaper->escapeJs(__('Passwords do not match.')) ?>';
                    }

                    this.checkFormValidity();
                },

                validatePasswordConfirmation() {
                    this.passwordError = '';
                    const password = document.getElementById('ajax_password').value;
                    const confirmation = document.getElementById('ajax-password-confirmation').value;

                    if (password && confirmation && password !== confirmation) {
                        this.passwordError = '<?= $escaper->escapeJs(__('Passwords do not match.')) ?>';
                    }

                    this.checkFormValidity();
                },

                validateBeforeSubmit() {
                    // Validate passwords
                    const password = document.getElementById('ajax_password').value;
                    const confirmation = document.getElementById('ajax-password-confirmation').value;
                    if (password !== confirmation) {
                        this.passwordError = '<?= $escaper->escapeJs(__('Passwords do not match.')) ?>';
                    } else {
                        this.passwordError = '';
                    }

                    // Validate mobile number
                    const mobileNumber = document.getElementById('mobile_number').value;
                    if (mobileNumber && !mobileNumber.match(/^(?:\+614|04)\d{8}$/)) {
                        this.mobileNumberError = '<?= $escaper->escapeJs(__('Please enter a valid Australian mobile number (format: 04XXXXXXXX or +614XXXXXXXX)')) ?>';
                    } else {
                        this.mobileNumberError = '';
                    }

                    this.checkFormValidity();
                    return this.formValid;
                },

                submitForm() {
                    if (!this.validateBeforeSubmit()) {
                        return;
                    }

                    this.loading = true;
                    this.errorMessage = '';
                    this.successMessage = '';

                    const formData = new FormData(document.getElementById('<?= $escaper->escapeJs($formId) ?>'));
                    const formObject = {};
                    formData.forEach((value, key) => {
                        formObject[key] = value;
                    });

                    fetch('<?= $escaper->escapeJs($block->getUrl('customer/ajax/register')); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify(formObject)
                    })
                        .then(response => response.json())
                        .then(data => {
                            this.loading = false;
                            if (data.errors) {
                                this.errorMessage = data.message;
                            } else {
                                this.successMessage = data.message;
                                document.getElementById('<?= $escaper->escapeJs($formId) ?>').reset();

                                // After successful registration, reload the page or redirect if needed
                                if (data.needConfirm) {
                                    // If confirmation is required, don't auto-redirect
                                    console.log('Account requires confirmation');
                                } else if (data.redirectUrl) {
                                    // Redirect to the provided URL
                                    setTimeout(() => {
                                        window.location.href = data.redirectUrl;
                                    }, 2000);
                                } else {
                                    // Default fallback to reload the current page
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 2000);
                                }
                            }
                        })
                        .catch(error => {
                            this.loading = false;
                            this.errorMessage = '<?= $escaper->escapeJs(__('An error occurred. Please try again later.')) ?>';
                            console.error('Registration error:', error);
                        });
                }
            };
        }
    </script>
</div>
