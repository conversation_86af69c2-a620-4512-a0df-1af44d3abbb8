<?php
/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \Magento\Framework\Escaper $escaper
 */
?>
<div id="login-register-popup"
     x-data="{ open: false }"
     x-show="$store.loginPopup.open"
     x-cloak
     class="fixed inset-0 z-50 overflow-y-auto"
     @keydown.escape.window="$store.loginPopup.hide()"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">

    <div class="flex items-start md:items-center justify-center min-h-screen">
        <!-- Overlay -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
             @click="$store.loginPopup.hide()"></div>

        <!-- Popup content -->
        <div class="bg-white overflow-hidden w-full md:max-w-md min-h-[calc(100vh-7rem)] md:min-h-0 top-[7rem] md:top-0 z-10 relative"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-4"
             x-transition:enter-end="opacity-100 transform translate-y-0">

             <div class="bg-white flex items-center justify-center min-h-header-mobile shadow-header">
                <h2 class="text-lg font-semibold">mycar Account</h2>
                <!-- Close button -->
                <button type="button"
                        class="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
                        @click="$store.loginPopup.hide()">
                        
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M15.625 4.375L4.375 15.625" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M15.625 15.625L4.375 4.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                </button>
            </div>

            <!-- Login/Register Content -->
            <?= $block->getChildHtml('customer.login.register.form') ?>
        </div>
    </div>
</div>

<script>
// Initialize the Alpine store as early as possible
document.addEventListener('DOMContentLoaded', function() {
    // Create a global function immediately, before Alpine initializes
    window.showLoginRegisterPopup = function() {
        if (Alpine && Alpine.store) {
            Alpine.store('loginPopup').show();
        }
    };

    // Create a global function for showing forgot password popup
    window.showForgotPassword = function() {
        console.log('Forgot password clicked from global function');
        if (Alpine && Alpine.store) {
            Alpine.store('loginPopup').hide();
            // Add a short delay to ensure transitions work correctly
            setTimeout(() => {
                Alpine.store('forgotPasswordPopup').show();
            }, 100);
        } else {
            console.log('Alpine or Alpine.store not available');
        }
    };
});

// Use alpine:init for Alpine component registration
document.addEventListener('alpine:init', () => {
    // Create a global Alpine store for the login popup
    Alpine.store('loginPopup', {
        open: false,
        toggle() {
            this.open = !this.open;
        },
        show() {
            this.open = true;
        },
        hide() {
            this.open = false;
        }
    });

    // Create a global Alpine store for the forgot password popup
    Alpine.store('forgotPasswordPopup', {
        open: false,
        toggle() {
            this.open = !this.open;
        },
        show() {
            this.open = true;
        },
        hide() {
            this.open = false;
        }
    });
});
</script>
