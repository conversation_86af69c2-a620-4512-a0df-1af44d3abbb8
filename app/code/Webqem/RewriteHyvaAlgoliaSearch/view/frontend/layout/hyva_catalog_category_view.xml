<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <attribute name="class" value="page-with-filter"/>
        <referenceContainer name="sidebar.main">
            <referenceBlock name="catalog.leftnav" remove="false"/>
            <referenceBlock name="category.products.list" remove="false"/>
        </referenceContainer>
        <referenceContainer name="content">
            <block name="search.result.list.js.compare" template="Magento_Catalog::product/list/js/compare.phtml"/>
            <block name="search.result.list.js.wishlist" template="Magento_Catalog::product/list/js/wishlist.phtml"/>
        </referenceContainer>
    </body>
</page>
