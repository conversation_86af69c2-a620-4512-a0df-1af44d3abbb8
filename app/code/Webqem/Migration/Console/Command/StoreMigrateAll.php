<?php

namespace Webqem\Migration\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Webqem\Migration\Model\DbMoto\Api;
use Magento\Framework\App\State;

class StoreMigrateAll extends Command
{
    /** @var Api */
    protected $dbMotoApi;

    /** @var State */
    protected $state;

    const PAGE = 'page';
    const LIMIT = 'limit';

    /**
     * @param Api $dbMotoApi
     * @param State $state
     * @param string|null $name
     */
    public function __construct(Api $dbMotoApi, State $state, ?string $name = null)
    {
        $this->dbMotoApi = $dbMotoApi;
        $this->state = $state;
        parent::__construct($name);
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $options = [
            new InputOption(
                self::PAGE,
                null,
                InputOption::VALUE_OPTIONAL,
                'Page'
            ),
            new InputOption(
                self::LIMIT,
                null,
                InputOption::VALUE_OPTIONAL,
                'Limit'
            )
        ];

        $this->setName('store:migrateall');
        $this->setDescription('Migrate all stores from DB Moto to Magento with options --page, --limit')->setDefinition($options);;
        parent::configure();
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $exitCode = 0;
        $output->writeln("Migrating stores ...");
        try {
            $page = $input->getOption(self::PAGE);
            $limit = $input->getOption(self::LIMIT);

            // Validation
            if (($page != null && !is_numeric($page)) || ($limit != null && !is_numeric($limit))) {
                $output->writeln("Please enter correct page/limit numbers");
                return $exitCode;
            }

            // Set area code
            $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_FRONTEND);

            // Migrate stores
            $normalStoresUrl = $this->dbMotoApi->getApiUrl() . 'api/v1/stores';
            $linkStoreUrl = $this->dbMotoApi->getApiUrl() . 'api/v1/linkstores';
            $xApiKey = $this->dbMotoApi->getApiKey();
            $this->dbMotoApi->getAllStoreData($normalStoresUrl, $xApiKey, $page, $limit);
            $this->dbMotoApi->getAllStoreData($linkStoreUrl, $xApiKey, $page, $limit);
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
        }
        $output->writeln("Done! See the log at var/log/product_migration.log");
        return $exitCode;
    }
}
