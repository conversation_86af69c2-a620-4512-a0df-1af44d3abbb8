<?php

namespace Webqem\Migration\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Webqem\Migration\Model\DbMoto\Api;
use Webqem\Migration\Model\ImportStores;
use Magento\Framework\App\State;
use Webqem\Migration\Logger\Logger as MycarStoreLogger;

class LinkStoreMigrateOne extends Command
{
    /** @var Api */
    protected $dbMotoApi;

    /** @var State */
    protected $state;

    /** @var ImportStores */
    protected $importStores;

    /** @var MycarStoreLogger */
    protected $mycarStoreLogger;

    const STORE_CODE = 'storecode';


    /**
     * @param Api $dbMotoApi
     * @param ImportStores $importStores
     * @param MycarStoreLogger $mycarStoreLogger
     * @param State $state
     * @param string|null $name
     */
    public function __construct(Api $dbMotoApi, ImportStores $importStores, MycarStoreLogger $mycarStoreLogger, State $state, ?string $name = null)
    {
        $this->dbMotoApi = $dbMotoApi;
        $this->importStores = $importStores;
        $this->mycarStoreLogger = $mycarStoreLogger;
        $this->state = $state;
        parent::__construct($name);
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $options = [
            new InputOption(
                self::STORE_CODE,
                null,
                InputOption::VALUE_REQUIRED,
                'Store Code'
            )
        ];

        $this->setName('linkstore:migrateone');
        $this->setDescription('Migrate a Link Store from DB Moto to Magento with option --storecode')
            ->setDefinition($options);
        parent::configure();
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $exitCode = 0;
        if ($storeCode = $input->getOption(self::STORE_CODE)) {
            $output->writeln("Migrating Link Store {$storeCode}: ...");
            try {
                // Set area code
                $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_FRONTEND);

                // Get Store By Store Code
                $store = $this->dbMotoApi->getLinkStoreDataByCode($storeCode);

                if ($store != null) {
                    // Get Features and Recommendation
                    $this->importStores->processLinkStoreResponseData($store);
                } else {
                    $output->writeln("Link Store Code {$storeCode} not found!");
                    $this->mycarStoreLogger->info("Link Store Code {$storeCode} not found!");
                }
            } catch (\Exception $e) {
                $output->writeln($e->getMessage());
            }
            $output->writeln("Done! See the log at var/log/store_migration.log");
        } else {
            $output->writeln("Please enter Link Store Code!");
        }
        return $exitCode;
    }
}
