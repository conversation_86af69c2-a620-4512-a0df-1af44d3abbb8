<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="migrate_all_products" xsi:type="object">Webqem\Migration\Console\Command\MigrateAll</item>
                <item name="migrate_one_product" xsi:type="object">Webqem\Migration\Console\Command\MigrateOne</item>
                <item name="migrate_all_stores" xsi:type="object">Webqem\Migration\Console\Command\StoreMigrateAll</item>
                <item name="migrate_one_store" xsi:type="object">Webqem\Migration\Console\Command\StoreMigrateOne</item>
                <item name="migrate_one_linkstore" xsi:type="object">Webqem\Migration\Console\Command\LinkStoreMigrateOne</item>
            </argument>
        </arguments>
    </type>

    <!-- Product Migration Logger -->
    <virtualType name="Webqem\Migration\Logger\ProductMigrationDebugger" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/product_migration.log</argument>
        </arguments>
    </virtualType>
    <virtualType name="Webqem\Migration\Logger\ProductMigrationLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Webqem\Migration\Logger\ProductMigrationDebugger</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Webqem\Migration\Model\DbMoto\Api">
        <arguments>
            <argument name="logger" xsi:type="object">Webqem\Migration\Logger\ProductMigrationLogger</argument>
        </arguments>
    </type>
    <type name="Webqem\Migration\Model\ImportProduct">
        <arguments>
            <argument name="logger" xsi:type="object">Webqem\Migration\Logger\ProductMigrationLogger</argument>
        </arguments>
    </type>
    <type name="Webqem\Migration\Cron\UpdateProducts">
        <arguments>
            <argument name="logger" xsi:type="object">Webqem\Migration\Logger\ProductMigrationLogger</argument>
        </arguments>
    </type>
    <type name="Webqem\Migration\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Webqem\Migration\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">store_migration.log</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">Webqem\Migration\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
</config>
