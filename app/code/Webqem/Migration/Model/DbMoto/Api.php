<?php

namespace Webqem\Migration\Model\DbMoto;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;
use Webqem\Migration\Helper\Data;
use Webqem\Migration\Logger\Logger as MycarStoreLogger;
use Webqem\Migration\Model\ImportProduct;
use Webqem\Migration\Model\ImportStores;

class Api
{
    public const DBMOTO_API_URL_PATH = 'migration/dbmoto/api_url';
    public const DBMOTO_API_KEY_PATH = 'migration/dbmoto/api_key';

    /** @var ScopeConfigInterface */
    protected $scopeConfig;

    /** @var Curl */
    protected $curl;

    /** @var LoggerInterface */
    protected $logger;

    /** @var Data */
    protected $helperData;

    /** @var ImportProduct */
    protected $importProduct;

    /** @var ResourceConnection */
    protected $resourceConnection;

    /** @var ImportStores */
    protected $importStores;

    /** @var TimezoneInterface */
    protected $timezone;

    /** @var MycarStoreLogger */
    protected $mycarStoreLogger;

    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param Curl $curl
     * @param LoggerInterface $logger
     * @param Data $helperData
     * @param ImportProduct $importProduct
     * @param ResourceConnection $resourceConnection
     * @param ImportStores $importStores
     * @param TimezoneInterface $timezone
     * @param MycarStoreLogger $mycarStoreLogger
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        Curl $curl,
        LoggerInterface $logger,
        Data $helperData,
        ImportProduct $importProduct,
        ResourceConnection $resourceConnection,
        ImportStores $importStores,
        TimezoneInterface $timezone,
        MycarStoreLogger $mycarStoreLogger
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->curl = $curl;
        $this->logger = $logger;
        $this->helperData = $helperData;
        $this->importProduct = $importProduct;
        $this->resourceConnection = $resourceConnection;
        $this->importStores = $importStores;
        $this->timezone = $timezone;
        $this->mycarStoreLogger = $mycarStoreLogger;
    }

    /**
     * Get API URL
     *
     * @return mixed
     */
    public function getApiUrl()
    {
        return $this->scopeConfig->getValue(self::DBMOTO_API_URL_PATH);
    }

    /**
     * Get API Key
     *
     * @return mixed
     */
    public function getApiKey()
    {
        return $this->scopeConfig->getValue(self::DBMOTO_API_KEY_PATH);
    }

    /**
     * Migrate Products
     *
     * @param $page
     * @param $limit
     * @return bool
     */
    public function migrateProducts($page = null, $limit = null)
    {
        try {
            // Get Features and Recommendation
            $features = $this->getProductFeatures();
            $recommendations = $this->getRecommendation();

            $this->logger->info('Getting products ...');
            $apiUrl = $this->getApiUrl() . 'api/v1/products';
            $apiKey = $this->getApiKey();

            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            // Try a loop
            $curPage = $page ?? 1;
            $limit = $limit ?? 100;
            $continue = true;
            while ($continue) {
                $param = ['page' => $curPage, 'limit' => $limit];
                $this->logger->info("page: $curPage - limit: $limit");
                $curlUrl = $apiUrl . '?' . http_build_query($param);
                $this->curl->setHeaders($headers);
                $this->curl->get($curlUrl);
                if ($this->curl->getStatus() == 200) {
                    $response = $this->curl->getBody();
                    $responseArr = json_decode($response, true);
                    $productData = $responseArr['data'];
                    $mergedData = [];
                    foreach ($productData as $product) {
                        $mergedData[] = $this->prepareDataForProduct($product, $features, $recommendations);
                    }
                    // Import Product
                    $this->importProduct->importProduct($mergedData);
                    if ($responseArr['next_page_url'] == null) {
                        $continue = false;
                    }
                } else {
                    $continue = false;
                    $this->logger->info('Something wrong with the connection, please try again!');
                }

                // Exist with debugging flag
                if ($page != null) {
                    $continue = false;
                }
                $curPage++;
            }
            return true;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return false;
    }

    /**
     * Get Product Data By Sku
     *
     * @param $sku
     * @return mixed|null
     */
    public function getProductDataBySku($sku)
    {
        try {
            $this->logger->info("Getting SKU {$sku} ...");
            $apiUrl = $this->getApiUrl() . 'api/v1/product/' . $sku;
            $apiKey = $this->getApiKey();
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];
            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return null;
    }

    /**
     * Get Updated Products
     *
     * @return array
     */
    public function getUpdatedProduct()
    {
        try {
            $this->logger->info("Getting updated products ...");
            $lastRun = $this->getLastRun('mycar_update_products');
            $this->logger->info("Last run: $lastRun");
            $param = ['last_run' => $lastRun];

            $apiUrl = $this->getApiUrl() . 'api/v1/getUpdatedProducts' . '?' . http_build_query($param);
            $apiKey = $this->getApiKey();
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];
            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return [];
    }

    /**
     * Prepare product data
     *
     * @param $product
     * @param $features
     * @param $recommendations
     * @return array
     * @throws LocalizedException
     */
    public function prepareDataForProduct($product, $features, $recommendations)
    {
        $sku = $product['MMITNO'];
        $this->logger->info("Formatting data for: {$sku}");

        // Merge product with features
        $product = $this->mergeProductWithFeatures($product, $features);

        // Merge product with recommendation
        $product = $this->mergeProductWithRecommendations($product, $recommendations);

        // Format data to import
        $formattedProduct = $this->helperData->formatData($product);
        return array_merge($formattedProduct, $product);
    }

    /**
     * Get Product Features
     *
     * @return array
     */
    public function getProductFeatures()
    {
        $result = [];
        try {
            $this->logger->info('Getting product features ...');
            $apiUrl = $this->getApiUrl() . 'api/v1/features';
            $apiKey = $this->getApiKey();

            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);

            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return [];
        }
        return $result;
    }

    /**
     * Get Product Recommendation
     *
     * @return array
     */
    public function getRecommendation()
    {
        $result = [];
        try {
            $this->logger->info('Getting recommendation ...');
            $apiUrl = $this->getApiUrl() . 'api/v1/recommendations';
            $apiKey = $this->getApiKey();

            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);

            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return [];
        }
        return $result;
    }

    /**
     * Merge Product with Feature
     *
     * @param $product
     * @param $features
     * @return array
     */
    public function mergeProductWithFeatures($product, $features)
    {
        $mmdim2 = $product['MMDIM2'];
        foreach ($features as $feature) {
            if ($feature['TYDIM2'] == $mmdim2) {
                return array_merge($product, $feature);
            }
        }
        return $product;
    }


    /**
     * Merge Product with Recommendation
     *
     * @param $product
     * @param $recommendations
     * @return array
     */
    public function mergeProductWithRecommendations($product, $recommendations)
    {
        $mmprgp = $product['MMPRGP'];
        foreach ($recommendations as $recommendation) {
            if ($recommendation['TYPRGP'] == $mmprgp) {
                return array_merge($product, $recommendation);
            }
        }
        return $product;
    }

    /**
     * Get last run
     *
     * @param $jobCode
     * @return string
     */
    public function getLastRun($jobCode)
    {
        $cronScheduleTable = $this->resourceConnection->getTableName('cron_schedule');
        $resource = $this->resourceConnection->getConnection();
        $query = $resource->select()->from($cronScheduleTable, ['executed_at'])
            ->where('job_code = ?', $jobCode)
            ->where("status = 'success'")
            ->order('schedule_id desc')
            ->limit(1);
        $lastRun = $resource->fetchOne($query);
        if ($lastRun) {
            return $this->timezone->date($lastRun)->format('Y-m-d H:i:s');
        }
        return '';
    }

    /**
     * Get Store Data From DB Moto
     * @param $url
     * @return array
     */
    public function getAllStoreData($url, $xApiKey, $page = null, $limit = null)
    {
        try {
            $storeData = [];
            $curPage = $page ?? 1;
            $limit = $limit ?? 100;
            $continue = true;
            while ($continue) {
                $param = ['page' => $curPage, 'limit' => $limit];
                $this->mycarStoreLogger->info("page: $curPage - limit: $limit");
                $curlUrl = $url . '?' . http_build_query($param);
                $response = $this->importStores->getApiRequest($curlUrl, $xApiKey);
                if ($response['code'] == 200) {
                    $responseStoreData = $response['data']['data'];
                    if (str_contains($curlUrl, 'api/v1/stores')) {
                        $this->importStores->processResponseStoreData($responseStoreData);
                    } else if (str_contains($url, 'api/v1/linkstores')) {
                        $this->importStores->processLinkStoreResponseData($responseStoreData);
                    }
                    if ($response['data']['next_page_url'] == null) {
                        $continue = false;
                    }
                } else {
                    $continue = false;
                    $this->mycarStoreLogger->info('Something wrong with the connection, please try again!');
                }
                // Exist with debugging flag
                if ($page != null) {
                    $continue = false;
                }

                $curPage++;
            }

            return $storeData;
        } catch (\Exception $e) {
            $this->mycarStoreLogger->error('Error while getting all stores data. Exception message: ' . $e->getMessage());
        }
        return [];
    }

    /**
     * Get Updated Products
     *
     * @return array
     */
    public function getUpdatedStores()
    {
        try {
            $this->mycarStoreLogger->info("Getting updated stores ...");
            $lastRun = $this->getLastRun('mycar_store_integration_cron');
            $this->mycarStoreLogger->info("Last run: $lastRun");
            $param = ['last_run' => $lastRun];

            $apiUrl = $this->getApiUrl() . 'api/v1/getUpdatedStores' . '?' . http_build_query($param);
            $apiKey = $this->getApiKey();
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];
            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return [];
    }

    /**
     * Get Normal Store Data By Store Code
     *
     * @param $storeCode
     * @return mixed|null
     */
    public function getStoreDataByCode($storeCode)
    {
        try {
            $this->mycarStoreLogger->info("Getting Store Code {$storeCode} ...");
            $apiUrl = $this->getApiUrl() . 'api/v1/stores/' . $storeCode;
            $apiKey = $this->getApiKey();
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];
            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->mycarStoreLogger->error($e->getMessage());
        }
        return null;
    }

    /**
     * Get Link Store Data By Store Code
     *
     * @param $storeCode
     * @return mixed|null
     */
    public function getLinkStoreDataByCode($storeCode)
    {
        try {
            $this->mycarStoreLogger->info("Getting Link Store Code {$storeCode} ...");
            $apiUrl = $this->getApiUrl() . 'api/v1/linkstores/' . $storeCode;
            $apiKey = $this->getApiKey();
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];
            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                return json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->mycarStoreLogger->error($e->getMessage());
        }
        return null;
    }
}
