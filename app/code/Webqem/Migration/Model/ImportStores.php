<?php

namespace Webqem\Migration\Model;

use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\HTTP\Client\CurlFactory;
use Magento\Framework\Serialize\SerializerInterface;
use Webqem\Migration\Logger\Logger as MycarStoreLogger;
use Webqem\Stores\Api\StoreRepositoryInterface;
use Webqem\Stores\Model\StoreFactory;
use Webqem\Stores\Model\StoreMobilePostcodeFactory;
use Webqem\Stores\Model\StoreMobilePostcodes\Builder as StoreMobilePostcodeBuilder;
use Webqem\Stores\Model\StoreOpeningTime\Builder as StoreOpeningTimeBuilder;
use Webqem\Stores\Model\StoreOpeningTimeFactory;
use Webqem\Stores\Model\StoreServices\Builder as StoreServicesBuilder;
use Webqem\Stores\Model\StoreServicesFactory;
use Webqem\Stores\Model\StoreType\Builder as StoreTypeBuilder;
use Webqem\Stores\Model\StoreTypeFactory;

class ImportStores
{
    public const STATE_MAP_VALUE = [
        'VIC' => 607,
        'SA' => 609,
        'WA' => 611,
        'NSW' => 606,
        'TAS' => 610,
        'QLD' => 608,
        'ACT' => 605,
        'NT' => 612
    ];

    /**
     * @var StoreRepositoryInterface
     */
    protected $storeRepository;

    /**
     * @var StoreFactory
     */
    protected $storeFactory;

    /**
     * @var StoreTypeFactory
     */
    protected $storeTypeFactory;

    /**
     * @var MycarStoreLogger
     */
    protected $mycarStoresLogger;

    /**
     * @var StoreMobilePostcodeBuilder
     */
    protected $storeMobilePostcodeBuilder;

    /**
     * @var StoreOpeningTimeBuilder
     */
    protected $storeOpeningTimeBuilder;

    /**
     * @var StoreServicesBuilder
     */
    protected $storeServicesBuilder;

    /**
     * @var StoreTypeBuilder
     */
    protected $storeTypeBuilder;

    /**
     * @var StoreMobilePostcodeFactory
     */
    protected $storeMobilePostcodeFactory;

    /**
     * @var StoreOpeningTimeFactory
     */
    protected $storeOpeningTimeFactory;

    /**
     * @var StoreServicesFactory
     */
    protected $storeServicesFactory;

    /**
     * @var CurlFactory
     */
    protected $curlFactory;

    /**
     * @var SerializerInterface
     */
    protected $serializer;

    /**
     * @param StoreRepositoryInterface $storeRepository
     * @param StoreFactory $storeFactory
     * @param StoreTypeFactory $storeTypeFactory
     * @param MycarStoreLogger $mycarStoresLogger
     * @param StoreMobilePostcodeBuilder $storeMobilePostcodeBuilder
     * @param StoreOpeningTimeBuilder $storeOpeningTimeBuilder
     * @param StoreServicesBuilder $storeServicesBuilder
     * @param StoreTypeBuilder $storeTypeBuilder
     * @param StoreMobilePostcodeFactory $storeMobilePostcodeFactory
     * @param StoreOpeningTimeFactory $storeOpeningTimeFactory
     * @param StoreServicesFactory $storeServicesFactory
     * @param CurlFactory $curlFactory
     * @param SerializerInterface $serializer
     */
    public function __construct(
        StoreRepositoryInterface $storeRepository,
        StoreFactory $storeFactory,
        StoreTypeFactory $storeTypeFactory,
        MycarStoreLogger $mycarStoresLogger,
        StoreMobilePostcodeBuilder $storeMobilePostcodeBuilder,
        StoreOpeningTimeBuilder $storeOpeningTimeBuilder,
        StoreServicesBuilder $storeServicesBuilder,
        StoreTypeBuilder $storeTypeBuilder,
        StoreMobilePostcodeFactory $storeMobilePostcodeFactory,
        StoreOpeningTimeFactory $storeOpeningTimeFactory,
        StoreServicesFactory $storeServicesFactory,
        CurlFactory $curlFactory,
        SerializerInterface $serializer
    )
    {
        $this->storeRepository = $storeRepository;
        $this->storeFactory = $storeFactory;
        $this->storeTypeFactory = $storeTypeFactory;
        $this->mycarStoresLogger = $mycarStoresLogger;
        $this->storeMobilePostcodeBuilder = $storeMobilePostcodeBuilder;
        $this->storeOpeningTimeBuilder = $storeOpeningTimeBuilder;
        $this->storeServicesBuilder = $storeServicesBuilder;
        $this->storeTypeBuilder = $storeTypeBuilder;
        $this->storeMobilePostcodeFactory = $storeMobilePostcodeFactory;
        $this->storeOpeningTimeFactory = $storeOpeningTimeFactory;
        $this->storeServicesFactory = $storeServicesFactory;
        $this->curlFactory = $curlFactory;
        $this->serializer = $serializer;
    }

    /**
     * Process response store data from DB moto, create store in Magento
     * @param $allStoreData
     * @return void
     */
    public function processResponseStoreData($allStoreData)
    {
        if (count($allStoreData) < 1) {
            $this->mycarStoresLogger->info('No updated stores were found');
            return;
        }
        $errors = [];
        $successCount = 0;
        $errorCount = 0;
        $this->mycarStoresLogger->info("Processing store data ...");
        foreach ($allStoreData as $storeData) {
            $storeUpdate = false;
            $this->mycarStoresLogger->info("Formatting data for store code: " . $storeData['OKWHLO'] . ' Name: ' .  trim($storeData['OKCUNM']));
            $newStore = null;
            if ($this->storeRepository->getByStoreCode($storeData['OKWHLO'])->getId()) {
                $storeUpdate = true;
            }
            if ((int) $storeData['B6STCE'] == 1) {
                $store_type = 'Shopping Centre';
            } elseif ((int) $storeData['B6STCE'] == 2) {
                $store_type = 'Service Station';
            } elseif ((int) $storeData['B6STCE'] == 3) {
                $store_type = 'Freestanding';
            } elseif ((int) $storeData['B6STCE'] == 9) {
                $store_type = 'Closed';
            }
            $storeAddress = $storeData['OKCUA1'] . ' ' . $storeData['OKCUA2'] . ' ' . $storeData['OKCUA3'] . ' ' . $storeData['OKCUA4'] . ' ' . $storeData['OKCORG'] . ' ' . $storeData['OKPONO'];
            $store_manager = trim($storeData['SCSNL1']);
            $storeInfo = [
                'enabled' => trim($storeData['OKSTAT'] ?? '') == 20 ? 1 : 0,
                'name' => trim($storeData['OKCUNM'] ?? ''), // OKCUNM = Store Name
                'is_link_store' => false,
                'is_dark_store' => 0,
                'is_mobile' => str_contains(trim($storeData['OKCUNM'] ?? ''), 'Mobile') ? 1 : 0,
                'lat' => $storeData['TMLATD'], // TMLATD = Store Latitude
                'long' => $storeData['TMLOND'], // TMLOND = Store Longtitude
                'state' => self::STATE_MAP_VALUE[trim($storeData['OKCORG'])] ?? '', // OKCORG = Store State
                'store_type' => trim($store_type),
                'phone' => trim($storeData['OKPHNO'] ?? ''), // OKPHNO = Store Phone Number
                'address' => $storeAddress,
                'postcode' => strval($storeData['OKPONO']), // OKPONO = Postcode
                'store_code' => trim($storeData['OKWHLO'] ?? ''), // OKWHLO = Store Code
                'has_waiting_room' => trim($storeData['ZSSPWS'] ?? '') == 1 ? 1 : 0,
                'has_key_dropoff' => trim($storeData['ZSSPAK'] ?? '') == 1 ? 1 : 0,
                'store_manager' => $store_manager,
                'driving_instructions' => $storeData['ZSSPGT'] != '' ? $storeData['ZSSPGT'] : '', // ZSSPGT = Private Driving Instructions
                'public_transport_instructions' => $storeData['ZSSPPT'] != '' ? $storeData['ZSSPPT'] : '', // ZSSPPT = Public Driving Instructions,
                'ev' => 0,
                'ev_charging' => 0
            ];
            $newStore = $this->storeFactory->create();
            $newStore->setData($storeInfo);
            $this->resolveStoreOpeningTimeData($newStore, $storeData);
            $this->resolveStoreTypeData($newStore, $store_type);
            try {
                $method = (!$storeUpdate) ? 'save' : 'update';
                $this->storeRepository->$method($newStore);
                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $errors[] = "Unable to create/update store code: " .  $storeData['OKWHLO'] . " . Error message: " . $e->getMessage();
            }
        }
        $processResult =
            [
                'Total Stores Processed' => count($allStoreData),
                'Number of stores created/updated successfully' => $successCount,
                'Error Count' => $errorCount,
                'Error Details' => $errors
            ];
        $this->mycarStoresLogger->info(print_r($processResult, true));
    }

    public function processLinkStoreResponseData($allLinkStoreData)
    {
        $errors = [];
        $successCount = 0;
        $errorCount = 0;
        $this->mycarStoresLogger->info("Processing link store data ...");
        foreach ($allLinkStoreData as $storeData) {
            $storeUpdate = false;
            $this->mycarStoresLogger->info("Formatting data for store code: " . $storeData['TMWHLO'] . ' Name: ' .  trim($storeData['LKSTNM']));
            $newStore = null;
            if ($this->storeRepository->getByStoreCode($storeData['TMWHLO'])->getId()) {
                $storeUpdate = true;
            }
            $store_type = 'Shopping Centre';
            $storeAddress = $storeData['LKSTNM'] . ' ' . $storeData['LKSTAD'] . ' ' . $storeData['LKSTSB'] . ' ' . $storeData['LKSTST'] . ' ' . $storeData['LKSTPN'];
            $store_manager = $storeData['LKSTMN'];
            $darkStoreId = $this->resolveDarkStoreData($storeData);
            $storeInfo = [
                'enabled' => trim($storeData['OKSTAT'] ?? '') == 20 ? 1 : 0,
                'name' => trim($storeData['LKSTNM'] ?? ''), // LKSTNM = Link Store Name
                'is_link_store' => true,
                'dark_store_id' => $darkStoreId,
                'lat' => $storeData['TMLATD'],
                'long' => $storeData['TMLOND'],
                'state' => self::STATE_MAP_VALUE[trim($storeData['LKSTST'])] ?? '', // LKSTST = Store State
                'type' => trim($store_type),
                'phone' => trim($results['LKSTPH'] ?? ''), // LKSTPH = Store Phone Number
                'address' => $storeAddress,
                'postcode' => (string)$storeData['LKSTPN'], // LKSTPN = Postcode
                'store_code' => trim($storeData['LKWHLO'] ?? ''), // LKWHLO = Store Code
                'has_waiting_room' => trim($storeData['LKSWRS'] ?? '') == 1 ? 1 : 0,
                'has_key_dropoff' => trim($storeData['LKSAKD'] ?? '') == 1 ? 1 : 0,
                'store_manager' => $store_manager,
                'driving_instructions' => $storeData['LKSHTG'] != '' ? $storeData['LKSHTG'] : '', // LKSHTG + LKSHTG = Private Driving Instructions
                'public_transport_instructions' => $storeData['LKSPTO'] != '' ? $storeData['LKSPTO'] : '', // LKSPTO + LKSPTO = Public Driving Instructions
                'ev' => 0,
                'ev_charging' => 0
            ];
            $newStore = $this->storeFactory->create();
            $newStore->setData($storeInfo);
            $this->resolveStoreOpeningTimeData($newStore, $storeData);
            $this->resolveStoreTypeData($newStore, $store_type);
            try {
                $method = (!$storeUpdate) ? 'save' : 'update';
                $this->storeRepository->$method($newStore);
                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $errors[] = "Unable to create/update link store code: " .  $storeData['TMWHLO'] . " . Error message: " . $e->getMessage();
            }
        }
        $processResult =
            [
                'Total Stores Processed' => count($allLinkStoreData),
                'Number of stores created successfully' => $successCount,
                'Error Count' => $errorCount,
                'Error Details' => $errors
            ];
        $this->mycarStoresLogger->info(print_r($processResult, true));
    }

    public function resolveStoreTypeData($store, $store_type)
    {
        $storeType = [
            'type' => $store_type
        ];
        $record = $this->storeTypeBuilder->setData($storeType)->build($this->storeTypeFactory->create());
        $extensionAttributes = $store->getExtensionAttributes();
        $extensionAttributes->setType($record);
    }

    public function resolveStoreOpeningTimeData($store, $storeData)
    {
        $timeData = [
            [
                'day' => 'Monday',
                'opening_time' => trim($storeData['TMMONO'] ?? '') != '' && (int)$storeData['TMMONO'] != 0 ? $this->getFormattedTime($storeData['TMMONO']) : "Closed",
                'closing_time' => trim($storeData['TMMONC'] ?? '') != '' && (int)$storeData['TMMONC'] != 0 ? $this->getFormattedTime($storeData['TMMONC']) : "Closed",
            ],
            [
                'day' => 'Tuesday',
                'opening_time' => trim($storeData['TMTUEO'] ?? '') != '' && (int)$storeData['TMTUEO'] != 0 ? $this->getFormattedTime($storeData['TMTUEO']) : "Closed",
                'closing_time' => trim($storeData['TMTUEC'] ?? '') != '' && (int)$storeData['TMTUEC'] != 0 ? $this->getFormattedTime($storeData['TMTUEC']) : "Closed",
            ],
            [
                'day' => 'Wednesday',
                'opening_time' => trim($storeData['TMWEDO'] ?? '') != '' && (int)$storeData['TMWEDO'] != 0 ? $this->getFormattedTime($storeData['TMWEDO']) : "Closed",
                'closing_time' => trim($storeData['TMWEDC'] ?? '') != '' && (int)$storeData['TMWEDC'] != 0 ? $this->getFormattedTime($storeData['TMWEDC']) : "Closed",
            ],
            [
                'day' => 'Thursday',
                'opening_time' => trim($storeData['TMTHUO'] ?? '') != '' && (int)$storeData['TMTHUO'] != 0 ? $this->getFormattedTime($storeData['TMTHUO']) : "Closed",
                'closing_time' => trim($storeData['TMTHUC'] ?? '') != '' && (int)$storeData['TMTHUC'] != 0 ? $this->getFormattedTime($storeData['TMTHUC']) : "Closed",
            ],
            [
                'day' => 'Friday',
                'opening_time' => trim($storeData['TMFRIO'] ?? '') != '' && (int)$storeData['TMFRIO'] != 0 ? $this->getFormattedTime($storeData['TMFRIO']) : "Closed",
                'closing_time' => trim($storeData['TMFRIC'] ?? '') != '' && (int)$storeData['TMFRIC'] != 0 ? $this->getFormattedTime($storeData['TMFRIC']) : "Closed",
            ],
            [
                'day' => 'Saturday',
                'opening_time' => trim($storeData['TMSATO'] ?? '') != '' && (int)$storeData['TMSATO'] != 0 ? $this->getFormattedTime($storeData['TMSATO']) : "Closed",
                'closing_time' => trim($storeData['TMSATC'] ?? '') != '' && (int)$storeData['TMSATC'] != 0 ? $this->getFormattedTime($storeData['TMSATC']) : "Closed",
            ],
            [
                'day' => 'Sunday',
                'opening_time' => trim($storeData['TMSUNO'] ?? '') != '' && (int)$storeData['TMSUNO'] != 0 ? $this->getFormattedTime($storeData['TMSUNO']) : "Closed",
                'closing_time' => trim($storeData['TMSUNC'] ?? '') != '' && (int)$storeData['TMSUNC'] != 0 ? $this->getFormattedTime($storeData['TMSUNC']) : "Closed",
            ]
        ];
        $extensionAttributes = $store->getExtensionAttributes();
        foreach($timeData as $storeOpeningTime) {
            $records[] = $this->storeOpeningTimeBuilder->setData($storeOpeningTime)->build($this->storeOpeningTimeFactory->create());
        }
        $extensionAttributes->setStoreOpeningTime($records);
        $store->setExtensionAttributes($extensionAttributes);
    }

    public function getFormattedTime($time)
    {
        $formatted_time = "Closed";

        if (trim($time ?? '') != '' && (int)$time != 0) {
            // Ensure the time string is 6 characters long, pad with leading zeros if necessary
            $time = str_pad($time, 6, "0", STR_PAD_LEFT);

            // Split the time string into hours, minutes, and seconds
            $hours = substr($time, 0, 2);
            $minutes = substr($time, 2, 2);
            $seconds = substr($time, 4, 2);

            // Construct a time string in HH:MM:SS format
            $formattedInput = $hours . ':' . $minutes . ':' . $seconds;

            // Format the time as "h:i A" (12-hour format with leading zero, uppercase AM/PM)
            $formatted_time = date('h:i A', strtotime($formattedInput));
        }

        return $formatted_time;
    }

    /**
     * @param $url
     * @return array|string
     */
    public function getApiRequest($url, $xApiKey)
    {
        try {
            if (str_contains($url, 'api/v1/stores')) {
                $this->mycarStoresLogger->info('Getting Store Data ...');
            } else if (str_contains($url, 'api/v1/linkstores')) {
                $this->mycarStoresLogger->info('Getting Link Store Data ...');
            }
            $curl = $this->curlFactory->create();
            $this->setAuthorization($curl, $xApiKey);
            $curl->get($url);
            $response = $curl->getBody();
            $responseDecoded = $this->serializer->unserialize($response);
            return [
                'code' => 200,
                'data' => $responseDecoded
            ];
        } catch (\Exception $e) {
            return [
                'code' => 400,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate authorization header for the request
     * @param $scopeType
     * @param $scopeCode
     * @return void
     */
    private function setAuthorization(Curl $curl, $xApiKey): void
    {
        $curl->addHeader('X-Api-Key', $xApiKey);
        $curl->addHeader('Content-type', 'application/json');
    }

    /**
     * @param $storeData
     * @return int|null
     */
    public function resolveDarkStoreData($storeData)
    {
        try {
            $darkStore = $this->storeRepository->getByStoreCode($storeData['ZSWHLO']);
            $darkStore->setIsDarkStore(true);
            $this->storeRepository->update($darkStore);
            return $darkStore->getId();
        } catch (\Exception $e) {
            $this->mycarStoresLogger->info($e->getMessage());
        }
        return null;
    }
}
