<?php

namespace Webqem\Migration\Cron;

use Webqem\Migration\Model\DbMoto\Api;
use Webqem\Migration\Model\ImportStores;

class StoreIntegration
{
    /**
     * @var Api
     */
    protected $dbMotoApi;

    /**
     * @var ImportStores
     */
    protected $importStores;

    /**
     * @param Api $dbMotoApi
     * @param ImportStores $importStores
     */
    public function __construct(
        Api $dbMotoApi,
        ImportStores $importStores
    ) {
        $this->dbMotoApi = $dbMotoApi;
        $this->importStores = $importStores;
    }

    /**
     * @return void
     */
    public function execute()
    {
        $updatedStoreData = $this->dbMotoApi->getUpdatedStores();
        $this->importStores->processResponseStoreData($updatedStoreData);
    }
}
