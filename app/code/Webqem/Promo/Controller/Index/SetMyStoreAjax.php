<?php
namespace Webqem\Promo\Controller\Index;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Webqem\Stores\Model\ResourceModel\Store\CollectionFactory;
use Webqem\Promo\Model\Session;
use Magento\Framework\Stdlib\CookieManagerInterface;

class SetMyStoreAjax extends Action
{
    protected $jsonFactory;

    protected $storeCollectionFactory;

    protected $mycarStoreSession;

    protected $cookieMetadataFactory;

    protected $cookieManager;

    public function __construct(
        Context $context,
        JsonFactory $jsonFactory,
        CollectionFactory $storeCollectionFactory,
        Session $mycarStoreSession,
        CookieMetadataFactory $cookieMetadataFactory,
        CookieManagerInterface $cookieManager,
    ) {
        $this->jsonFactory = $jsonFactory;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->mycarStoreSession = $mycarStoreSession;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        $this->cookieManager = $cookieManager;
        parent::__construct($context);
    }

    public function execute()
    {
        $storeId = $this->getRequest()->getParam('store_id');
        $enabledStoreIds = [];
        $enabledStoreCollection = $this->storeCollectionFactory->create()
            ->addFieldToSelect('id')
            ->addFieldToFilter('enabled', 1);
        foreach ($enabledStoreCollection as $enabledStore) {
            $enabledStoreIds[] = $enabledStore->getId();
        }
        $metadata = $this->cookieMetadataFactory->createPublicCookieMetadata()
            ->setDurationOneYear()
            ->setPath($this->mycarStoreSession->getCookiePath())
            ->setDomain($this->mycarStoreSession->getCookieDomain());
        if (in_array($storeId, $enabledStoreIds)) {
            $this->mycarStoreSession->setStoreId($storeId);
            $this->cookieManager->setPublicCookie('mycar_store_id', $storeId, $metadata);
            $response = [
                'success' => true,
            ];
        } else {
            $this->mycarStoreSession->setStoreId(null);
            $this->cookieManager->setPublicCookie('mycar_store_id', null, $metadata);
            $response = [
                'success' => false,
            ];
        }
        $result = $this->jsonFactory->create();
        return $result->setData($response);
    }
}
