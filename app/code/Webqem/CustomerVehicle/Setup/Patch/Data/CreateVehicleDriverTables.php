<?php
/**
 * Vehicle and Driver Data Patch
 *
 * @category   Module
 * @package    Company\VehicleManagement
 */
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchRevertableInterface;
use Magento\Framework\DB\Ddl\Table;

/**
 * Class CreateVehicleDriverTables
 */
class CreateVehicleDriverTables implements DataPatchInterface, PatchRevertableInterface
{
    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        $this->moduleDataSetup->startSetup();

        $this->createVehicleTable();
        $this->createDriverTable();

        $this->moduleDataSetup->endSetup();

        return $this;
    }

    /**
     * Create vehicle table
     */
    private function createVehicleTable()
    {
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('customer_vehicle');

        // Drop table if it exists
        if ($connection->isTableExists($tableName)) {
            $connection->dropTable($tableName);
        }

        $table = $connection->newTable($tableName)
            ->addColumn(
                'vehicleId',
                Table::TYPE_INTEGER,
                null,
                [
                    'identity' => true,
                    'unsigned' => true,
                    'nullable' => false,
                    'primary' => true,
                ],
                'Vehicle ID'
            )
            ->addColumn(
                'vehicle_name',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Vehicle Name'
            )
            ->addColumn(
                'rego_number',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Rego Number'
            )
            ->addColumn(
                'state',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'State'
            )
            ->addColumn(
                'make',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Make'
            )
            ->addColumn(
                'model',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Model'
            )
            ->addColumn(
                'year',
                Table::TYPE_TEXT,
                255,
                ['unsigned' => true, 'nullable' => true],
                'Year'
            )
            ->addColumn(
                'image',
                Table::TYPE_TEXT,
                255,
                ['nullable' => true],
                'Image'
            )
            ->addColumn(
                'customerId',
                Table::TYPE_INTEGER,
                null,
                ['unsigned' => true, 'nullable' => true],
                'Customer ID'
            )
            ->addColumn(
                'series',
                Table::TYPE_TEXT,
                100,
                ['nullable' => true],
                'Series'
            )
            ->addColumn(
                'pollId',
                Table::TYPE_TEXT,
                100,
                ['nullable' => true],
                'Poll ID'
            )
            ->addColumn(
                'vehicleLogicId',
                Table::TYPE_TEXT,
                100,
                ['nullable' => true],
                'Vehicle Logic ID'
            )
            ->addColumn(
                'isElectric',
                Table::TYPE_BOOLEAN,
                null,
                ['nullable' => false, 'default' => false],
                'Is Electric'
            )
            ->addIndex(
                $connection->getIndexName($tableName, ['customerId']),
                ['customerId']
            )
            ->addForeignKey(
                $connection->getForeignKeyName($tableName, 'customerId', $this->moduleDataSetup->getTable('customer_entity'), 'entity_id'),
                'customerId',
                $this->moduleDataSetup->getTable('customer_entity'),
                'entity_id',
                Table::ACTION_SET_NULL
            )
            ->setComment('Vehicle Table');

        $connection->createTable($table);
    }

    /**
     * Create driver table
     */
    private function createDriverTable()
    {
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('vehicle_driver');

        // Drop table if it exists
        if ($connection->isTableExists($tableName)) {
            $connection->dropTable($tableName);
        }

        $table = $connection->newTable($tableName)
            ->addColumn(
                'driverId',
                Table::TYPE_INTEGER,
                null,
                [
                    'identity' => true,
                    'unsigned' => true,
                    'nullable' => false,
                    'primary' => true,
                ],
                'Driver ID'
            )
            ->addColumn(
                'email',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Email'
            )
            ->addColumn(
                'firstname',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'First Name'
            )
            ->addColumn(
                'lastname',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Last Name'
            )
            ->addColumn(
                'mobileNumber',
                Table::TYPE_TEXT,
                50,
                ['nullable' => true],
                'Mobile Number'
            )
            ->addColumn(
                'relationshipToOwner',
                Table::TYPE_TEXT,
                100,
                ['nullable' => true],
                'Relationship to Owner'
            )
            ->addColumn(
                'vehicleId',
                Table::TYPE_INTEGER,
                null,
                ['unsigned' => true, 'nullable' => true],
                'Vehicle ID'
            )
            ->addIndex(
                $connection->getIndexName($tableName, ['vehicleId']),
                ['vehicleId']
            )
            ->addForeignKey(
                $connection->getForeignKeyName($tableName, 'vehicleId', 'customer_vehicle', 'vehicleId'),
                'vehicleId',
                $this->moduleDataSetup->getTable('customer_vehicle'),
                'vehicleId',
                Table::ACTION_SET_NULL
            )
            ->setComment('Driver Table');

        $connection->createTable($table);
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function revert()
    {
        $this->moduleDataSetup->startSetup();

        $connection = $this->moduleDataSetup->getConnection();
        $connection->dropTable($this->moduleDataSetup->getTable('vehicle_driver'));
        $connection->dropTable($this->moduleDataSetup->getTable('customer_vehicle'));

        $this->moduleDataSetup->endSetup();
    }
}
