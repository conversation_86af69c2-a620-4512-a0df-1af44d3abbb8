<?php
/**
 * Vehicle Collection
 */
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Model\ResourceModel\Vehicle;

use Webqem\CustomerVehicle\Model\Vehicle;
use Webqem\CustomerVehicle\Model\ResourceModel\Vehicle as ResourceModel;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'vehicleId';

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            Vehicle::class,
            ResourceModel::class
        );
    }
}
