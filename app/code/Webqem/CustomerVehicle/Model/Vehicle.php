<?php
/**
 * Vehicle Model
 */
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Model;

use Webqem\CustomerVehicle\Api\Data\VehicleInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Vehicle extends AbstractExtensibleModel implements VehicleInterface
{
    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(ResourceModel\Vehicle::class);
    }

    /**
     * @inheritDoc
     */
    public function getVehicleId(): ?int
    {
        return $this->getData(self::VEHICLE_ID) === null ? null : (int)$this->getData(self::VEHICLE_ID);
    }

    /**
     * @inheritDoc
     */
    public function setVehicleId(int $vehicleId): VehicleInterface
    {
        return $this->setData(self::VEHICLE_ID, $vehicleId);
    }

    /**
     * @inheritDoc
     */
    public function getRegoNumber(): string
    {
        return (string)$this->getData(self::REGO_NUMBER);
    }

    /**
     * @inheritDoc
     */
    public function setRegoNumber(string $rego_number): VehicleInterface
    {
        return $this->setData(self::REGO_NUMBER, $rego_number);
    }

    /**
     * @inheritDoc
     */
    public function getVehicleName(): string
    {
        return (string)$this->getData(self::VEHICLE_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setVehicleName(string $vehicle_name): VehicleInterface
    {
        return $this->setData(self::VEHICLE_NAME, $vehicle_name);
    }

    /**
     * @inheritDoc
     */
    public function getMake(): string
    {
        return (string)$this->getData(self::MAKE);
    }

    /**
     * @inheritDoc
     */
    public function setMake(string $make): VehicleInterface
    {
        return $this->setData(self::MAKE, $make);
    }

    /**
     * @inheritDoc
     */
    public function getModel(): string
    {
        return (string)$this->getData(self::MODEL);
    }

    /**
     * @inheritDoc
     */
    public function setModel(string $model): VehicleInterface
    {
        return $this->setData(self::MODEL, $model);
    }

    /**
     * @inheritDoc
     */
    public function getYear(): int
    {
        return (int)$this->getData(self::YEAR);
    }

    /**
     * @inheritDoc
     */
    public function setYear(int $year): VehicleInterface
    {
        return $this->setData(self::YEAR, $year);
    }

    /**
     * @inheritDoc
     */
    public function getImage(): ?string
    {
        return $this->getData(self::IMAGE);
    }

    /**
     * @inheritDoc
     */
    public function setImage(?string $image): VehicleInterface
    {
        return $this->setData(self::IMAGE, $image);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerId(): ?int
    {
        return $this->getData(self::CUSTOMER_ID) === null ? null : (int)$this->getData(self::CUSTOMER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerId(?int $customerId): VehicleInterface
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }

    /**
     * @inheritDoc
     */
    public function getSeries(): ?string
    {
        return $this->getData(self::SERIES);
    }

    /**
     * @inheritDoc
     */
    public function setSeries(?string $series): VehicleInterface
    {
        return $this->setData(self::SERIES, $series);
    }

    /**
     * @inheritDoc
     */
    public function getPollId(): ?string
    {
        return $this->getData(self::POLL_ID);
    }

    /**
     * @inheritDoc
     */
    public function setPollId(?string $pollId): VehicleInterface
    {
        return $this->setData(self::POLL_ID, $pollId);
    }

    /**
     * @inheritDoc
     */
    public function getVehicleLogicId(): ?string
    {
        return $this->getData(self::VEHICLE_LOGIC_ID);
    }

    /**
     * @inheritDoc
     */
    public function setVehicleLogicId(?string $vehicleLogicId): VehicleInterface
    {
        return $this->setData(self::VEHICLE_LOGIC_ID, $vehicleLogicId);
    }

    /**
     * @inheritDoc
     */
    public function isElectric(): bool
    {
        return (bool)$this->getData(self::IS_ELECTRIC);
    }

    /**
     * @inheritDoc
     */
    public function setIsElectric(bool $isElectric): VehicleInterface
    {
        return $this->setData(self::IS_ELECTRIC, $isElectric);
    }

    /**
     * Get extension attributes
     *
     * @return \Webqem\CustomerVehicle\Api\Data\VehicleExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set extension attributes
     *
     * @param \Webqem\CustomerVehicle\Api\Data\VehicleExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Webqem\CustomerVehicle\Api\Data\VehicleExtensionInterface $extensionAttributes
    )
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    public function getState(): string
    {
        return $this->getData(self::STATE);
    }

    public function setState(string $state): VehicleInterface
    {
        return $this->setData(self::STATE, $state);
    }
}
