<?php
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Model;

use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Psr\Log\LoggerInterface;

class VehicleLookupService
{
    /**
     * @var Curl
     */
    protected $curl;

    /**
     * @var Json
     */
    protected $json;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    protected $motoDbApi;

    /**
     * @param Curl $curl
     * @param Json $json
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        Curl $curl,
        Json $json,
        ScopeConfigInterface $scopeConfig,
        LoggerInterface $logger,
        \Webqem\Migration\Model\DbMoto\Api $motoDbApi
    ) {
        $this->curl = $curl;
        $this->json = $json;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
        $this->motoDbApi = $motoDbApi;
    }

    /**
     * Get Car make
     *
     * @return array
     */
    public function getCarMakes()
    {
        $result = [];
        try {
            $apiUrl = $this->motoDbApi->getApiUrl() . 'api/v1/vehicleMakes';
            $apiKey = $this->motoDbApi->getApiKey();

            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                $result = json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return [];
        }
        return $result;
    }

    public function getCarModels($makeId)
    {
        $result = [];
        try {
            $apiUrl = $this->motoDbApi->getApiUrl() . 'api/v1/vehicleModels/' . $makeId;
            $apiKey = $this->motoDbApi->getApiKey();

            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                $result = json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return [];
        }
        return $result;
    }

    public function getCarSeries($modelId, $year)
    {
        $result = [];
        try {
            $apiUrl = "{$this->motoDbApi->getApiUrl()}api/v1/vehicleSeries/{$modelId}/{$year}";
            $apiKey = $this->motoDbApi->getApiKey();

            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                $result = json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return [];
        }
        return $result;
    }

    /**
     * Lookup vehicle by registration
     *
     * @param string $regoNumber
     * @param string $state
     * @return array|null
     * @throws \Exception
     */
    public function lookupVehicle($regoNumber, $state)
    {
        $result = [];
        try {
            $apiUrl = "{$this->motoDbApi->getApiUrl()}api/v1/vehicleRegoLookup/{$regoNumber}/{$state}";
            $apiKey = $this->motoDbApi->getApiKey();
            $headers = [
                'Content-Type' => 'application/json',
                "x-api-key" => $apiKey
            ];

            $this->curl->setHeaders($headers);
            $this->curl->get($apiUrl);
            if ($this->curl->getStatus() == 200) {
                $response = $this->curl->getBody();
                $result = json_decode($response, true);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return [];
        }
        return $result;
    }
}
