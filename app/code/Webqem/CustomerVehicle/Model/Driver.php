<?php
/**
 * Driver Model
 */
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Model;

use Webqem\CustomerVehicle\Api\Data\DriverInterface;
use Magento\Framework\Model\AbstractModel;

class Driver extends AbstractModel implements DriverInterface
{
    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(ResourceModel\Driver::class);
    }

    /**
     * @inheritDoc
     */
    public function getDriverId(): ?int
    {
        return $this->getData(self::DRIVER_ID) === null ? null : (int)$this->getData(self::DRIVER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setDriverId(int $driverId): DriverInterface
    {
        return $this->setData(self::DRIVER_ID, $driverId);
    }

    /**
     * @inheritDoc
     */
    public function getFirstname(): string
    {
        return (string)$this->getData(self::FIRSTNAME);
    }

    /**
     * @inheritDoc
     */
    public function setFirstname(string $firstname): DriverInterface
    {
        return $this->setData(self::FIRSTNAME, $firstname);
    }

    /**
     * @inheritDoc
     */
    public function getLastname(): string
    {
        return (string)$this->getData(self::LASTNAME);
    }

    /**
     * @inheritDoc
     */
    public function setLastname(string $lastname): DriverInterface
    {
        return $this->setData(self::LASTNAME, $lastname);
    }

    /**
     * @inheritDoc
     */
    public function getMobileNumber(): ?string
    {
        return $this->getData(self::MOBILE_NUMBER);
    }

    /**
     * @inheritDoc
     */
    public function setMobileNumber(?string $mobileNumber): DriverInterface
    {
        return $this->setData(self::MOBILE_NUMBER, $mobileNumber);
    }

    /**
     * @inheritDoc
     */
    public function getRelationshipToOwner(): ?string
    {
        return $this->getData(self::RELATIONSHIP_TO_OWNER);
    }

    /**
     * @inheritDoc
     */
    public function setRelationshipToOwner(?string $relationshipToOwner): DriverInterface
    {
        return $this->setData(self::RELATIONSHIP_TO_OWNER, $relationshipToOwner);
    }

    /**
     * @inheritDoc
     */
    public function getVehicleId(): ?int
    {
        return $this->getData(self::VEHICLE_ID) === null ? null : (int)$this->getData(self::VEHICLE_ID);
    }

    /**
     * @inheritDoc
     */
    public function setVehicleId(?int $vehicleId): DriverInterface
    {
        return $this->setData(self::VEHICLE_ID, $vehicleId);
    }

    public function getEmail(): string
    {
        return $this->getData(self::EMAIL);
    }

    public function setEmail(string $email): DriverInterface
    {
        return $this->setData(self::EMAIL, $email);
    }
}
