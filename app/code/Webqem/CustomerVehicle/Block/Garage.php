<?php
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Webqem\CustomerVehicle\Api\VehicleRepositoryInterface;
use Magento\Customer\Model\Session;

class Garage extends Template
{
    /**
     * @var VehicleRepositoryInterface
     */
    protected $vehicleRepository;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @param Context $context
     * @param VehicleRepositoryInterface $vehicleRepository
     * @param Session $customerSession
     * @param array $data
     */
    public function __construct(
        Context $context,
        VehicleRepositoryInterface $vehicleRepository,
        Session $customerSession,
        array $data = []
    ) {
        $this->vehicleRepository = $vehicleRepository;
        $this->customerSession = $customerSession;
        parent::__construct($context, $data);
    }

    /**
     * Get customer vehicles
     *
     * @return array
     */
    public function getVehicles()
    {
        try {
            $customerId = $this->customerSession->getCustomerId();
            $searchResults = $this->vehicleRepository->getByCustomerId((int)$customerId);
            return $searchResults->getItems();
        } catch (\Exception $e) {
            $this->_logger->error($e->getMessage());
            return [];
        }
    }
}
