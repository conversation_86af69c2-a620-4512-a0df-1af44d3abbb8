<?php
namespace Webqem\CustomerVehicle\Block\Driver;

use Magento\Framework\View\Element\Template;

class Add extends Template
{
    /**
     * Get form action URL
     *
     * @return string
     */
    public function getFormAction()
    {
        return $this->getUrl('*/driver/save');
    }

    public function getVehicleId()
    {
        return $this->getRequest()->getParam('vehicle_id');
    }
}
