<?php
namespace Webqem\CustomerVehicle\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Customer\Model\Session;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory as RegionCollectionFactory;
use Webqem\CustomerVehicle\Model\ResourceModel\Vehicle as VehicleResource;
use Webqem\CustomerVehicle\Model\VehicleFactory;
use Webqem\CustomerVehicle\Model\VehicleLookupService;

class AddRegoForm extends Template
{
    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var RegionCollectionFactory
     */
    protected $regionCollectionFactory;

    protected $vehicleLookupService;

    /**
     * @var VehicleFactory
     */
    protected $vehicleFactory;

    /**
     * @var VehicleResource
     */
    protected $vehicleResource;

    /**
     * @var \Webqem\CustomerVehicle\Model\Vehicle|null
     */
    protected $vehicle = null;


    /**
     * @param Context $context
     * @param Session $customerSession
     * @param RegionCollectionFactory $regionCollectionFactory
     * @param VehicleFactory $vehicleFactory
     * @param VehicleResource $vehicleResource
     * @param array $data
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        RegionCollectionFactory $regionCollectionFactory,
        VehicleLookupService $vehicleLookupService,
        VehicleFactory $vehicleFactory,
        VehicleResource $vehicleResource,
        array $data = []
    ) {
        $this->customerSession = $customerSession;
        $this->regionCollectionFactory = $regionCollectionFactory;
        $this->vehicleLookupService  = $vehicleLookupService;
        $this->vehicleFactory = $vehicleFactory;
        $this->vehicleResource = $vehicleResource;
        parent::__construct($context, $data);
    }

    /**
     * Get form action URL
     *
     * @return string
     */
    public function getFormAction()
    {
        return $this->getUrl('*/*/saveRego');
    }

    /**
     * Get States/Regions for dropdown
     *
     * @return array
     */
    public function getStates()
    {
        $regions = $this->regionCollectionFactory->create()
            ->addFieldToFilter('country_id', 'AU')
            ->setOrder('name', 'ASC');

        $options = [];
        foreach ($regions as $region) {
            $options[] = [
                'value' => $region->getCode(),
                'label' => $region->getName()
            ];
        }

        return $options;
    }

    /**
     * Get the vehicle object
     *
     * @return \Webqem\CustomerVehicle\Model\Vehicle
     */
    public function getVehicle()
    {
        if ($this->vehicle === null) {
            $vehicleId = $this->getRequest()->getParam('id');
            $vehicle = $this->vehicleFactory->create();

            if ($vehicleId) {
                $this->vehicleResource->load($vehicle, $vehicleId);
            }

            $this->vehicle = $vehicle;
        }

        return $this->vehicle;
    }
}
