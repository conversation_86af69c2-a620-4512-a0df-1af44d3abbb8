<?php
/**
 * Vehicle Search Results Interface
 */
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

/**
 * Interface VehicleSearchResultsInterface
 * @api
 */
interface VehicleSearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get vehicles list
     *
     * @return \Webqem\CustomerVehicle\Api\Data\VehicleInterface[]
     */
    public function getItems();

    /**
     * Set vehicles list
     *
     * @param \Webqem\CustomerVehicle\Api\Data\VehicleInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
