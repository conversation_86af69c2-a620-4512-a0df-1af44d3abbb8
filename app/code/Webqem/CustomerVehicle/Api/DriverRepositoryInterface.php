<?php
/**
 * Driver Repository Interface
 */
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Api;

use Webqem\CustomerVehicle\Api\Data\DriverInterface;
use Webqem\CustomerVehicle\Api\Data\DriverSearchResultsInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Interface DriverRepositoryInterface
 * @api
 */
interface DriverRepositoryInterface
{
    /**
     * Save driver
     *
     * @param DriverInterface $driver
     * @return DriverInterface
     * @throws LocalizedException
     */
    public function save(DriverInterface $driver);

    /**
     * Get driver by ID
     *
     * @param int $driverId
     * @return DriverInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $driverId);

    /**
     * Retrieve drivers matching the specified criteria
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return DriverSearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Delete driver
     *
     * @param DriverInterface $driver
     * @return bool
     * @throws LocalizedException
     */
    public function delete(DriverInterface $driver);

    /**
     * Delete driver by ID
     *
     * @param int $driverId
     * @return bool
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(int $driverId);

    /**
     * Get drivers by vehicle ID
     *
     * @param int $vehicleId
     * @return DriverSearchResultsInterface
     */
    public function getByVehicleId(int $vehicleId);
}
