<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Vehicle -->
    <preference for="Webqem\CustomerVehicle\Api\Data\VehicleInterface" type="Webqem\CustomerVehicle\Model\Vehicle" />
    <preference for="Webqem\CustomerVehicle\Api\Data\VehicleSearchResultsInterface" type="Webqem\CustomerVehicle\Model\VehicleSearchResults" />
    <preference for="Webqem\CustomerVehicle\Api\VehicleRepositoryInterface" type="Webqem\CustomerVehicle\Model\VehicleRepository" />

    <!-- Driver -->
    <preference for="Webqem\CustomerVehicle\Api\Data\DriverInterface" type="Webqem\CustomerVehicle\Model\Driver" />
    <preference for="Webqem\CustomerVehicle\Api\Data\DriverSearchResultsInterface" type="Webqem\CustomerVehicle\Model\DriverSearchResults" />
    <preference for="Webqem\CustomerVehicle\Api\DriverRepositoryInterface" type="Webqem\CustomerVehicle\Model\DriverRepository" />

    <type name="Webqem\CustomerVehicle\Api\VehicleRepositoryInterface">
        <plugin name="webqem_vehicle_load_drivers" type="Webqem\CustomerVehicle\Plugin\VehicleRepositoryPlugin" />
    </type>
</config>
