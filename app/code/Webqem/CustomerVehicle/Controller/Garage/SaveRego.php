<?php
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Controller\Garage;

use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Webqem\CustomerVehicle\Api\VehicleRepositoryInterface;
use Webqem\CustomerVehicle\Model\VehicleFactory;

class SaveRego implements HttpPostActionInterface
{
    /**
     * @var RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var FormKeyValidator
     */
    protected $formKeyValidator;

    /**
     * @var VehicleRepositoryInterface
     */
    protected $vehicleRepository;

    /**
     * @var VehicleFactory
     */
    protected $vehicleFactory;

    /**
     * @param RedirectFactory $resultRedirectFactory
     * @param ManagerInterface $messageManager
     * @param Session $customerSession
     * @param RequestInterface $request
     * @param FormKeyValidator $formKeyValidator
     * @param VehicleRepositoryInterface $vehicleRepository
     * @param VehicleFactory $vehicleFactory
     */
    public function __construct(
        RedirectFactory $resultRedirectFactory,
        ManagerInterface $messageManager,
        Session $customerSession,
        RequestInterface $request,
        FormKeyValidator $formKeyValidator,
        VehicleRepositoryInterface $vehicleRepository,
        VehicleFactory $vehicleFactory
    ) {
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->messageManager = $messageManager;
        $this->customerSession = $customerSession;
        $this->request = $request;
        $this->formKeyValidator = $formKeyValidator;
        $this->vehicleRepository = $vehicleRepository;
        $this->vehicleFactory = $vehicleFactory;
    }

    /**
     * Save vehicle
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();

        // Check if user is logged in
        if (!$this->customerSession->isLoggedIn()) {
            $this->messageManager->addErrorMessage(__('Please log in to save rego to a vehicle.'));
            return $resultRedirect->setPath('customer/account/login');
        }

        // Validate form key
        if (!$this->formKeyValidator->validate($this->request)) {
            $this->messageManager->addErrorMessage(__('Invalid form key. Please try again.'));
            return $resultRedirect->setPath('*/*/addRego');
        }
        // Get form data
        $data = $this->request->getPostValue();
        try {
            // Create new vehicle
            $vehicle = $this->vehicleRepository->getById((int)$data['vehicle_id']);
            $vehicle->setRegoNumber($data['car_rego']);
            $vehicle->setState($data['state']);

            // Save vehicle
            $this->vehicleRepository->save($vehicle);

            $this->messageManager->addSuccessMessage(__('Rego added successfully.'));
            return $resultRedirect->setPath('*/*/index');

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while saving your vehicle: %1', $e->getMessage()));
            return $resultRedirect->setPath('*/*/add');
        }
    }
}
