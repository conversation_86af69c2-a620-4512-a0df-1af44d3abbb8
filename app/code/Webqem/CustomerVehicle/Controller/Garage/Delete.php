<?php
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Controller\Garage;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Customer\Model\Session;
use Webqem\CustomerVehicle\Model\VehicleFactory;
use Webqem\CustomerVehicle\Model\ResourceModel\Vehicle as VehicleResource;
use Magento\Framework\Exception\LocalizedException;

class Delete implements HttpGetActionInterface
{
    /**
     * @var RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var VehicleFactory
     */
    protected $vehicleFactory;

    /**
     * @var VehicleResource
     */
    protected $vehicleResource;

    /**
     * @param RedirectFactory $resultRedirectFactory
     * @param RequestInterface $request
     * @param ManagerInterface $messageManager
     * @param Session $customerSession
     * @param VehicleFactory $vehicleFactory
     * @param VehicleResource $vehicleResource
     */
    public function __construct(
        RedirectFactory $resultRedirectFactory,
        RequestInterface $request,
        ManagerInterface $messageManager,
        Session $customerSession,
        VehicleFactory $vehicleFactory,
        VehicleResource $vehicleResource
    ) {
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->request = $request;
        $this->messageManager = $messageManager;
        $this->customerSession = $customerSession;
        $this->vehicleFactory = $vehicleFactory;
        $this->vehicleResource = $vehicleResource;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();

        if (!$this->customerSession->isLoggedIn()) {
            return $resultRedirect->setPath('customer/account/login');
        }

        try {
            $vehicleId = (int)$this->request->getParam('id');
            if (!$vehicleId) {
                $this->messageManager->addErrorMessage(__('Please select a vehicle to delete.'));
                return $resultRedirect->setPath('*/*/');
            }

            $vehicle = $this->vehicleFactory->create();
            $this->vehicleResource->load($vehicle, $vehicleId);

            if (!$vehicle->getId()) {
                $this->messageManager->addErrorMessage(__('The vehicle no longer exists.'));
                return $resultRedirect->setPath('*/*/');
            }

            if ($vehicle->getCustomerId() != $this->customerSession->getCustomerId()) {
                $this->messageManager->addErrorMessage(__('You do not have permission to delete this vehicle.'));
                return $resultRedirect->setPath('*/*/');
            }

            $this->vehicleResource->delete($vehicle);

            $this->messageManager->addSuccessMessage(__('The vehicle has been deleted.'));
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->messageManager->addExceptionMessage($e, __('Something went wrong while deleting the vehicle.'));
        }

        return $resultRedirect->setPath('*/*/');
    }
}
