<?php
namespace Webqem\CustomerVehicle\Controller\Garage;

use Magento\Framework\App\Action\Action;
use Magento\Framework\View\Result\PageFactory;

class AddRego extends Action
{
    protected $resultPageFactory;

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    public function execute()
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->getConfig()->getTitle()->set(__('Add Rego for Vehicle'));
        return $resultPage;
    }
}
