<?php
declare(strict_types=1);

namespace Webqem\CustomerVehicle\Controller\Driver;

use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Webqem\CustomerVehicle\Api\DriverRepositoryInterface;
use Webqem\CustomerVehicle\Model\DriverFactory;

class Save implements HttpPostActionInterface
{
    /**
     * @var RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var FormKeyValidator
     */
    protected $formKeyValidator;

    /**
     * @var DriverRepositoryInterface
     */
    protected $driverRepository;

    /**
     * @var DriverFactory
     */
    protected $driverFactory;

    /**
     * @param RedirectFactory $resultRedirectFactory
     * @param ManagerInterface $messageManager
     * @param Session $customerSession
     * @param RequestInterface $request
     * @param FormKeyValidator $formKeyValidator
     * @param DriverRepositoryInterface $driverRepository
     * @param DriverFactory $driverFactory
     */
    public function __construct(
        RedirectFactory           $resultRedirectFactory,
        ManagerInterface          $messageManager,
        Session                   $customerSession,
        RequestInterface          $request,
        FormKeyValidator          $formKeyValidator,
        DriverRepositoryInterface $driverRepository,
        DriverFactory             $driverFactory
    )
    {
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->messageManager = $messageManager;
        $this->customerSession = $customerSession;
        $this->request = $request;
        $this->formKeyValidator = $formKeyValidator;
        $this->driverRepository = $driverRepository;
        $this->driverFactory = $driverFactory;
    }

    /**
     * Save vehicle
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();

        // Check if user is logged in
        if (!$this->customerSession->isLoggedIn()) {
            $this->messageManager->addErrorMessage(__('Please log in to add a driver to selected vehicle.'));
            return $resultRedirect->setPath('customer/account/login');
        }

        // Validate form key
        if (!$this->formKeyValidator->validate($this->request)) {
            $this->messageManager->addErrorMessage(__('Invalid form key. Please try again.'));
            return $resultRedirect->setPath('customervehicle/garage/index');
        }

        // Get form data
        $data = $this->request->getPostValue();
        try {
            if (isset($data['driver_id']) && $data['driver_id'] != '') {
                $driver = $this->driverRepository->getById((int)$data['driver_id']);
            } else {
                $driver = $this->driverFactory->create();
            }
            $driver->setFirstname($data['firstname']);
            $driver->setLastname($data['lastname']);
            $driver->setVehicleId((int)$data['vehicleId']);
            $driver->setEmail($data['email']);
            $driver->setRelationshipToOwner($data['relationshipToOwner']);
            $driver->setMobileNumber($data['mobileNumber']);

            // Save vehicle
            $this->driverRepository->save($driver);

            $this->messageManager->addSuccessMessage(__('The driver information saved successfully.'));
            return $resultRedirect->setPath('*/garage/vehicledetail', ['id' => $driver->getVehicleId()]);

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while saving your driver: %1', $e->getMessage()));
            return $resultRedirect->setPath('*/garage/vehicledetail', ['id' => $data['vehicleId']]);
        }
    }
}
