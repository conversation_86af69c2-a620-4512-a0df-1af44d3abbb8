<?php
/** @var \Webqem\CustomerVehicle\Block\Garage $block */
?>
<div class="garage-page">
    <div class="block block-garage">
        <div class="block-title">
            <strong><?= $block->escapeHtml(__('My Vehicles')) ?></strong>
            <a href="<?= $block->escapeUrl($block->getUrl('*/*/add')) ?>" class="action add">
                <span><?= $block->escapeHtml(__('Add New Vehicle')) ?></span>
            </a>
        </div>
        <div class="block-content">
            <?php $vehicles = $block->getVehicles(); ?>
            <?php if (count($vehicles)): ?>
                <div class="table-wrapper vehicles-table">
                    <table class="data table table-vehicles">
                        <caption class="table-caption"><?= $block->escapeHtml(__('My Vehicles')) ?></caption>
                        <thead>
                        <tr>
                            <th scope="col" class="col vehicle_name"><?= $block->escapeHtml(__('Name')) ?></th>
                            <th scope="col" class="col rego_number"><?= $block->escapeHtml(__('Rego Number')) ?></th>
                            <th scope="col" class="col state"><?= $block->escapeHtml(__('State')) ?></th>
                            <th scope="col" class="col make"><?= $block->escapeHtml(__('Make')) ?></th>
                            <th scope="col" class="col model"><?= $block->escapeHtml(__('Model')) ?></th>
                            <th scope="col" class="col year"><?= $block->escapeHtml(__('Year')) ?></th>
                            <th scope="col" class="col actions">Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($vehicles as $vehicle): ?>
                            <tr>
                                <td data-th="<?= $block->escapeHtml(__('Name')) ?>" class="col vehicle_name">
                                    <?= $block->escapeHtml($vehicle->getVehicleName()) ?>
                                </td>
                                <td data-th="<?= $block->escapeHtml(__('Rego Number')) ?>" class="col rego_number">
                                    <?= $block->escapeHtml($vehicle->getRegoNumber()) ?>
                                </td>
                                <td data-th="<?= $block->escapeHtml(__('State')) ?>" class="col state">
                                    <?= $block->escapeHtml($vehicle->getState()) ?>
                                </td>
                                <td data-th="<?= $block->escapeHtml(__('Make')) ?>" class="col make">
                                    <?= $block->escapeHtml($vehicle->getMake()) ?>
                                </td>
                                <td data-th="<?= $block->escapeHtml(__('Model')) ?>" class="col model">
                                    <?= $block->escapeHtml($vehicle->getModel()) ?>
                                </td>
                                <td data-th="<?= $block->escapeHtml(__('Year')) ?>" class="col year">
                                    <?= $block->escapeHtml($vehicle->getYear()) ?>
                                </td>
                                <td data-th="<?= $block->escapeHtml(__('Actions')) ?>" class="col actions">
                                    <?php if (!$vehicle->getRegoNumber()) : ?>
                                        <a href="<?= $block->escapeUrl($block->getUrl('*/*/addrego', ['id' => $vehicle->getVehicleId()])) ?>" class="action edit">
                                            <span><?= $block->escapeHtml(__('Add Rego')) ?></span>
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?= $block->escapeUrl($block->getUrl('*/*/vehicledetail', ['id' => $vehicle->getVehicleId()])) ?>" class="action vehicle_detail_view">
                                        <span><?= $block->escapeHtml(__('View Details')) ?></span>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="message info empty">
                    <span><?= $block->escapeHtml(__('You have no vehicles in your garage.')) ?></span>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
