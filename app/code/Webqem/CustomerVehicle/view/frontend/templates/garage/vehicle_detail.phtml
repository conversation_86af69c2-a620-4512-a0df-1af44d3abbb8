<?php
/** @var \Webqem\CustomerVehicle\Block\Garage\Edit $block */
$vehicle = $block->getVehicle();
?>
<div class="garage-edit-page">
    <div class="block block-garage-edit">
        <div class="block-content">
            <form class="form form-edit-vehicle"
                  action="<?= $block->escapeUrl($block->getUrl('*/*/save')) ?>"
                  method="post"
                  id="form-validate"
                  enctype="multipart/form-data"
                  data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
                  autocomplete="off">

                <?= $block->getBlockHtml('formkey') ?>
                <input type="hidden" name="vehicle_id" value="<?= $block->escapeHtmlAttr($vehicle->getVehicleId()) ?>">

                <fieldset class="fieldset info">
                    <!-- Form fields remain unchanged -->
                    <div class="field vehicle-name">
                        <label class="label" for="vehicle_name"><span><?= $block->escapeHtml(__('Vehicle Name')) ?></span></label>
                        <div class="control">
                            <input type="text"
                                   name="vehicle_name"
                                   id="vehicle_name"
                                   value="<?= $block->escapeHtmlAttr($vehicle->getVehicleName()) ?>"
                                   title="<?= $block->escapeHtmlAttr(__('Vehicle Name')) ?>"
                                   class="input-text"
                                   readonly>
                        </div>
                    </div>

                    <div class="field make">
                        <label class="label" for="make"><span><?= $block->escapeHtml(__('Make')) ?></span></label>
                        <div class="control">
                            <input type="text"
                                   name="make"
                                   id="make"
                                   value="<?= $block->escapeHtmlAttr($vehicle->getMake()) ?>"
                                   title="<?= $block->escapeHtmlAttr(__('Make')) ?>"
                                   class="input-text"
                                   readonly>
                        </div>
                    </div>

                    <div class="field model">
                        <label class="label" for="model"><span><?= $block->escapeHtml(__('Model')) ?></span></label>
                        <div class="control">
                            <input type="text"
                                   name="model"
                                   id="model"
                                   value="<?= $block->escapeHtmlAttr($vehicle->getModel()) ?>"
                                   title="<?= $block->escapeHtmlAttr(__('Model')) ?>"
                                   class="input-text"
                                   readonly>
                        </div>
                    </div>

                    <div class="field series">
                        <label class="label" for="series"><span><?= $block->escapeHtml(__('Series')) ?></span></label>
                        <div class="control">
                            <input type="text"
                                   name="series"
                                   id="series"
                                   value="<?= $block->escapeHtmlAttr($vehicle->getSeries()) ?>"
                                   title="<?= $block->escapeHtmlAttr(__('Series')) ?>"
                                   class="input-text" readonly>
                        </div>
                    </div>

                    <div class="field year">
                        <label class="label" for="year"><span><?= $block->escapeHtml(__('Year')) ?></span></label>
                        <div class="control">
                            <input type="text"
                                   name="year"
                                   id="year"
                                   value="<?= $block->escapeHtmlAttr($vehicle->getYear()) ?>"
                                   title="<?= $block->escapeHtmlAttr(__('Year')) ?>"
                                   class="input-text"
                                   readonly>
                        </div>
                    </div>
                </fieldset>

                <fieldset class="fieldset additional-info">
                    <legend class="legend"><span><?= $block->escapeHtml(__('Driver Information')) ?></span></legend>
                    <?php if (!$block->getVehicleDriver()) : ?>
                        <div class="no-driver-found">No driver is associated with the vehicle</div>
                    <?php else :?>
                        <div class="vehicle-driver-info">
                            <table class="data table table-vehicles">
                                <caption class="table-caption"><?= $block->escapeHtml(__('My Vehicles')) ?></caption>
                                <thead>
                                <tr>
                                    <th scope="col" class="col vehicle_name"><?= $block->escapeHtml(__('Firstname')) ?></th>
                                    <th scope="col" class="col rego_number"><?= $block->escapeHtml(__('Lastname')) ?></th>
                                    <th scope="col" class="col state"><?= $block->escapeHtml(__('Mobile Number')) ?></th>
                                    <th scope="col" class="col make"><?= $block->escapeHtml(__('Email')) ?></th>
                                    <th scope="col" class="col model"><?= $block->escapeHtml(__('Relationship')) ?></th>
                                    <th scope="col" class="col actions">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($block->getVehicleDriver() as $driver): ?>
                                    <tr>
                                        <td data-th="<?= $block->escapeHtml(__('Firstname')) ?>" class="col firstname">
                                            <?= $block->escapeHtml($driver->getFirstName()) ?>
                                        </td>
                                        <td data-th="<?= $block->escapeHtml(__('Lastname')) ?>" class="col lastname">
                                            <?= $block->escapeHtml($driver->getLastname()) ?>
                                        </td>
                                        <td data-th="<?= $block->escapeHtml(__('Mobile Number')) ?>" class="col mobile_number">
                                            <?= $block->escapeHtml($driver->getMobileNumber()) ?>
                                        </td>
                                        <td data-th="<?= $block->escapeHtml(__('Email')) ?>" class="col email">
                                            <?= $block->escapeHtml($driver->getEmail()) ?>
                                        </td>
                                        <td data-th="<?= $block->escapeHtml(__('Relationship')) ?>" class="col relationship">
                                            <?= $block->escapeHtml($driver->getRelationshipToOwner()) ?>
                                        </td>
                                        <td data-th="<?= $block->escapeHtml(__('Actions')) ?>" class="col actions">
                                            <a href="<?= $block->escapeUrl($block->getUrl('customervehicle/driver/edit', ['driver_id' => $driver->getDriverId(), 'vehicleId' => $driver->getVehicleId()])) ?>" class="action edit">
                                                Edit Driver
                                            </a>
                                            <a class="action delete driver-delete-button" href="#"
                                               data-vehicle-id="<?= $block->escapeHtmlAttr($vehicle->getVehicleId()) ?>"
                                               data-delete-url="<?= $block->escapeUrl($block->getUrl('*/driver/delete', ['driver_id' => $driver->getDriverId()])) ?>">
                                                <span><?= $block->escapeHtml(__('Delete Driver')) ?></span>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </fieldset>

                <div class="actions-toolbar">
                    <div class="secondary">
                        <div class="action-buttons">
                            <a href="<?= $block->escapeUrl($block->getUrl('customervehicle/driver/add', ['vehicle_id' => $vehicle->getVehicleId()])) ?>" class="action-primary">
                                Add a new driver
                            </a>
                            <a class="action-primary delete" href="#" id="vehicle-delete-button"
                               data-vehicle-id="<?= $block->escapeHtmlAttr($vehicle->getVehicleId()) ?>"
                               data-delete-url="<?= $block->escapeUrl($block->getUrl('*/*/delete', ['id' => $vehicle->getVehicleId()])) ?>">
                                <span><?= $block->escapeHtml(__('Delete Car')) ?></span>
                            </a>
                            <a class="action-primary back_to_garage" href="<?= $block->escapeUrl($block->getUrl('*/*/')) ?>">
                                <span><?= $block->escapeHtml(__('Back to My Garage')) ?></span>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<script type="text/x-magento-init">
    {
        "*": {
            "Webqem_CustomerVehicle/js/vehicle-delete": {},
            "Webqem_CustomerVehicle/js/driver-delete": {}
        }
    }
</script>
