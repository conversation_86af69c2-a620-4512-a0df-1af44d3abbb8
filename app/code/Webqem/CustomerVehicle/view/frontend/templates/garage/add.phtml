<?php
/** @var \Webqem\CustomerVehicle\Block\Garage\Add $block */
?>
<div class="add-vehicle-form">
    <div class="search-methods">
        <div class="method-toggle">
            <button type="button" class="action toggle active" id="toggle-rego">
                <?= $block->escapeHtml(__('Find by Rego')) ?>
            </button>
            <button type="button" class="action toggle" id="toggle-manual">
                <?= $block->escapeHtml(__('Find by Make, Model and Year')) ?>
            </button>
        </div>
    </div>

    <form id="add-vehicle-form" action="<?= $block->escapeUrl($block->getFormAction()) ?>" method="post" data-mage-init='{"validation": {}}'>
        <?= $block->getBlockHtml('formkey') ?>

        <!-- SCENARIO 1: Registration lookup section -->
        <fieldset id="rego-search" class="fieldset">

            <div class="field rego required">
                <label class="label" for="rego_number"><span><?= $block->escapeHtml(__('Car Rego')) ?></span></label>
                <div class="control">
                    <input type="text" name="rego_number" id="rego_number" value=""
                           title="<?= $block->escapeHtmlAttr(__('Car Rego')) ?>"
                           class="input-text" data-validate="{required:true}"/>
                </div>
            </div>

            <div class="field state required">
                <label class="label" for="state"><span><?= $block->escapeHtml(__('State')) ?></span></label>
                <div class="control">
                    <select name="state" id="state" title="<?= $block->escapeHtmlAttr(__('State')) ?>"
                            class="select" data-validate="{required:true}">
                        <option value=""><?= $block->escapeHtml(__('Please select a state')) ?></option>
                        <?php foreach ($block->getStates() as $state): ?>
                            <option value="<?= $block->escapeHtmlAttr($state['value']) ?>">
                                <?= $block->escapeHtml($state['label']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="actions-toolbar">
                <div class="primary">
                    <button type="button" id="find-car-button" class="action find primary">
                        <span><?= $block->escapeHtml(__('Find My Car')) ?></span>
                    </button>
                </div>
            </div>
        </fieldset>

        <!-- SCENARIO 2: Manual selection section -->
        <fieldset id="manual-search" class="fieldset" style="display: none;">
            <legend class="legend"><span><?= $block->escapeHtml(__('Vehicle Make, Model and Year')) ?></span></legend>

            <div class="field make required">
                <label class="label" for="make_select"><span><?= $block->escapeHtml(__('Make')) ?></span></label>
                <div class="control">
                    <select name="make_select" id="make_select" title="<?= $block->escapeHtmlAttr(__('Make')) ?>"
                            class="select" data-validate="{required:true}">
                        <option value=""><?= $block->escapeHtml(__('Please select a make')) ?></option>
                        <?php foreach ($block->getVehicleMakes() as $make): ?>
                            <option value="<?= $block->escapeHtmlAttr($make['Id']) ?>">
                                <?= $block->escapeHtml($make['Name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="field model required">
                <label class="label" for="model_select"><span><?= $block->escapeHtml(__('Model')) ?></span></label>
                <div class="control">
                    <select name="model_select" id="model_select" title="<?= $block->escapeHtmlAttr(__('Model')) ?>"
                            class="select" data-validate="{required:true}" disabled>
                        <option value=""><?= $block->escapeHtml(__('Please select a model')) ?></option>
                    </select>
                </div>
            </div>

            <div class="field year required">
                <label class="label" for="year_select"><span><?= $block->escapeHtml(__('Year')) ?></span></label>
                <div class="control">
                    <select name="year_select" id="year_select" title="<?= $block->escapeHtmlAttr(__('Year')) ?>"
                            class="select" data-validate="{required:true}" disabled>
                        <option value=""><?= $block->escapeHtml(__('Please select a year')) ?></option>
                    </select>
                </div>
            </div>

            <div class="field series">
                <label class="label" for="series_select"><span><?= $block->escapeHtml(__('Series')) ?></span></label>
                <div class="control">
                    <select name="series_select" id="series_select" title="<?= $block->escapeHtmlAttr(__('Series')) ?>"
                            class="select" disabled>
                        <option value=""><?= $block->escapeHtml(__('Please select a series (optional)')) ?></option>
                    </select>
                </div>
            </div>

            <div class="actions-toolbar">
                <div class="primary">
                    <button type="button" id="find-car-manual-button" class="action find primary">
                        <span><?= $block->escapeHtml(__('Find My Car')) ?></span>
                    </button>
                </div>
            </div>
        </fieldset>

        <!-- Vehicle Details Display Section - Shown after vehicle lookup -->
        <div id="vehicle-details" style="display: none;" class="vehicle-details-container">
            <h3><?= $block->escapeHtml(__('Vehicle Details')) ?></h3>
            <div class="vehicle-details-content">
                <div class="field vehicle_name">
                    <label class="label" for="vehicle_name"><span><?= $block->escapeHtml(__('Vehicle Name')) ?></span></label>
                    <div class="control">
                        <input type="text" name="vehicle_name" id="vehicle_name" readonly class="input-text" />
                    </div>
                </div>

                <div class="field make">
                    <label class="label" for="make"><span><?= $block->escapeHtml(__('Make')) ?></span></label>
                    <div class="control">
                        <input type="text" name="make" id="make" readonly class="input-text" />
                    </div>
                </div>

                <div class="field model">
                    <label class="label" for="model"><span><?= $block->escapeHtml(__('Model')) ?></span></label>
                    <div class="control">
                        <input type="text" name="model" id="model" readonly class="input-text" />
                    </div>
                </div>

                <div class="field year">
                    <label class="label" for="year"><span><?= $block->escapeHtml(__('Year')) ?></span></label>
                    <div class="control">
                        <input type="text" name="year" id="year" readonly class="input-text" />
                    </div>
                </div>

                <div class="field series">
                    <label class="label" for="series"><span><?= $block->escapeHtml(__('Series')) ?></span></label>
                    <div class="control">
                        <input type="text" name="series" id="series" readonly class="input-text" />
                    </div>
                </div>

                <div class="field poll_id">
                    <label class="label" for="poll_id"><span><?= $block->escapeHtml(__('Poll ID')) ?></span></label>
                    <div class="control">
                        <input type="text" name="poll_id" id="poll_id" readonly class="input-text" />
                    </div>
                </div>

                <input type="hidden" name="vehicle_logic_id" id="vehicle_logic_id" value="" />
                <input type="hidden" name="search_method" id="search_method" value="rego" />
            </div>
        </div>

        <div class="message error" id="lookup-error" style="display: none;"></div>

        <div class="actions-toolbar final-actions">
            <div class="primary">
                <button type="submit" id="add-car-button" class="action submit primary" disabled>
                    <span><?= $block->escapeHtml(__('Add My Car to Garage')) ?></span>
                </button>
            </div>
            <div class="secondary">
                <a class="action back" href="<?= $block->escapeUrl($block->getUrl('*/*/index')) ?>">
                    <span><?= $block->escapeHtml(__('Back to My Garage')) ?></span>
                </a>
            </div>
        </div>
    </form>
</div>

<script type="text/x-magento-init">
    {
        "*": {
            "Webqem_CustomerVehicle/js/vehicle-lookup": {
                "lookupUrl": "<?= $block->escapeJs($block->getUrl('*/*/lookup')) ?>",
                "getModelsUrl": "<?= $block->escapeJs($block->getUrl('*/*/modellookup')) ?>",
                "getYearsUrl": "<?= $block->escapeJs($block->getUrl('*/*/yearlookup')) ?>",
                "getSeriesUrl": "<?= $block->escapeJs($block->getUrl('*/*/serieslookup')) ?>"
        }
    }
}
</script>
