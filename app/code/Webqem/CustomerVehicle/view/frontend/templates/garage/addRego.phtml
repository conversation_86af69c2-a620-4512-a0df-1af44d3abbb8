<?php
/** @var \Webqem\CustomerVehicle\Block\AddRegoForm $block */
$vehicle = $block->getVehicle();
?>
<form action="<?= $block->getFormAction() ?>" method="post"
      id="form-add-rego-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
      autocomplete="off"
      data-mage-init='{"validation": {}}'>
    <?= $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="vehicle_id" value="<?= $block->escapeHtmlAttr($vehicle->getVehicleId()) ?>">
    <fieldset class="fieldset info">
        <div class="field car_rego required">
            <label class="label" for="car_rego"><span><?= $block->escapeHtml(__('Car Rego')) ?></span></label>
            <div class="control">
                <input type="text" id="car_rego" name="car_rego" class="input-text required-entry"
                       data-validate='{"required":true}'/>
            </div>
        </div>
        <div class="field state required">
            <label class="label" for="state"><span><?= $block->escapeHtml(__('State')) ?></span></label>
            <div class="control">
                <select name="state" id="state" title="<?= $block->escapeHtmlAttr(__('State')) ?>"
                        class="select" data-validate="{required:true}">
                    <option value=""><?= $block->escapeHtml(__('Please select a state')) ?></option>
                    <?php foreach ($block->getStates() as $state): ?>
                        <option value="<?= $block->escapeHtmlAttr($state['value']) ?>">
                            <?= $block->escapeHtml($state['label']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </fieldset>
    <div>
        <button type="submit">Save</button>
    </div>
</form>

<script type="text/x-magento-init">
    {
        "#form-add-rego-validate": {
            "validation": {}
        }
    }
</script>
