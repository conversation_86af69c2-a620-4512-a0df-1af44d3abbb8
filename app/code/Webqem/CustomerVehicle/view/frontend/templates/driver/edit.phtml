<?php
/** @var \Webqem\CustomerVehicle\Block\Driver\Edit $block */
$driver = $block->getDriver();
?>
<form action="<?= $block->getFormAction() ?>" method="post"
      id="form-add-driver-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
      autocomplete="off"
      data-mage-init='{"validation": {}}'>
    <?= $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="driver_id" value="<?= $block->escapeHtmlAttr($block->getDriverId()) ?>">
    <input type="hidden" name="vehicleId" value="<?= $block->escapeHtmlAttr($block->getVehicleId()) ?>">
    <fieldset class="fieldset info">
        <div class="field firstname required">
            <label class="label" for="firstname">First Name</label>
            <input value="<?= $driver->getFirstname() ?>" type="text" id="firstname" name="firstname" class="input-text required-entry"
                   data-validate='{"required":true}'/>
        </div>
        <div class="field lastname required">
            <label class="label" for="lastname">Last Name</label>
            <input value="<?= $driver->getLastname() ?>" type="text" id="lastname" name="lastname" class="input-text required-entry"
                   data-validate='{"required":true}'/>
        </div>
        <div class="field mobileNumber required">
            <label class="label" for="mobileNumber">Mobile</label>
            <input value="<?= $driver->getMobileNumber() ?>" type="tel" id="mobileNumber" name="mobileNumber" class="input-text required-entry"
                   data-validate='{"required":true, "validate-au-mobile": true}'/>
        </div>
        <div class="field email">
            <label class="label" for="email">Email Address</label>
            <input value="<?= $driver->getEmail() ?>" type="email" id="email" name="email" class="input-text"
                   data-validate='{"validate-email":true}'/>
        </div>
        <div class="field relationshipToOwner">
            <label class="label" for="relationshipToOwner">Relationship To Owner</label>
            <input value="<?= $driver->getRelationshipToOwner() ?>" type="text" id="relationshipToOwner" name="relationshipToOwner"/>
        </div>
        <div>
            <button type="submit">Save driver</button>
        </div>
    </fieldset>
</form>


<script type="text/x-magento-init">
    {
        "#form-add-driver-validate": {
            "validation": {}
        }
    }
</script>
