<?php
/** @var \Webqem\CustomerVehicle\Block\Driver\Add $block */
?>
<form action="<?= $block->getFormAction() ?>" method="post"
      id="form-add-driver-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
      autocomplete="off"
      data-mage-init='{"validation": {}}'>
    <?= $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="vehicleId" value="<?= $block->escapeHtmlAttr($block->getVehicleId()) ?>">
    <fieldset class="fieldset info">
        <div class="field firstname required">
            <label class="label" for="firstname">First Name</label>
            <input type="text" id="firstname" name="firstname" class="input-text required-entry"
                   data-validate='{"required":true}'/>
        </div>
        <div class="field lastname required">
            <label class="label" for="lastname">Last Name</label>
            <input type="text" id="lastname" name="lastname" class="input-text required-entry"
                   data-validate='{"required":true}'/>
        </div>
        <div class="field mobileNumber required">
            <label class="label" for="mobileNumber">Mobile</label>
            <input type="tel" id="mobileNumber" name="mobileNumber" class="input-text required-entry"
                   data-validate='{"required":true, "validate-au-mobile": true}'/>
        </div>
        <div class="field email">
            <label class="label" for="email">Email Address</label>
            <input type="email" id="email" name="email" class="input-text"
                   data-validate='{"validate-email":true}'/>
        </div>
        <div class="field relationshipToOwner">
            <label class="label" for="relationshipToOwner">Relationship To Owner</label>
            <input type="text" id="relationshipToOwner" name="relationshipToOwner"/>
        </div>
        <div>
            <button type="submit">Add driver</button>
        </div>
    </fieldset>
</form>


<script type="text/x-magento-init">
    {
        "#form-add-driver-validate": {
            "validation": {}
        }
    }
</script>
