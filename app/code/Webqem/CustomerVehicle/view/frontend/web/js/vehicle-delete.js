define([
    'jquery',
    'Magento_Ui/js/modal/confirm'
], function ($, confirmation) {
    'use strict';

    return function (config) {
        $(document).ready(function () {
            $('#vehicle-delete-button').on('click', function (e) {
                e.preventDefault();

                var deleteUrl = $(this).data('delete-url');

                confirmation({
                    title: $.mage.__('Delete Vehicle'),
                    content: $.mage.__('Are you sure you want to delete this vehicle?'),
                    actions: {
                        confirm: function () {
                            window.location.href = deleteUrl;
                        }
                    },
                    buttons: [{
                        text: $.mage.__('Cancel'),
                        class: 'action-secondary action-dismiss',
                        click: function (event) {
                            this.closeModal(event);
                        }
                    }, {
                        text: $.mage.__('Confirm'),
                        class: 'action-primary action-accept',
                        click: function (event) {
                            this.closeModal(event, true);
                        }
                    }]
                });
            });
        });
    };
});
