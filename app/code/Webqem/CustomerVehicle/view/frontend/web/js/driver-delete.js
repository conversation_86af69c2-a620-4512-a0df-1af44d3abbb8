define([
    'jquery',
    'Magento_Ui/js/modal/confirm'
], function ($, confirmation) {
    'use strict';

    return function (config) {
        $(document).ready(function () {
            $('.driver-delete-button').on('click', function (e) {
                e.preventDefault();

                var deleteUrl = $(this).data('delete-url');

                confirmation({
                    title: $.mage.__('Delete Driver'),
                    content: $.mage.__('Are you sure you want to delete this driver?'),
                    actions: {
                        confirm: function () {
                            window.location.href = deleteUrl;
                        }
                    },
                    buttons: [{
                        text: $.mage.__('Cancel'),
                        class: 'action-secondary action-dismiss',
                        click: function (event) {
                            this.closeModal(event);
                        }
                    }, {
                        text: $.mage.__('Confirm'),
                        class: 'action-primary action-accept',
                        click: function (event) {
                            this.closeModal(event, true);
                        }
                    }]
                });
            });
        });
    };
});
