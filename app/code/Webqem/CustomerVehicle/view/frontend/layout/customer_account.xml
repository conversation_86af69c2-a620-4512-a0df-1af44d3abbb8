<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <!-- Garage link -->
            <block class="Webqem\Customer\Block\Html\Link\CustomNav" name="customer-account-navigation-garage-link">
                <arguments>
                    <argument name="path" xsi:type="string">customervehicle/garage/index</argument>
                    <argument name="label" xsi:type="string">Garage</argument>
                    <argument name="sortOrder" xsi:type="number">400</argument>
                    <argument name="attributes" xsi:type="array">
                        <item name="class" xsi:type="string">nav-garage</item>
                    </argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
