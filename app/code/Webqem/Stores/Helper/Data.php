<?php
declare(strict_types=1);

namespace Webqem\Stores\Helper;

use Magento\Framework\App\Filesystem\DirectoryList;
use Webqem\Stores\Api\StoreStoreTypeRepositoryInterface;
use Webqem\Stores\Api\StoreTypeRepositoryInterface;
use Webqem\Stores\Api\StoreRepositoryInterface;
use Webqem\Stores\Model\ImageUploader;
use Webqem\Stores\Model\StoreFactory;
use Webqem\Stores\Model\StoreMobilePostcodes\Builder as StoreMobilePostcodeBuilder;
use Webqem\Stores\Model\StoreOpeningTime\Builder as StoreOpeningTimeBuilder;
use Webqem\Stores\Model\StoreServices\Builder as StoreServicesBuilder;
use Webqem\Stores\Model\StoreType\Builder as StoreTypeBuilder;
use Webqem\Stores\Model\StoreMobilePostcodeFactory;
use Webqem\Stores\Model\StoreOpeningTimeFactory;
use Webqem\Stores\Model\StoreServicesFactory;
use Webqem\Stores\Model\StoreTypeFactory;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Webqem\Stores\Model\StoreStoreTypeFactory;
use Magento\Framework\File\Csv;
use Magento\Store\Model\StoreManagerInterface;
use Webqem\Stores\Model\ResourceModel\Store\CollectionFactory as StoreCollectionFactory;

/**
 * Class Helper Data School Address
 */
class Data extends AbstractHelper
{
    /**
     * Connection timeout, seconds
     */
    public const CONNECTION_TIMEOUT = 60;
    const TMP_PATH = 'storemobilepostcodes/tmp';
    protected $storeMobilePostcodes = [];

    /**
     * @var ImageUploader
     */
    protected $imageUploaderModel;

    /**
     * @var StoreMobilePostcodeBuilder
     */
    protected $storePostcodeBuilder;

    /**
     * @var StoreMobilePostcodeFactory
     */
    protected $storeMobilePostcodeFactory;

    /**
     * @var StoreFactory
     */
    protected $storeFactory;

    /**
     * @var DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $fileCsv;

    /**
     * @var StoreOpeningTimeBuilder
     */
    protected $storeOpeningTimeBuilder;

    /**
     * @var StoreOpeningTimeFactory
     */
    protected $storeOpeningTimeFactory;

    /**
     * @var StoreServicesBuilder
     */
    protected $storeServicesBuilder;

    /**
     * @var StoreServicesFactory
     */
    protected $storeServicesFactory;

    /**
     * @var StoreStoreTypeFactory
     */
    protected $storeStoreTypeFactory;

    /**
     * @var StoreStoreTypeRepositoryInterface
     */
    protected $storeStoreTypeRepository;

    /**
     * @var StoreTypeBuilder
     */
    protected $storeTypeBuilder;

    /**
     * @var StoreTypeRepositoryInterface
     */
    protected $storeTypeRepository;

    /**
     * @var StoreTypeFactory
     */
    protected $storeTypeFactory;

    /**
     * @var StoreRepositoryInterface
     */
    protected $storeRepository;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var StoreCollectionFactory
     */
    protected $storeCollectionFactory;

    /**
     * @param Context $context
     * @param ImageUploader $imageUploaderModel
     * @param StoreMobilePostcodeBuilder $storePostcodeBuilder
     * @param StoreOpeningTimeBuilder $storeOpeningTimeBuilder
     * @param StoreServicesBuilder $storeServicesBuilder
     * @param StoreMobilePostcodeFactory $storeMobilePostcodeFactory
     * @param StoreOpeningTimeFactory $storeOpeningTimeFactory
     * @param StoreServicesFactory $storeServicesFactory
     * @param StoreStoreTypeFactory $storeStoreTypeFactory
     * @param StoreStoreTypeRepositoryInterface $storeStoreTypeRepository
     * @param StoreFactory $storeFactory
     * @param DirectoryList $directoryList
     * @param Csv $fileCsv
     * @param StoreTypeBuilder $storeTypeBuilder
     * @param StoreTypeRepositoryInterface $storeTypeRepository
     * @param StoreTypeFactory $storeTypeFactory
     * @param StoreRepositoryInterface $storeRepository
     * @param StoreManagerInterface $storeManager
     * @param StoreCollectionFactory $storeCollectionFactory
     */
    public function __construct(
        Context $context,
        ImageUploader $imageUploaderModel,
        StoreMobilePostcodeBuilder $storePostcodeBuilder,
        StoreOpeningTimeBuilder $storeOpeningTimeBuilder,
        StoreServicesBuilder $storeServicesBuilder,
        StoreMobilePostcodeFactory $storeMobilePostcodeFactory,
        StoreOpeningTimeFactory $storeOpeningTimeFactory,
        StoreServicesFactory $storeServicesFactory,
        StoreStoreTypeFactory $storeStoreTypeFactory,
        StoreStoreTypeRepositoryInterface $storeStoreTypeRepository,
        StoreFactory $storeFactory,
        DirectoryList $directoryList,
        Csv $fileCsv,
        StoreTypeBuilder $storeTypeBuilder,
        StoreTypeRepositoryInterface $storeTypeRepository,
        StoreTypeFactory $storeTypeFactory,
        StoreRepositoryInterface $storeRepository,
        StoreManagerInterface $storeManager,
        StorecollectionFactory $storeCollectionFactory
    ) {
        $this->imageUploaderModel = $imageUploaderModel;
        $this->storePostcodeBuilder = $storePostcodeBuilder;
        $this->storeOpeningTimeBuilder = $storeOpeningTimeBuilder;
        $this->storeServicesBuilder = $storeServicesBuilder;
        $this->storeServicesFactory = $storeServicesFactory;
        $this->storeMobilePostcodeFactory = $storeMobilePostcodeFactory;
        $this->storeOpeningTimeFactory = $storeOpeningTimeFactory;
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeStoreTypeRepository = $storeStoreTypeRepository;
        $this->storeFactory = $storeFactory;
        $this->directoryList = $directoryList;
        $this->fileCsv = $fileCsv;
        $this->storeTypeBuilder = $storeTypeBuilder;
        $this->storeTypeRepository = $storeTypeRepository;
        $this->storeTypeFactory = $storeTypeFactory;
        $this->storeRepository = $storeRepository;
        $this->storeManager = $storeManager;
        $this->storeCollectionFactory = $storeCollectionFactory;
        parent::__construct($context);
    }

    /**
     * @param $model
     * @param $data
     * @return mixed
     */
    public function image1Data($model, $data)
    {
        if (!isset($data['image1'])) {
            return $model;
        }
        $imageUrl = $data['image1'][0]['url'];
        $imageName = $data['image1'][0]['name'];
        if ($model->getId()) {
            $storeData = $this->storeFactory->create();
            $storeData->load($model->getId());
            if (isset($data['image1'][0]['name'])) {
                $data['image1'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            } else {
                $data['image1'] = '';
            }
        } else {
            if (isset($data['image1'][0]['name'])) {
                $data['image1'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            }
        }
        $model->setData('image1', $data['image1']);
        return $model;
    }
    /**
     * @param $model
     * @param $data
     * @return mixed
     */
    public function image4Data($model, $data)
    {
        if (!isset($data['image4'])) {
            return $model;
        }
        $imageUrl = $data['image4'][0]['url'];
        $imageName = $data['image4'][0]['name'];
        if ($model->getId()) {
            $storeData = $this->storeFactory->create();
            $storeData->load($model->getId());
            if (isset($data['image4'][0]['name'])) {
                $data['image4'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            } else {
                $data['image4'] = '';
            }
        } else {
            if (isset($data['image4'][0]['name'])) {
                $data['image4'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            }
        }
        $model->setData('image4', $data['image4']);
        return $model;
    }
    /**
     * @param $model
     * @param $data
     * @return mixed
     */
    public function image2Data($model, $data)
    {
        if (!isset($data['image2'])) {
            return $model;
        }
        $imageUrl = $data['image2'][0]['url'];
        $imageName = $data['image2'][0]['name'];
        if ($model->getId()) {
            $storeData = $this->storeFactory->create();
            $storeData->load($model->getId());
            if (isset($data['image2'][0]['name'])) {
                $data['image2'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            } else {
                $data['image2'] = '';
            }
        } else {
            if (isset($data['image2'][0]['name'])) {
                $data['image2'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            }
        }
        $model->setData('image2', $data['image2']);
        return $model;
    }
    /**
     * @param $model
     * @param $data
     * @return mixed
     */
    public function image3Data($model, $data)
    {
        if (!isset($data['image3'])) {
            return $model;
        }
        $imageUrl = $data['image3'][0]['url'];
        $imageName = $data['image3'][0]['name'];
        if ($model->getId()) {
            $storeData = $this->storeFactory->create();
            $storeData->load($model->getId());
            if (isset($data['image3'][0]['name'])) {
                $data['image3'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            } else {
                $data['image3'] = '';
            }
        } else {
            if (isset($data['image3'][0]['name'])) {
                $data['image3'] = $this->imageUploaderModel->saveMediaImage($imageName, $imageUrl);
            }
        }
        $model->setData('image3', $data['image3']);
        return $model;
    }

    public function addStoreServices($store, $data)
    {
        $records = [];
        $extensionAttributes = $store->getExtensionAttributes();
        if (isset($data['store_services'])) {
            $store->setStoreServices($data['store_services']);
            foreach($data['store_services'] as $storeService) {
                $storeService['store_id'] = $store->getId();
                $records[] = $this->storeServicesBuilder->setData($storeService)->build($this->storeServicesFactory->create());
            }
        }
        $extensionAttributes->setStoreServices($records);
        $store->setExtensionAttributes($extensionAttributes);
        return $store;
    }

    public function addStoreMobilePostcode($store, $data)
    {
        $storePostcodesData = [];
        $records = [];
        $extensionAttributes = $store->getExtensionAttributes();
        $storePostcodes = $this->getPostcodes($data);
        if (isset($storePostcodes)) {
            $store->setStoreMobilePostcodeData($storePostcodes);
            foreach($storePostcodes as $postcode) {
                $storePostcodesData['postcode'] = (int) $postcode['postcode'];
                $storePostcodesData['store_id'] = $store->getId();
                $records[] = $this->storePostcodeBuilder->setData($storePostcodesData)->build($this->storeMobilePostcodeFactory->create());
            }
        }
        $extensionAttributes->setStoreMobilePostcode($records);
        $store->setExtensionAttributes($extensionAttributes);
        return $store;
    }

    public function addOrUpdateStoreOpeningtime($store, $data)
    {
        $records = [];
        $extensionAttributes = $store->getExtensionAttributes();
        if (isset($data['store_openingtime'])) {
            $store->setStoreOpeningTime($data['store_openingtime']);
            foreach($data['store_openingtime'] as $storeOpeningTime) {
                $storeOpeningTime['store_id'] = $store->getId();
                $records[] = $this->storeOpeningTimeBuilder->setData($storeOpeningTime)->build($this->storeOpeningTimeFactory->create());
            }
        }
        $extensionAttributes->setStoreOpeningTime($records);
        $store->setExtensionAttributes($extensionAttributes);
        return $store;
    }

    public function getPostcodes($data){
        if (isset($data['store_postcodes']) && is_array($data['store_postcodes'])) {
            $this->storeMobilePostcodes = $data['store_postcodes'];
        }
        if(isset($data['import'])) {
            if(is_array($data['import']) && isset($data['import'][0]['file'])){
                $postCodeFile = $data['import'][0]['file'];
                $this->loadPostcodesFromFile($postCodeFile);
            }
        }
        return $this->storeMobilePostcodes;
    }

    private function loadPostCodesFromFile($postCodeFile){
        $filePath = $this->directoryList->getPath(DirectoryList::MEDIA) . '/' . self::TMP_PATH . $postCodeFile;
        if (!file_exists($filePath)) {
            return;
        }
        $uploadedData = [];
        $recordId = 0;
        if(count($this->storeMobilePostcodes)){
            $recordId = count($this->storeMobilePostcodes);
        }
        $data = $this->fileCsv->getData($filePath);
        $dataLen = count($data);
        for($i = 1; $i < $dataLen; $i++) {
            if(isset($data[$i][0])){
                $recordId ++;
                $newRecord = [];
                $newRecord["record_id"] = $recordId;
                $newRecord["postcode"] =  $data[$i][0];
                $uploadedData[] = $newRecord;
            }
        }
        $this->storeMobilePostcodes = array_merge($this->storeMobilePostcodes, $uploadedData);
        unlink($filePath);
        return;
    }


    public function addStoreType($store, $data)
    {
        if (isset($data['store_type']) && $data['store_type'] != null) {
            $storeType = $this->storeTypeRepository->getById((int)$data['store_type']);
            $record = $this->storeTypeBuilder->setData($storeType->getData())->build($this->storeTypeFactory->create());
            $extensionAttributes = $store->getExtensionAttributes();
            $extensionAttributes->setType($record);
            $store->setExtensionAttributes($extensionAttributes);
        }
        return $store;
    }

    public function addOrUpdateStoreData($storeDataArray)
    {
        $stateMapValues = [
            'Victoria' => 607,
            'South Australia' => 609,
            'Western Australia' => 611,
            'New South Wales' => 606,
            'Tasmania' => 610,
            'Queensland' => 608,
            'Australian Capital Territory' => 605,
            'Northern Territory' => 612
        ];
        if (isset($storeDataArray['state'])) {
            $storeDataArray['state'] = $stateMapValues[$storeDataArray['state']];
        }
        try {
            $store = $this->storeRepository->getStoreByCode($storeDataArray['store_code']);
            if ($store !== null) {
                $store->setData($storeDataArray);
                $store->setDatetimeUpdated(null);
                if (isset($storeDataArray['store_openingtime']) && $storeDataArray['store_openingtime'] != null) {
                    $openingTimeData['store_openingtime'] = $this->convertExtensionAttributesData($storeDataArray['store_openingtime']);
                    $this->addOrUpdateStoreOpeningtime($store, $openingTimeData);
                }
                if (isset($storeDataArray['store_services']) && $storeDataArray['store_services'] != null) {
                    $openingTimeData['store_services'] = $this->convertExtensionAttributesData($storeDataArray['store_services']);
                    $this->addStoreServices($store, $openingTimeData);
                }
                if (isset($storeDataArray['store_postcodes']) && $storeDataArray['store_postcodes'] != null) {
                    $openingTimeData['store_postcodes'] = $this->convertExtensionAttributesData($storeDataArray['store_postcodes']);
                    $this->addStoreMobilePostcode($store, $openingTimeData);
                }
                if (isset($storeDataArray['store_type'])) {
                    $this->resolveStoreTypeData($store, $storeDataArray['store_type']);
                }
                $this->storeRepository->update($store);
            } else {
                $newStore = $this->storeFactory->create()
                    ->setData($storeDataArray);
                if (isset($storeDataArray['store_openingtime']) && $storeDataArray['store_openingtime'] != null) {
                    $openingTimeData['store_openingtime'] = $this->convertExtensionAttributesData($storeDataArray['store_openingtime']);
                    $this->addOrUpdateStoreOpeningtime($newStore, $openingTimeData);
                }
                if (isset($storeDataArray['store_services']) && $storeDataArray['store_services'] != null) {
                    $openingTimeData['store_services'] = $this->convertExtensionAttributesData($storeDataArray['store_services']);
                    $this->addStoreServices($newStore, $openingTimeData);
                }
                if (isset($storeDataArray['store_postcodes']) && $storeDataArray['store_postcodes'] != null) {
                    $openingTimeData['store_postcodes'] = $this->convertExtensionAttributesData($storeDataArray['store_postcodes']);
                    $this->addStoreMobilePostcode($newStore, $openingTimeData);
                }
                if (isset($storeDataArray['store_type'])) {
                    $this->resolveStoreTypeData($newStore, $storeDataArray['store_type']);
                }
                $this->storeRepository->save($newStore);
            }
        } catch (\Exception $e) {
            $this->_logger->info($e->getMessage());
        }
    }

    public function resolveStoreTypeData($store, $store_type)
    {
        $storeType = [
            'type' => $store_type
        ];
        $record = $this->storeTypeBuilder->setData($storeType)->build($this->storeTypeFactory->create());
        $extensionAttributes = $store->getExtensionAttributes();
        $extensionAttributes->setType($record);
    }

    public function convertExtensionAttributesData($extensionData)
    {
        // Split the string by the pipe character "|"
        $parts = explode('|', $extensionData);

        $options = [];

        foreach ($parts as $part) {
            $keyValuePairs = explode(',', $part);
            $option = [];
            foreach ($keyValuePairs as $keyValue) {
                list($key, $value) = explode('=', $keyValue);

                $option[$key] = $value;
            }
            // Add this option to the final options array
            $options[] = $option;
        }
        return $options;
    }

    /**
     * Get current base URL
     * @return string|null
     */
    public function getBaseUrl()
    {
        try {
            return $this->storeManager->getStore()->getBaseUrl();
        } catch (\Exception $e) {
            $this->_logger->info($e->getMessage());
            return null;
        }
    }

    /**
     * Get Store By State and Name
     * @param $state
     * @param $storeName
     * @return \Magento\Framework\DataObject
     */
    public function getStoreByStateAndName($state, $storeName)
    {
        $stateMapValues = [
            'vic' => 607,
            'sa' => 609,
            'wa' => 611,
            'nsw' => 606,
            'tas' => 610,
            'qld' => 608,
            'act' => 605,
            'nt' => 612
        ];
        $store = $this->storeCollectionFactory->create()
            ->addFieldToFilter('state', $stateMapValues[$state])
            ->addFieldToFilter('name', $storeName)
            ->addFieldToFilter('enabled', 1)
            ->getFirstItem();
        return $store;
    }
}
