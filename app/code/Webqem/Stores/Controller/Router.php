<?php

namespace Webqem\Stores\Controller;

use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\UrlRewrite\Model\UrlFinderInterface;
use Webqem\Stores\Helper\Data;
use Magento\Framework\App\ActionFactory;
use Magento\Framework\App\ResponseInterface;

class Router extends \Magento\UrlRewrite\Controller\Router
{
    /**
     * @var ActionFactory
     */
    protected $actionFactory;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @param ActionFactory $actionFactory
     * @param UrlInterface $url
     * @param StoreManagerInterface $storeManager
     * @param ResponseInterface $response
     * @param UrlFinderInterface $urlFinder
     * @param Data $helper
     */
    public function __construct(
        ActionFactory $actionFactory,
        UrlInterface $url,
        StoreManagerInterface $storeManager,
        ResponseInterface $response,
        UrlFinderInterface $urlFinder,
        Data $helper
    )
    {
        $this->helper = $helper;
        parent::__construct($actionFactory, $url, $storeManager, $response, $urlFinder);
    }

    /**
     * @param \Magento\Framework\App\RequestInterface $request
     * @return false|\Magento\Framework\App\ActionInterface|null
     */
    public function match(\Magento\Framework\App\RequestInterface $request)
    {
        $store_states = ['act', 'nsw', 'qld', 'wa', 'vic', 'nt', 'sa', 'tas'];
        $params = [];
        $identifier = trim($request->getPathInfo(), '/');
        $d = explode('/', $identifier);
        if (isset($d[0]) && ($d[0] != 'store')) {
            return false;
        }

        if (substr($request->getRequestString(), -1) == '/') {
            $new_url = rtrim(trim($request->getRequestString(), '/'), "/");
            $this->response->setRedirect($this->helper->getBaseUrl() . $new_url, 301);
            return $this->actionFactory->create('Magento\Framework\App\Action\Redirect');
        }

        if (isset($d[1]) && in_array($d[1], $store_states)) {
            $params['state'] = trim(preg_replace('/ +/', ' ', preg_replace('/[^-A-Za-z0-9 ]/', ' ', urldecode(html_entity_decode(strip_tags($d[1]))))));
            $request->setModuleName('store')->setControllerName('index')->setActionName('index');
            if (isset($d[2])) {
                $params['state'] = trim(preg_replace('/ +/', ' ', preg_replace('/[^-A-Za-z0-9 ]/', ' ', urldecode(html_entity_decode(strip_tags($d[1]))))));
                $params['location'] = trim(preg_replace('/ +/', ' ', preg_replace('/[^-A-Za-z0-9 ]/', ' ', urldecode(html_entity_decode(strip_tags($d[2]))))));
                $request->setModuleName('store')->setControllerName('state')->setActionName('locations');
                $store = $this->helper->getStoreByStateAndName($params['state'], $params['location']);
                if ($store->getId()) {
                    $params['store_id'] = $store->getId();
                    $params['link_store'] = $store->getIsLinkStore();
                } else {
                    $params = [];
                }
            }
            if (count($params) > 0) {
                $request->setParams($params);
            }
            $request->setAlias(\Magento\Framework\Url::REWRITE_REQUEST_PATH_ALIAS, $identifier);
            return $this->actionFactory->create(\Magento\Framework\App\Action\Forward::class);
        } else {
            return false;
        }
    }
}
