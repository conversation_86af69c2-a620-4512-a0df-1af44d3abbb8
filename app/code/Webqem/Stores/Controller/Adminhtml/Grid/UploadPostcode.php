<?php
namespace Webqem\Stores\Controller\Adminhtml\Grid;

use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Filesystem\DirectoryList;

class UploadPostcode extends \Magento\Backend\App\Action
{

    protected $storeMobilePostcode;

    protected $uploaderFactory;

    protected $storageDatabase;

    protected $mediaDirectory;

    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Webqem\Stores\Model\StoreMobilePostcode $storeMobilePostcode,
        \Magento\MediaStorage\Model\File\UploaderFactory $uploaderFactory,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\MediaStorage\Helper\File\Storage\Database $storageDatabase
    ) {
        parent::__construct($context);
        $this->storeMobilePostcode = $storeMobilePostcode;
        $this->uploaderFactory = $uploaderFactory;
        $this->storageDatabase = $storageDatabase;
        $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
    }

    public function execute()
    {
        $tmpPath = 'storemobilepostcodes/tmp';
        try {
            $uploader = $this->uploaderFactory->create(['fileId' => 'mobile_postcode_file']);
            $result = $this->uploadFromTmp($tmpPath, $uploader);
            if (!$result) {
                throw new \Exception('File can not be moved from temporary folder to the destination folder.');
            }

            unset($result['tmp_name'], $result['path']);

            if (isset($result['file'])) {
                $relativePath = rtrim($tmpPath, '/') . '/' . ltrim($result['file'], '/');
                $this->storageDatabase->saveFile($relativePath);
            }

            $result['cookie'] = [
                'name' => $this->_getSession()->getName(),
                'value' => $this->_getSession()->getSessionId(),
                'lifetime' => $this->_getSession()->getCookieLifetime(),
                'path' => $this->_getSession()->getCookiePath(),
                'domain' => $this->_getSession()->getCookieDomain(),
            ];
        } catch (\Exception $e) {
            $result = ['error' => $e->getMessage(), 'errorcode' => $e->getCode()];
        }
        return $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData($result);
    }

    public function uploadFromTmp($tmpPath, \Magento\MediaStorage\Model\File\Uploader $uploader)
    {
        $uploader->setAllowRenameFiles(true);
        $uploader->setFilesDispersion(true);
        $uploader->setAllowedExtensions(['csv']);
        $absoluteTmpPath = $this->mediaDirectory->getAbsolutePath($tmpPath);
        $result = $uploader->save($absoluteTmpPath);

        return $result;
    }
}
