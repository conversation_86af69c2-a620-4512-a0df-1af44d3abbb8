<?php
declare(strict_types=1);

namespace Webqem\Stores\Controller\Adminhtml\Types;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Ui\Component\MassAction\Filter;
use Webqem\Stores\Api\StoreTypeRepositoryInterface;
use Webqem\Stores\Model\ResourceModel\StoreType\CollectionFactory;
use Webqem\Stores\Model\StoreTypeRepository;

class Delete extends Action
{
    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var StoreTypeRepositoryInterface
     */
    protected $storeTypeRepositoryInterface;

    /**
     * @var StoreTypeRepository
     */
    protected $storeTypeRepository;

    /**
     * Delete constructor.
     * @param CollectionFactory $collectionFactory
     * @param Filter $filter
     * @param StoreTypeRepositoryInterface $storeTypeRepositoryInterface
     * @param StoreTypeRepository $storeTypeRepository
     * @param Context $context
     */
    public function __construct(
        CollectionFactory $collectionFactory,
        Filter $filter,
        StoreTypeRepositoryInterface $storeTypeRepositoryInterface,
        StoreTypeRepository $storeTypeRepository,
        Context $context
    ) {
        $this->collectionFactory = $collectionFactory;
        $this->filter = $filter;
        $this->storeTypeRepositoryInterface = $storeTypeRepositoryInterface;
        $this->storeTypeRepository = $storeTypeRepository;
        parent::__construct($context);
    }

    /**
     * @inheirtDoc
     */
    public function execute()
    {
        $collectFilter = $this->filter->getCollection($this->collectionFactory->create());
        $deleteRowTotal = 0;
        $err = 0;
        foreach ($collectFilter->getItems() as $item) {
            $deleteRow = $this->storeTypeRepositoryInterface->getById((int)$item['id']);
            try {
                $this->storeTypeRepository->delete($deleteRow);
                $deleteRowTotal++;
            } catch (\Exception $exception) {
                $err++;
            }
        }

        if ($deleteRowTotal) {
            $this->messageManager->addSuccessMessage(
                __('A total of %1 record(s) have been deleted.', $deleteRowTotal)
            );
        }

        if ($err) {
            $this->messageManager->addErrorMessage(
                __(
                    'A total of %1 record(s) haven\'t been deleted. Please see server logs for more details.',
                    $err
                )
            );
        }
        return $this->_redirect('stores/types/index');
    }
}
