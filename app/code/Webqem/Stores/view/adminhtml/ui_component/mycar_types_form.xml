<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">mycar_types_form.mycar_type_form_data_source</item>
            <item name="deps" xsi:type="string">mycar_types_form.mycar_type_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Mycar Store Type Information</item>
        <item name="config" xsi:type="array">
            <item name="dataScope" xsi:type="string">data</item>
            <item name="namespace" xsi:type="string">mycar_types_form</item>
        </item>
        <item name="spinner" xsi:type="string">store_type_information</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">Webqem\Stores\Block\Adminhtml\Index\Edit\Button\Back</item>
            <item name="reset" xsi:type="string">Webqem\Stores\Block\Adminhtml\Index\Edit\Button\Reset</item>
            <item name="save" xsi:type="string">Webqem\Stores\Block\Adminhtml\Index\Edit\Button\Save</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <dataSource name="mycar_type_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Webqem\Stores\Model\TypeDataProvider</argument>
            <argument name="name" xsi:type="string">mycar_type_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="stores/types/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
    <fieldset name="general_information">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">false</item>
                <item name="label" xsi:type="string" translate="true">Store Type Information</item>
                <item name="sortOrder" xsi:type="number">20</item>
            </item>
        </argument>
        <field name="type">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Type</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">type</item>
                    <item name="dataScope" xsi:type="string">type</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
