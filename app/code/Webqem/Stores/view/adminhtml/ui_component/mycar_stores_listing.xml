<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">mycar_stores_listing.mycar_stores_listing_data_source</item>
            <item name="deps" xsi:type="string">mycar_stores_listing.mycar_stores_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">spinner_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Add New Store</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/addnew</item>
            </item>
            <item name="import" xsi:type="array">
                <item name="name" xsi:type="string">import</item>
                <item name="label" xsi:type="string" translate="true">Import/Update Stores</item>
                <item name="class" xsi:type="string">secondary</item>
                <item name="url" xsi:type="string">*/*/importdata</item>
                <item name="sortOrder" xsi:type="number">20</item>
            </item>
        </item>
    </argument>

    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <filters name="listing_filters"/>
        <columnsControls name="columns_controls"/>
        <paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                </item>
            </argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">mycar_stores_listing.checker_columns.ids</item>
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="stores/grid/delete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete Row</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete
                                selected items?
                            </item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
    </listingToolbar>

    <dataSource name="mycar_stores_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">mycar_stores_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <columns name="spinner_columns">
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="label" xsi:type="string" translate="true">ID</item>
                </item>
            </argument>
        </column>
        <column name="datetime_created"
                class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date"
                >
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
            </settings>
        </column>
        <column name="datetime_updated"
                class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date"
        >
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated At</label>
            </settings>
        </column>
        <column name="store_code">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store Code</item>
                </item>
            </argument>
        </column>
        <column name="name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Name</item>
                </item>
            </argument>
        </column>
        <column name="lat">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Latitude</item>
                </item>
            </argument>
        </column>
        <column name="long">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Longitude</item>
                </item>
            </argument>
        </column>
        <column name="phone">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store Phone Number</item>
                </item>
            </argument>
        </column>
        <column name="state">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\StoreStateOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store State</item>
                </item>
            </argument>
        </column>
        <column name="postcode">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store Postcode</item>
                </item>
            </argument>
        </column>
        <column name="ev">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\Availability</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Electric Vehicle Availability</item>
                </item>
            </argument>
        </column>
        <column name="ev_charging">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\Availability</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">EV Charging Availability</item>
                </item>
            </argument>
        </column>
        <column name="enabled">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\Enabled</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store Status</item>
                </item>
            </argument>
        </column>
<!--        <column name="image1">-->
<!--            <argument name="data" xsi:type="array">-->
<!--                <item name="config" xsi:type="array">-->
<!--                    <item name="filter" xsi:type="string">text</item>-->
<!--                    <item name="editor" xsi:type="array">-->
<!--                        <item name="editorType" xsi:type="string">text</item>-->
<!--                        <item name="validation" xsi:type="array">-->
<!--                            <item name="required-entry" xsi:type="boolean">true</item>-->
<!--                        </item>-->
<!--                    </item>-->
<!--                    <item name="label" xsi:type="string" translate="true">Image 1</item>-->
<!--                </item>-->
<!--            </argument>-->
<!--        </column>-->
<!--        <column name="image2">-->
<!--            <argument name="data" xsi:type="array">-->
<!--                <item name="config" xsi:type="array">-->
<!--                    <item name="filter" xsi:type="string">text</item>-->
<!--                    <item name="editor" xsi:type="array">-->
<!--                        <item name="editorType" xsi:type="string">text</item>-->
<!--                        <item name="validation" xsi:type="array">-->
<!--                            <item name="required-entry" xsi:type="boolean">true</item>-->
<!--                        </item>-->
<!--                    </item>-->
<!--                    <item name="label" xsi:type="string" translate="true">Image 2</item>-->
<!--                </item>-->
<!--            </argument>-->
<!--        </column>-->
<!--        <column name="image3">-->
<!--            <argument name="data" xsi:type="array">-->
<!--                <item name="config" xsi:type="array">-->
<!--                    <item name="filter" xsi:type="string">text</item>-->
<!--                    <item name="editor" xsi:type="array">-->
<!--                        <item name="editorType" xsi:type="string">text</item>-->
<!--                        <item name="validation" xsi:type="array">-->
<!--                            <item name="required-entry" xsi:type="boolean">true</item>-->
<!--                        </item>-->
<!--                    </item>-->
<!--                    <item name="label" xsi:type="string" translate="true">Image 3</item>-->
<!--                </item>-->
<!--            </argument>-->
<!--        </column>-->
<!--        <column name="image4">-->
<!--            <argument name="data" xsi:type="array">-->
<!--                <item name="config" xsi:type="array">-->
<!--                    <item name="filter" xsi:type="string">text</item>-->
<!--                    <item name="editor" xsi:type="array">-->
<!--                        <item name="editorType" xsi:type="string">text</item>-->
<!--                        <item name="validation" xsi:type="array">-->
<!--                            <item name="required-entry" xsi:type="boolean">true</item>-->
<!--                        </item>-->
<!--                    </item>-->
<!--                    <item name="label" xsi:type="string" translate="true">Image 4</item>-->
<!--                </item>-->
<!--            </argument>-->
<!--        </column>-->
        <column name="store_type">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\StoreType</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store Type</item>
                </item>
            </argument>
        </column>
        <column name="has_waiting_room">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\Availability</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Has Waiting Room</item>
                </item>
            </argument>
        </column>
        <column name="has_key_dropoff">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\YesNoOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Has Key Drop Off</item>
                </item>
            </argument>
        </column>
        <column name="store_manager">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Store Manager Name</item>
                </item>
            </argument>
        </column>
        <column name="is_link_store">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\YesNoOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Is Link Store</item>
                </item>
            </argument>
        </column>
        <column name="is_dark_store">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\YesNoOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Is Dark Store</item>
                </item>
            </argument>
        </column>
        <column name="timezone">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\TimezoneOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Timezone</item>
                </item>
            </argument>
        </column>
        <column name="is_mobile">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Ui\Component\Listing\Grid\Column\YesNoOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Is Mobile Store</item>
                </item>
            </argument>
        </column>
        <actionsColumn class="Webqem\Stores\Ui\Component\Listing\Grid\Column\Action" name="actions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>
