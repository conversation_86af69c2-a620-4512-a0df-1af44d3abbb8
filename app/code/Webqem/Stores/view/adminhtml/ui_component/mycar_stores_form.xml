<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">mycar_stores_form.mycar_stores_form_data_source</item>
            <item name="deps" xsi:type="string">mycar_stores_form.mycar_stores_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Mycar Store Information</item>
        <item name="config" xsi:type="array">
            <item name="dataScope" xsi:type="string">data</item>
            <item name="namespace" xsi:type="string">mycar_stores_form</item>
        </item>
        <item name="spinner" xsi:type="string">store_information</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">Webqem\Stores\Block\Adminhtml\Index\Edit\Button\Back</item>
            <item name="reset" xsi:type="string">Webqem\Stores\Block\Adminhtml\Index\Edit\Button\Reset</item>
            <item name="save" xsi:type="string">Webqem\Stores\Block\Adminhtml\Index\Edit\Button\Save</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <dataSource name="mycar_stores_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Webqem\Stores\Model\DataProvider</argument>
            <argument name="name" xsi:type="string">mycar_stores_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="stores/grid/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
    <fieldset name="general_information">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">false</item>
                <item name="label" xsi:type="string" translate="true">Store Information</item>
                <item name="sortOrder" xsi:type="number">20</item>
            </item>
        </argument>
        <field name="enabled">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Store Status</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">enabled</item>
                </item>
            </argument>
        </field>
        <field name="store_code">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Code</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">store_code</item>
                    <item name="dataScope" xsi:type="string">store_code</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Name</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">name</item>
                    <item name="dataScope" xsi:type="string">name</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="lat">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">float</item>
                    <item name="label" xsi:type="string" translate="true">Store Latitude</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">lat</item>
                    <item name="dataScope" xsi:type="string">lat</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="long">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">float</item>
                    <item name="label" xsi:type="string" translate="true">Store Longitude</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">long</item>
                    <item name="dataScope" xsi:type="string">long</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="phone">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Phone Number</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">phone</item>
                    <item name="dataScope" xsi:type="string">phone</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="address">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Address</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">address</item>
                    <item name="dataScope" xsi:type="string">address</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="state">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Model\Config\Source\StoreStateOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store State</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">state</item>
                    <item name="dataScope" xsi:type="string">state</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="postcode">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Postcode</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">postcode</item>
                    <item name="dataScope" xsi:type="string">postcode</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="ev">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Electric Vehicle Availability</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">ev</item>
                </item>
            </argument>
        </field>
        <field name="ev_charging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">EV Charging Availability</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">ev_charging</item>
                </item>
            </argument>
        </field>
        <field name="driving_instructions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Driving Instruction</item>
                    <item name="source" xsi:type="string">driving_instructions</item>
                    <item name="dataScope" xsi:type="string">driving_instructions</item>
                    <item name="cols" xsi:type="number">15</item>
                    <item name="rows" xsi:type="number">5</item>
                    <item name="formElement" xsi:type="string">textarea</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="public_transport_instructions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">textarea</item>
                    <item name="label" xsi:type="string" translate="true">Public Transport Instructions</item>
                    <item name="source" xsi:type="string">public_transport_instructions</item>
                    <item name="dataScope" xsi:type="string">public_transport_instructions</item>
                    <item name="cols" xsi:type="number">15</item>
                    <item name="rows" xsi:type="number">5</item>
                    <item name="formElement" xsi:type="string">textarea</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="store_type">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Model\Config\Source\StoreTypeOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">int</item>
                    <item name="label" xsi:type="string" translate="true">Store Type</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">store_type</item>
                    <item name="dataScope" xsi:type="string">store_type</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="mobile_van">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Model\Config\Source\MobileVanOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">int</item>
                    <item name="label" xsi:type="string" translate="true">Mobile Van</item>
                    <item name="formElement" xsi:type="string">multiselect</item>
                    <item name="source" xsi:type="string">mobile_van</item>
                    <item name="dataScope" xsi:type="string">mobile_van</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="is_mobile_primary_van">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Mobile Primary Van</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">is_mobile_primary_van</item>
                </item>
            </argument>
        </field>
        <field name="image1">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="label" xsi:type="string" translate="true">Image 1</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="allowedExtensions" xsi:type="string">jpg jpeg png</item>
                    <item name="maxFileSize" xsi:type="number">2097152</item>
                    <item name="formElement" xsi:type="string">imageUploader</item>
                    <item name="previewTmpl" xsi:type="string">Webqem_Stores/image-preview</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="dataScope" xsi:type="string">image1</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="stores/grid/uploadimage1">
                            <param name="target_element_id">image1</param>
                            <param name="type">image</param>
                        </item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="image2">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="label" xsi:type="string" translate="true">Image 2</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="allowedExtensions" xsi:type="string">jpg jpeg png</item>
                    <item name="maxFileSize" xsi:type="number">2097152</item>
                    <item name="formElement" xsi:type="string">imageUploader</item>
                    <item name="previewTmpl" xsi:type="string">Webqem_Stores/image-preview</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="dataScope" xsi:type="string">image2</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="stores/grid/uploadimage2">
                            <param name="target_element_id">image2</param>
                            <param name="type">image</param>
                        </item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="image3">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="label" xsi:type="string" translate="true">Image 3</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="allowedExtensions" xsi:type="string">jpg jpeg png</item>
                    <item name="maxFileSize" xsi:type="number">2097152</item>
                    <item name="formElement" xsi:type="string">imageUploader</item>
                    <item name="previewTmpl" xsi:type="string">Webqem_Stores/image-preview</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="dataScope" xsi:type="string">image3</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="stores/grid/uploadimage3">
                            <param name="target_element_id">image2</param>
                            <param name="type">image</param>
                        </item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="image4">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="label" xsi:type="string" translate="true">Image 4</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="allowedExtensions" xsi:type="string">jpg jpeg png</item>
                    <item name="maxFileSize" xsi:type="number">2097152</item>
                    <item name="formElement" xsi:type="string">imageUploader</item>
                    <item name="previewTmpl" xsi:type="string">Webqem_Stores/image-preview</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="dataScope" xsi:type="string">image4</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="stores/grid/uploadimage4">
                            <param name="target_element_id">image4</param>
                            <param name="type">image</param>
                        </item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="dark_store_id">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Model\Config\Source\StoreTypeOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Dark Store</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">dark_store_id</item>
                    <item name="dataScope" xsi:type="string">dark_store_id</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="has_waiting_room">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Has Waiting Room</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">has_waiting_room</item>
                </item>
            </argument>
        </field>
        <field name="has_key_dropoff">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Has Key Drop Off</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">has_key_dropoff</item>
                </item>
            </argument>
        </field>
        <field name="store_manager">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Store Manager Name</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">storeManager</item>
                    <item name="dataScope" xsi:type="string">store_manager</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="is_dark_store">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Is Dark Store</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">is_dark_store</item>
                </item>
            </argument>
        </field>
        <field name="is_link_store">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Is Link Store</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">is_link_store</item>
                </item>
            </argument>
        </field>
        <field name="dark_store_id">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Model\Config\Source\DarkStoreOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Dark Store</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">dark_store_id</item>
                    <item name="dataScope" xsi:type="string">dark_store_id</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="timezone">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webqem\Stores\Model\Config\Source\TimezoneOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Timezone</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">timezone</item>
                    <item name="dataScope" xsi:type="string">timezone</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="is_mobile">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="label" xsi:type="string" translate="true">Is Mobile Store</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="number">1</item>
                        <item name="false" xsi:type="number">0</item>
                    </item>
                    <item name="valuesForOptions" xsi:type="array">
                        <item name="boolean" xsi:type="string">boolean</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="dataScope" xsi:type="string">is_mobile</item>
                </item>
            </argument>
        </field>
    </fieldset>
    <fieldset name="store_description">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">false</item>
                <item name="label" xsi:type="string" translate="true">Store Description</item>
            </item>
        </argument>
        <field name="description" formElement="wysiwyg" template="ui/form/field">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">page</item>
                    <item name="wysiwygConfigData" xsi:type="array">
                        <item name="is_pagebuilder_enabled" xsi:type="boolean">true</item>
                        <item name="toggle_button" xsi:type="boolean">true</item>
                        <item name="height" xsi:type="string">200px</item>
                        <item name="add_variables" xsi:type="boolean">true</item>
                        <item name="add_widgets" xsi:type="boolean">true</item>
                        <item name="add_images" xsi:type="boolean">true</item>
                        <item name="add_directives" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
            <settings>
                <dataScope>description</dataScope>
            </settings>
            <formElements>
                <wysiwyg>
                    <settings>
                        <rows>5</rows>
                        <wysiwyg>true</wysiwyg>
                    </settings>
                </wysiwyg>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="store_services">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">true</item>
                <item name="label" xsi:type="string" translate="true">Store Services</item>
            </item>
        </argument>
        <dynamicRows name="store_services">
            <settings>
                <addButtonLabel translate="true">Add Record</addButtonLabel>
                <additionalClasses>
                    <class name="admin__field-wide">true</class>
                </additionalClasses>
                <componentType>dynamicRows</componentType>
            </settings>
            <container name="record" component="Magento_Ui/js/dynamic-rows/record">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="isTemplate" xsi:type="boolean">true</item>
                        <item name="is_collection" xsi:type="boolean">true</item>
                        <item name="componentType" xsi:type="string">container</item>
                    </item>
                </argument>
                <field name="product_id" formElement="select">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="prefixName" xsi:type="string">product_id</item>
                            <item name="prefixElementName" xsi:type="string">option_</item>
                            <item name="dataScope" xsi:type="string">product_id</item>
                            <item name="fit" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string" translate="true">Service</item>
                            <item name="dataType" xsi:type="string">text</item>
                        </item>
                    </argument>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Webqem\Stores\Ui\Component\Service\Options"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <field name="available" formElement="checkbox">
                    <settings>
                        <label translate="true">Availability</label>
                        <dataType>boolean</dataType>
                    </settings>
                    <formElements>
                        <checkbox>
                            <settings>
                                <prefer>checkbox</prefer>
                                <valueMap>
                                    <map name="false" xsi:type="number">0</map>
                                    <map name="true" xsi:type="number">1</map>
                                </valueMap>
                            </settings>
                        </checkbox>
                    </formElements>
                </field>
                <actionDelete template="Magento_Backend/dynamic-rows/cells/action-delete">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="fit" xsi:type="boolean">false</item>
                        </item>
                    </argument>
                    <settings>
                        <additionalClasses>
                            <class name="some-class">true</class>
                        </additionalClasses>
                        <dataType>text</dataType>
                        <label>Actions</label>
                        <componentType>actionDelete</componentType>
                    </settings>
                </actionDelete>
            </container>
        </dynamicRows>
    </fieldset>
    <fieldset name="store_postcodes">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">true</item>
                <item name="label" xsi:type="string" translate="true">Store Postcodes</item>
            </item>
        </argument>
        <field name="import">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string">Import Postcode</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="formElement" xsi:type="string">fileUploader</item>
                    <item name="notice" xsi:type="string" translate="true">Allowed file types: csv.</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="stores/grid/uploadpostcode"/>
                    </item>
                    <item name="fileInputName" xsi:type="string">mobile_postcode_file</item>
                </item>
            </argument>
        </field>
        <dynamicRows name="store_postcodes">
            <settings>
                <addButtonLabel translate="true">Add Record</addButtonLabel>
                <additionalClasses>
                    <class name="admin__field-wide">true</class>
                </additionalClasses>
                <componentType>dynamicRows</componentType>
            </settings>
            <container name="record" component="Magento_Ui/js/dynamic-rows/record">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="isTemplate" xsi:type="boolean">true</item>
                        <item name="is_collection" xsi:type="boolean">true</item>
                        <item name="componentType" xsi:type="string">container</item>
                    </item>
                </argument>
                <field name="postcode" formElement="input">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="fit" xsi:type="boolean">false</item>
                        </item>
                    </argument>
                    <settings>
                        <validation>
                            <rule name="required-entry" xsi:type="boolean">true</rule>
                        </validation>
                        <dataType>int</dataType>
                        <label>Postcode</label>
                    </settings>
                </field>
                <actionDelete template="Magento_Backend/dynamic-rows/cells/action-delete">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="fit" xsi:type="boolean">false</item>
                        </item>
                    </argument>
                    <settings>
                        <additionalClasses>
                            <class name="some-class">true</class>
                        </additionalClasses>
                        <dataType>text</dataType>
                        <label>Actions</label>
                        <componentType>actionDelete</componentType>
                    </settings>
                </actionDelete>
            </container>
        </dynamicRows>
    </fieldset>
    <fieldset name="store_openingtime">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">true</item>
                <item name="label" xsi:type="string" translate="true">Store Opening Time</item>
            </item>
        </argument>
        <dynamicRows name="store_openingtime">
            <settings>
                <addButtonLabel translate="true">Add Record</addButtonLabel>
                <additionalClasses>
                    <class name="admin__field-wide">true</class>
                </additionalClasses>
                <componentType>dynamicRows</componentType>
            </settings>
            <container name="record" component="Magento_Ui/js/dynamic-rows/record">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="isTemplate" xsi:type="boolean">true</item>
                        <item name="is_collection" xsi:type="boolean">true</item>
                        <item name="componentType" xsi:type="string">container</item>
                    </item>
                </argument>
                <field name="day" formElement="select">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="prefixName" xsi:type="string">day</item>
                            <item name="prefixElementName" xsi:type="string">option_</item>
                            <item name="dataScope" xsi:type="string">day</item>
                            <item name="fit" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string" translate="true">Day</item>
                            <item name="dataType" xsi:type="string">text</item>
                        </item>
                    </argument>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Webqem\Stores\Ui\Component\Service\Day"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <field name="opening_time" formElement="select">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="prefixName" xsi:type="string">opening_time</item>
                            <item name="prefixElementName" xsi:type="string">option_</item>
                            <item name="dataScope" xsi:type="string">opening_time</item>
                            <item name="fit" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string" translate="true">Opening Time</item>
                            <item name="dataType" xsi:type="string">text</item>
                        </item>
                    </argument>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Webqem\Stores\Ui\Component\Service\OpeningTime"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <field name="closing_time" formElement="select">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="prefixName" xsi:type="string">closing_time</item>
                            <item name="prefixElementName" xsi:type="string">option_</item>
                            <item name="dataScope" xsi:type="string">closing_time</item>
                            <item name="fit" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string" translate="true">Closing Time</item>
                            <item name="dataType" xsi:type="string">text</item>
                        </item>
                    </argument>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Webqem\Stores\Ui\Component\Service\ClosingTime"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <actionDelete template="Magento_Backend/dynamic-rows/cells/action-delete">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="fit" xsi:type="boolean">false</item>
                        </item>
                    </argument>
                    <settings>
                        <additionalClasses>
                            <class name="some-class">true</class>
                        </additionalClasses>
                        <dataType>text</dataType>
                        <label>Actions</label>
                        <componentType>actionDelete</componentType>
                    </settings>
                </actionDelete>
            </container>
        </dynamicRows>
    </fieldset>
</form>
