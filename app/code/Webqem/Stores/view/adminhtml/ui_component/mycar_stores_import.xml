<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">mycar_stores_import.mycar_stores_import_data_source</item>
            <item name="deps" xsi:type="string">mycar_stores_import.mycar_stores_import_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">General Information</item>
        <item name="config" xsi:type="array">
            <item name="dataScope" xsi:type="string">data</item>
            <item name="namespace" xsi:type="string">mycar_stores_import</item>
        </item>
        <item name="spinner" xsi:type="string">general_information</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">Webqem\Stores\Block\Adminhtml\ImportStore\Edit\Button\Back</item>
            <item name="reset" xsi:type="string">Webqem\Stores\Block\Adminhtml\ImportStore\Edit\Button\Reset</item>
            <item name="save" xsi:type="string">Webqem\Stores\Block\Adminhtml\ImportStore\Edit\Button\SaveImportData</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <dataSource name="mycar_stores_import_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Webqem\Stores\Model\StoreImport\DataProvider</argument>
            <argument name="name" xsi:type="string">mycar_stores_import_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
              <item name="config" xsi:type="array">
                 <item name="submit_url" xsi:type="url" path="*/*/saveimportdata"/>
              </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
    <fieldset name="general_information">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="collapsible" xsi:type="boolean">false</item>
                <item name="label" xsi:type="string" translate="true"></item>
                <item name="sortOrder" xsi:type="number">20</item>
            </item>
        </argument>
        <field name="importfile">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="source" xsi:type="string">importfile</item>
                    <item name="label" xsi:type="string" translate="true">Upload File</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="allowedExtensions" xsi:type="string">csv xls</item>
                    <item name="maxFileSize" xsi:type="number">2097152</item>
                    <item name="formElement" xsi:type="string">fileUploader</item>
                    <item name="dataScope" xsi:type="string">importfile</item>
                    <item name="notice" xsi:type="string" translate="true">Allow File Types:-  .csv and .xls</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="stores/grid/import" >
                            <param name="target_element_id">importfile</param>
                            <param name="type">image</param>
                        </item>
                    </item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
