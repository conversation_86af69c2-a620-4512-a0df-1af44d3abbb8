<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="../../../../../../../lib/internal/Magento/Framework/View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <block class="Webqem\Stores\Block\Index\View" name="store_locations" template="Webqem_Stores::store_state_locations.phtml"/>
        </referenceContainer>
    </body>
</page>
