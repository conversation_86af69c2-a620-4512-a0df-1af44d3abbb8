<?php
namespace Webqem\Stores\Model;

use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttributesFactory;
use Magento\Framework\Model\AbstractModel;
use Webqem\Stores\Model\ResourceModel\StoreType as StoreTypeResource;
use Magento\Framework\Model\Context;

class StoreType extends \Magento\Framework\Model\AbstractExtensibleModel implements \Webqem\Stores\Api\Data\StoreTypeInterface
{
    /**
     * @param Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ExtensionAttributesFactory $extensionFactory
     * @param AttributeValueFactory $customAttributeFactory
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Registry $registry,
        ExtensionAttributesFactory $extensionFactory, AttributeValueFactory $customAttributeFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    )
    {
        parent::__construct($context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $resource,
            $resourceCollection,
            $data
        );
    }

    /**
     * Store Model constructor
     */
    protected $_idFieldName = 'id';

    protected $_resourceModel = StoreTypeResource::class;

    /**
     * Define the table name for the model
     */
    protected $_table = 'mycar_store_type';

    /**
     * Store constructor
     */
    protected function _construct()
    {
        $this->_init(StoreTypeResource::class);
        parent::_construct();
    }

    public function getType()
    {
        return $this->getData('type');
    }

    public function setType($type)
    {
        return $this->setData('type', $type);
    }
}
