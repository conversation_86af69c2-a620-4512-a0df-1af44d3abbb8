<?php
namespace Webqem\Stores\Model;

use Webqem\Stores\Api\Data\StoreTypeInterface;
use Webqem\Stores\Api\StoreTypeRepositoryInterface;
use Webqem\Stores\Model\ResourceModel\StoreType as StoreTypeResource;
use Webqem\Stores\Model\StoreTypeFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Api\SearchResultsFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Webqem\Stores\Model\ResourceModel\StoreType\CollectionFactory as StoreTypeCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

class StoreTypeRepository implements StoreTypeRepositoryInterface
{
    /**
     * @var StoreTypeFactory
     */
    protected $storeTypeFactory;

    /**
     * @var StoreTypeResource
     */
    protected $storeTypeResource;

    /**
     * @var SearchResultsFactory
     */
    protected $searchResultsFactory;

    /**
     * @var StoreTypeCollectionFactory
     */
    protected $storeTypeCollectionFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;


    public function __construct(
        StoreTypeFactory $storeTypeFactory,
        StoreTypeResource $storeTypeResource,
        SearchResultsFactory $searchResultsFactory,
        StoreTypeCollectionFactory $storeTypeCollectionFactory,
        CollectionProcessorInterface $collectionProcessor,
        SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->storeTypeFactory = $storeTypeFactory;
        $this->storeTypeResource = $storeTypeResource;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->storeTypeCollectionFactory = $storeTypeCollectionFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * Save store type data
     *
     * @param StoreTypeInterface $storeType
     * @return StoreTypeInterface
     * @throws LocalizedException
     */
    public function save(StoreTypeInterface $storeType): StoreTypeInterface
    {
        try {
            $this->storeTypeResource->save($storeType);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not save the storeType: %1', $e->getMessage()));
        }
        return $storeType;
    }

    /**
     * Get store type by ID
     *
     * @param int $storeTypeId
     * @return StoreStoreTypeInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $storeTypeId): StoreTypeInterface
    {
        $storeType = $this->storeTypeFactory->create();
        $this->storeTypeResource->load($storeType, $storeTypeId);

        if (!$storeType->getId()) {
            throw new NoSuchEntityException(__('Store Type with ID "%1" does not exist.', $storeTypeId));
        }

        return $storeType;
    }

    public function getByType($type)
    {
        $storeStoreTypeCollection = $this->storeTypeCollectionFactory->create()
            ->addFieldToFilter('type', $type);
        return $storeStoreTypeCollection->getFirstItem();
    }

    /**
     * Delete store
     *
     * @param StoreTypeInterface $store
     * @return bool
     * @throws LocalizedException
     */
    public function delete(StoreTypeInterface $storeType)
    {
        try {
            $this->storeTypeResource->delete($storeType);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not delete the storeType: %1', $e->getMessage()));
        }
        return true;
    }
}
