<?php
namespace Webqem\Stores\Model;

use Webqem\Stores\Api\Data\StoreStoreTypeInterface;
use Webqem\Stores\Api\StoreStoreTypeRepositoryInterface;
use Webqem\Stores\Model\ResourceModel\StoreStoreType as StoreStoreTypeResource;
use Webqem\Stores\Model\StoreStoreTypeFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Api\SearchResultsFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Webqem\Stores\Model\ResourceModel\StoreStoreType\CollectionFactory as StoreStoreTypeCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

class StoreStoreTypeRepository implements StoreStoreTypeRepositoryInterface
{
    /**
     * @var StoreStoreTypeFactory
     */
    protected $storeStoreTypeFactory;

    /**
     * @var StoreStoreTypeResource
     */
    protected $storeStoreTypeResource;

    /**
     * @var SearchResultsFactory
     */
    protected $searchResultsFactory;

    /**
     * @var StoreStoreTypeCollectionFactory
     */
    protected $storeStoreTypeCollectionFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;


    public function __construct(
        StoreStoreTypeFactory $storeStoreTypeFactory,
        StoreStoreTypeResource $storeStoreTypeResource,
        SearchResultsFactory $searchResultsFactory,
        StoreStoreTypeCollectionFactory $storeStoreTypeCollectionFactory,
        CollectionProcessorInterface $collectionProcessor,
        SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeStoreTypeResource = $storeStoreTypeResource;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->storeStoreTypeCollectionFactory = $storeStoreTypeCollectionFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * Save store opening time data
     *
     * @param StoreStoreTypeInterface $storeStoreType
     * @return StoreStoreTypeInterface
     * @throws LocalizedException
     */
    public function save(StoreStoreTypeInterface $storeStoreType): StoreStoreTypeInterface
    {
        try {
            $this->storeStoreTypeResource->save($storeStoreType);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not save the storeStoreType: %1', $e->getMessage()));
        }
        return $storeStoreType;
    }

    /**
     * Get store store type by ID
     *
     * @param int $storeStoreTypeId
     * @return StoreStoreTypeInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $storeStoreTypeId): StoreStoreTypeInterface
    {
        $storeStoreType = $this->storeStoreTypeFactory->create();
        $this->storeStoreTypeResource->load($storeStoreType, $storeStoreTypeId);

        if (!$storeStoreType->getId()) {
            throw new NoSuchEntityException(__('Store Store Type with ID "%1" does not exist.', $storeStoreTypeId));
        }

        return $storeStoreType;
    }

    public function getStoreStoreTypeByStoreId($storeId)
    {
        $storeStoreTypeCollection = $this->storeStoreTypeCollectionFactory->create()
            ->addFieldToFilter('store_id', $storeId);
        return $storeStoreTypeCollection->getFirstItem();
    }

    /**
     * Delete store
     *
     * @param StoreStoreTypeInterface $storeStoreType
     * @return bool
     * @throws LocalizedException
     */
    public function delete(StoreStoreTypeInterface $storeStoreType)
    {
        try {
            $this->storeStoreTypeResource->delete($storeStoreType);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not delete the storeService: %1', $e->getMessage()));
        }
        return true;
    }
}
