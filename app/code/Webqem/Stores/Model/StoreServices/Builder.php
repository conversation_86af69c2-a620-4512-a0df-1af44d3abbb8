<?php
namespace Webqem\Stores\Model\StoreServices;

use Webqem\Stores\Model\StoreServices;
use Webqem\Stores\Model\StoreServicesFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\DataObject\Copy;

/**
 * Class Builder
 */
class Builder
{
    /**
     * @var Link
     */
    private $component;

    /**
     * @var Copy
     */
    private $objectCopyService;

    /**
     * @var DataObjectHelper
     */
    private $dataObjectHelper;

    /**
     * @var LinkFactory
     */
    private $componentFactory;

    /**
     * @var array
     */
    private $data = [];

    /**
     * Mapper constructor.
     *
     * @param Copy $objectCopyService
     * @param DataObjectHelper $dataObjectHelper
     * @param StoreServicesFactory $componentFactory
     */
    public function __construct(
        Copy $objectCopyService,
        DataObjectHelper $dataObjectHelper,
        StoreServicesFactory $componentFactory
    ) {
        $this->objectCopyService = $objectCopyService;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->componentFactory = $componentFactory;
    }

    /**
     * @param array $data
     * @return $this
     */
    public function setData(array $data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @param \Webqem\Stores\Api\Data\StoreServicesInterface $storeService
     * @return \Webqem\Stores\Api\Data\StoreServicesInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function build(\Webqem\Stores\Api\Data\StoreServicesInterface $storeService)
    {
        $this->dataObjectHelper->populateWithArray(
            $storeService,
            $this->data,
            \Webqem\Stores\Api\Data\StoreServicesInterface::class
        );


        $this->resetData();

        return $storeService;
    }


    /**
     * @return void
     */
    private function resetData()
    {
        $this->data = [];
    }

    /**
     * @return Link
     */
    private function getComponent()
    {
        if (!$this->component) {
            $this->component = $this->componentFactory->create();
        }
        return $this->component;
    }
}
