<?php
namespace Webqem\Stores\Model;

use Webqem\Stores\Api\Data\StoreOpeningTimeInterface;
use Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface;
use Webqem\Stores\Model\ResourceModel\StoreOpeningTime as StoreOpeningTimeResource;
use Webqem\Stores\Model\StoreOpeningTimeFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Api\SearchResultsFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Webqem\Stores\Model\ResourceModel\StoreOpeningTime\CollectionFactory as StoreOpeningTimeCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

class StoreOpeningTimeRepository implements StoreOpeningTimeRepositoryInterface
{
    /**
     * @var StoreOpeningTimeFactory
     */
    protected $storeOpeningTimeFactory;

    /**
     * @var StoreOpeningTimeResource
     */
    protected $storeOpeningTimeResource;

    /**
     * @var SearchResultsFactory
     */
    protected $searchResultsFactory;

    /**
     * @var StoreOpeningTimeCollectionFactory
     */
    protected $storeOpeningTimeCollectionFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;


    public function __construct(
        StoreOpeningTimeFactory $storeOpeningTimeFactory,
        StoreOpeningTimeResource $storeOpeningTimeResource,
        SearchResultsFactory $searchResultsFactory,
        StoreOpeningTimeCollectionFactory $storeOpeningTimeCollectionFactory,
        CollectionProcessorInterface $collectionProcessor,
        SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->storeOpeningTimeFactory = $storeOpeningTimeFactory;
        $this->storeOpeningTimeResource = $storeOpeningTimeResource;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->storeOpeningTimeCollectionFactory = $storeOpeningTimeCollectionFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * Save store opening time data
     *
     * @param StoreOpeningTimeInterface $storeOpeningTime
     * @return StoreOpeningTimeInterface
     * @throws LocalizedException
     */
    public function save(StoreOpeningTimeInterface $storeOpeningTime): StoreOpeningTimeInterface
    {
        try {
            $this->storeOpeningTimeResource->save($storeOpeningTime);
        } catch (\Exception $e) {
            var_dump($e->getMessage());die;
            throw new LocalizedException(__('Could not save the storeOpeningTime: %1', $e->getMessage()));
        }
        return $storeOpeningTime;
    }

    /**
     * Get store opening time by ID
     *
     * @param int $storeOpeningTimeId
     * @return StoreOpeningTimeInterface
     * @throws NoSuchEntityException
     */
    public function getById($storeOpeningTimeId): StoreOpeningTimeInterface
    {
        $storeOpeningTime = $this->storeOpeningTimeFactory->create();
        $this->storeOpeningTimeResource->load($storeOpeningTime, $storeOpeningTimeId);

        if (!$storeOpeningTime->getId()) {
            throw new NoSuchEntityException(__('Store Opening Time with ID "%1" does not exist.', $storeOpeningTimeId));
        }

        return $storeOpeningTime;
    }

    /**
     * @param $storeId
     * @return \Magento\Framework\DataObject[]|StoreOpeningTimeInterface[]
     */
    public function getStoreOpeningTimeByStoreId($storeId)
    {
        $storeServicesCollection = $this->storeOpeningTimeCollectionFactory->create()
            ->addFieldToFilter('store_id', $storeId);
        return $storeServicesCollection->getItems();
    }

    /**
     * Delete store
     *
     * @param StoreOpeningTimeInterface $storeOpeningTime
     * @return bool
     * @throws LocalizedException
     */
    public function delete(StoreOpeningTimeInterface $storeOpeningTime)
    {
        try {
            $this->storeOpeningTimeResource->delete($storeOpeningTime);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not delete the storeOpeningTime: %1', $e->getMessage()));
        }
        return true;
    }
}
