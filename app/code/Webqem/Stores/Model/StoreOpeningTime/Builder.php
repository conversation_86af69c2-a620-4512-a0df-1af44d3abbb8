<?php
namespace Webqem\Stores\Model\StoreOpeningTime;

use Webqem\Stores\Model\StoreOpeningTime;
use Webqem\Stores\Model\StoreOpeningTimeFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\DataObject\Copy;

/**
 * Class Builder
 */
class Builder
{
    /**
     * @var Link
     */
    private $component;

    /**
     * @var Copy
     */
    private $objectCopyService;

    /**
     * @var DataObjectHelper
     */
    private $dataObjectHelper;

    /**
     * @var LinkFactory
     */
    private $componentFactory;

    /**
     * @var array
     */
    private $data = [];

    /**
     * Mapper constructor.
     *
     * @param Copy $objectCopyService
     * @param DataObjectHelper $dataObjectHelper
     * @param StoreOpeningTimeFactory $componentFactory
     */
    public function __construct(
        Copy $objectCopyService,
        DataObjectHelper $dataObjectHelper,
        StoreOpeningTimeFactory $componentFactory
    ) {
        $this->objectCopyService = $objectCopyService;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->componentFactory = $componentFactory;
    }

    /**
     * @param array $data
     * @return $this
     */
    public function setData(array $data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @param \Webqem\Stores\Api\Data\StoreOpeningTimeInterface $storeOpeningTime
     * @return \Webqem\Stores\Api\Data\StoreOpeningTimeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function build(\Webqem\Stores\Api\Data\StoreOpeningTimeInterface $storeOpeningTime)
    {
        $this->dataObjectHelper->populateWithArray(
            $storeOpeningTime,
            $this->data,
            \Webqem\Stores\Api\Data\StoreOpeningTimeInterface::class
        );


        $this->resetData();

        return $storeOpeningTime;
    }


    /**
     * @return void
     */
    private function resetData()
    {
        $this->data = [];
    }

    /**
     * @return Link
     */
    private function getComponent()
    {
        if (!$this->component) {
            $this->component = $this->componentFactory->create();
        }
        return $this->component;
    }
}
