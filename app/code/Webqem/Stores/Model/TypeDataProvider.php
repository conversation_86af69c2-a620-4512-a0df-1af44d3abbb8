<?php

namespace Webqem\Stores\Model;

use Webqem\Stores\Model\ResourceModel\StoreType\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Store\Model\StoreManagerInterface;

class TypeDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var $_loadedData
     */
    protected $_loadedData;

    /**
     * @var ResourceModel\Store\Collection
     */
    protected $collection;

    protected $storeManager;

    protected $resource;

    protected $dataPersistor;

    /**
     * DataProvider constructor.
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        StoreManagerInterface $storeManager,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->storeManager = $storeManager;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * @return array
     */
    public function getData()
    {

        if (isset($this->_loadedData)) {
            return $this->_loadedData;
        }
        $items = $this->collection->getItems();
        foreach ($items as $item) {
            $this->_loadedData[$item->getId()] = $item->getData();
        }
        $data = $this->dataPersistor->get('mycar_store_type');

        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->_loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('mycar_store_type');
        }
        return $this->_loadedData;
    }
}
