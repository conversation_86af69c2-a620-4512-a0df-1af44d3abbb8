<?php
namespace Webqem\Stores\Model;

use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttributesFactory;
use Magento\Framework\Model\AbstractModel;
use Webqem\Stores\Model\ResourceModel\StoreOpeningTime as StoreOpeningTimeResource;
use Magento\Framework\Model\Context;

class StoreOpeningTime extends \Magento\Framework\Model\AbstractExtensibleModel implements \Webqem\Stores\Api\Data\StoreOpeningTimeInterface
{
    /**
     * @param Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ExtensionAttributesFactory $extensionFactory
     * @param AttributeValueFactory $customAttributeFactory
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Registry $registry,
        ExtensionAttributesFactory $extensionFactory, AttributeValueFactory $customAttributeFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    )
    {
        parent::__construct($context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $resource,
            $resourceCollection,
            $data
        );
    }

    /**
     * Store Model constructor
     */
    protected $_idFieldName = 'id';

    protected $_resourceModel = StoreOpeningTimeResource::class;

    /**
     * Define the table name for the model
     */
    protected $_table = 'mycar_stores_opening_times';

    /**
     * Store constructor
     */
    protected function _construct()
    {
        $this->_init(StoreOpeningTimeResource::class);
        parent::_construct();
    }

    public function getStoreId()
    {
        return $this->getData('store_id');
    }

    public function setStoreId($storeId)
    {
        return $this->setData('store_id', $storeId);
    }

    public function getDay()
    {
        return $this->getData('day');
    }

    public function setDay($day)
    {
        return $this->setData('day', $day);
    }

    public function getOpeningTime()
    {
        return $this->getData('opening_time');
    }

    public function setOpeningTime($openingTime)
    {
        return $this->setData('opening_time', $openingTime);
    }

    public function getClosingTime()
    {
        return $this->getData('closing_time');
    }

    public function setClosingTime($closingTime)
    {
        return $this->setData('closing_time', $closingTime);
    }
}
