<?php
declare(strict_types=1);

namespace Webqem\Stores\Model\Config\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory;
use Magento\Directory\Model\Country;

class StoreStateOptions extends AbstractSource
{
    protected $country;
    /**
     * @param Country $timezone
     */
    public function __construct(
        Country $country
    ) {
        $this->country = $country;
    }

    /**
     * @return array|null
     */
    public function getAllOptions()
    {
        $regionCollection = $this->country->loadByCode('AU')->getRegions();
        $regions = $regionCollection->loadData()->toOptionArray();
        return $regions;
    }
}
