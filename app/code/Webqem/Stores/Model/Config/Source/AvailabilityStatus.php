<?php
declare(strict_types=1);

namespace Webqem\Stores\Model\Config\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class AvailabilityStatus extends AbstractSource
{
    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions()
    {
        $this->_options =
            [
                ['label' => __('Not Available'), 'value' => 0],
                ['label' => __('Available'), 'value' => 1]
            ];
        return $this->_options;
    }
}
