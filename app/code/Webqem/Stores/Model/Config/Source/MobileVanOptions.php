<?php
declare(strict_types=1);

namespace Webqem\Stores\Model\Config\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Webqem\Stores\Model\ResourceModel\Store\CollectionFactory;

class MobileVanOptions extends AbstractSource
{
    protected $collectionFactory;

    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions()
    {
        $mobileStoreList = [];
        $storeCollection = $this->collectionFactory->create()
            ->addFieldToSelect('*')
            ->addFieldToFilter('is_mobile', 1);
        foreach ($storeCollection as $store) {
            $mobileStoreList[] = [
                'value' => $store->getId(),
                'label' => $store->getName()
            ];
        }
        $this->_options = $mobileStoreList;
        return $this->_options;
    }
}
