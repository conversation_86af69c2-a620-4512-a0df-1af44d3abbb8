<?php
declare(strict_types=1);

namespace Webqem\Stores\Model\Config\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Webqem\Stores\Model\ResourceModel\StoreType\CollectionFactory;
class StoreTypeOptions extends AbstractSource
{
    protected $collectionFactory;

    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions()
    {
        $storeTypeList[] = [
            'value' => null,
            'label' => 'Select a store type'
        ];
        $storeTypeCollection = $this->collectionFactory->create()
            ->addFieldToSelect('*');
        foreach ($storeTypeCollection as $storeType) {
            $storeTypeList[] = [
                'value' => $storeType->getId(),
                'label' => $storeType->getType()
            ];
        }
        $this->_options = $storeTypeList;
        return $this->_options;
    }
}
