<?php
declare(strict_types=1);

namespace Webqem\Stores\Model\Config\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Webqem\Stores\Model\ResourceModel\Store\CollectionFactory;
class DarkStoreOptions extends AbstractSource
{
    protected $collectionFactory;

    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions()
    {
        $darkStoreList[] = [
            'value' => null,
            'label' => 'Select a dark store'
        ];
        $darkStoreCollection = $this->collectionFactory->create()
            ->addFieldToSelect('*')
            ->addFieldToFilter('is_dark_store', 1);
        foreach ($darkStoreCollection as $darkStore) {
            $darkStoreList[] = [
                'value' => $darkStore->getId(),
                'label' => $darkStore->getName()
            ];
        }
        $this->_options = $darkStoreList;
        return $this->_options;
    }
}
