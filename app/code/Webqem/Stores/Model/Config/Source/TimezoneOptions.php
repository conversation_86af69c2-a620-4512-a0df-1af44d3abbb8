<?php
declare(strict_types=1);

namespace Webqem\Stores\Model\Config\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory;

class TimezoneOptions extends AbstractSource
{
    protected $timezone;
    /**
     * @param \Magento\Config\Model\Config\Source\Locale\Timezone $timezone
     */
    public function __construct(
        \Magento\Config\Model\Config\Source\Locale\Timezone $timezone
    ) {
        $this->timezone = $timezone;
    }

    /**
     * @return array|null
     */
    public function getAllOptions()
    {
        $allTimeZone = $this->timezone->toOptionArray();
        foreach ($allTimeZone as $item) {
            if (str_contains($item['value'], 'Australia')) {
                $this->_options[] = ['label' => $item['label'], 'value' => $item['value']];
            }
        }
        return $this->_options;
    }
}
