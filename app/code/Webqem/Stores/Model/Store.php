<?php
namespace Webqem\Stores\Model;

use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttributesFactory;
use Magento\Store\Api\Data\StoreExtensionInterface;
use Magento\Framework\Model\AbstractModel;
use Webqem\Stores\Model\ResourceModel\Store as StoreResource;
use Magento\Framework\Model\Context;

class Store extends \Magento\Framework\Model\AbstractExtensibleModel implements \Webqem\Stores\Api\Data\StoreInterface
{
    /**
     * @param Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ExtensionAttributesFactory $extensionFactory
     * @param AttributeValueFactory $customAttributeFactory
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Registry $registry,
        ExtensionAttributesFactory $extensionFactory, AttributeValueFactory $customAttributeFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    )
    {
        parent::__construct($context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $resource,
            $resourceCollection,
            $data
        );
    }

    /**
     * Store Model constructor
     */
    protected $_idFieldName = 'id';

    protected $_resourceModel = StoreResource::class;

    /**
     * Define the table name for the model
     */
    protected $_table = 'mycar_stores';

    /**
     * Store constructor
     */
    protected function _construct()
    {
        $this->_init(StoreResource::class);
        parent::_construct();
    }

    /**
     * @return array|int|mixed|null
     */
    public function getId()
    {
        return $this->getData('id');
    }

    /**
     * @param $id
     * @return Store
     */
    public function setId($id)
    {
        return $this->setData('id', $id);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getStoreCode()
    {
        return $this->getData('store_code');
    }

    /**
     * @param $storeCode
     * @return Store
     */
    public function setStoreCode($storeCode)
    {
        return $this->setData('store_code', $storeCode);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getName()
    {
        return $this->getData('name');
    }

    /**
     * @param $name
     * @return Store
     */
    public function setName($name)
    {
        return $this->setData('name', $name);
    }

    /**
     * @return array|float|mixed|null
     */
    public function getLat()
    {
        return $this->getData('lat');
    }

    /**
     * @param $lat
     * @return Store
     */
    public function setLat($lat)
    {
        return $this->setData('lat', $lat);
    }

    /**
     * @return array|float|mixed|null
     */
    public function getLong()
    {
        return $this->getData('long');
    }

    /**
     * @param $long
     * @return Store
     */
    public function setLong($long)
    {
        return $this->setData('long', $long);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getDescription()
    {
        return $this->getData('description');
    }

    /**
     * @param $description
     * @return Store
     */
    public function setDescription($description)
    {
        return $this->setData('description', $description);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getPhone()
    {
        return $this->getData('phone');
    }

    /**
     * @param $phone
     * @return Store
     */
    public function setPhone($phone)
    {
        return $this->setData('phone', $phone);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getAddress()
    {
        return $this->getData('address');
    }

    /**
     * @param $address
     * @return Store
     */
    public function setAddress($address)
    {
        return $this->setData('address', $address);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getState()
    {
        return $this->getData('state');
    }

    /**
     * @param $state
     * @return Store
     */
    public function setState($state)
    {
        return $this->setData('state', $state);
    }

    /**
     * @return array|string|mixed|null
     */
    public function getPostcode()
    {
        return $this->getData('postcode');
    }

    /**
     * @param $postcode
     * @return Store
     */
    public function setPostcode($postcode)
    {
        return $this->setData('postcode', $postcode);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getEv()
    {
        return $this->getData('ev');
    }

    /**
     * @param $ev
     * @return Store
     */
    public function setEv($ev)
    {
        return $this->setData('ev', $ev);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getEvCharging()
    {
        return $this->getData('ev_charging');
    }

    /**
     * @param $evCharging
     * @return Store
     */
    public function setEvCharging($evCharging)
    {
        return $this->setData('ev_charging', $evCharging);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getEnabled()
    {
        return $this->getData('enabled');
    }

    /**
     * @param $enabled
     * @return Store
     */
    public function setEnabled($enabled)
    {
        return $this->setData('enabled', $enabled);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getDrivingInstructions()
    {
        return $this->getData('driving_instructions');
    }

    /**
     * @param $drivingInstructions
     * @return Store
     */
    public function setDrivingInstructions($drivingInstructions)
    {
        return $this->setData('driving_instructions', $drivingInstructions);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getPublicTransportInstructions()
    {
        return $this->getData('public_transport_instructions');
    }

    /**
     * @param $publicTransportInstructions
     * @return Store
     */
    public function setPublicTransportInstructions($publicTransportInstructions)
    {
        return $this->setData('public_transport_instructions', $publicTransportInstructions);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getImage1()
    {
        return $this->getData('image1');
    }

    /**
     * @param $image1
     * @return Store
     */
    public function setImage1($image1)
    {
        return $this->setData('image1', $image1);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getImage2()
    {
        return $this->getData('image2');
    }

    /**
     * @param $image2
     * @return Store
     */
    public function setImage2($image2)
    {
        return $this->setData('image2', $image2);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getImage3()
    {
        return $this->getData('image3');
    }

    /**
     * @param $image3
     * @return Store
     */
    public function setImage3($image3)
    {
        return $this->setData('image3', $image3);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getImage4()
    {
        return $this->getData('image4');
    }

    /**
     * @param $image4
     * @return Store
     */
    public function setImage4($image4)
    {
        return $this->setData('image4', $image4);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getStoreType()
    {
        return $this->getData('store_type');
    }

    /**
     * @param $storeType
     * @return Store
     */
    public function setStoreType($storeType)
    {
        return $this->setData('store_type', $storeType);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getHasWaitingRoom()
    {
        return $this->getData('has_waiting_room');
    }

    /**
     * @param $hasWaitingRoom
     * @return Store
     */
    public function setHasWaitingRoom($hasWaitingRoom)
    {
        return $this->setData('has_waiting_room', $hasWaitingRoom);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getHasKeyDropoff()
    {
        return $this->getData('has_key_dropoff');
    }

    /**
     * @param $hasKeyDropOff
     * @return Store
     */
    public function setHasKeyDropoff($hasKeyDropOff)
    {
        return $this->setData('has_key_dropoff', $hasKeyDropOff);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getStoreManager()
    {
        return $this->getData('store_manager');
    }

    /**
     * @param $storeManager
     * @return Store
     */
    public function setStoreManager($storeManager)
    {
        return $this->setData('store_manager', $storeManager);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getIsLinkStore()
    {
        return $this->getData('is_link_store');
    }

    /**
     * @param $isLinkStore
     * @return Store
     */
    public function setIsLinkStore($isLinkStore)
    {
        return $this->setData('is_link_store', $isLinkStore);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getIsDarkStore()
    {
        return $this->getData('is_dark_store');
    }

    /**
     * @param $isDarkStore
     * @return Store
     */
    public function setIsDarkStore($isDarkStore)
    {
        return $this->setData('is_dark_store', $isDarkStore);
    }

    /**
     * @return array|mixed|string|null
     */
    public function getTimezone()
    {
        return $this->getData('timezone');
    }

    /**
     * @param $timeZone
     * @return Store
     */
    public function setTimezone($timeZone)
    {
        return $this->setData('timezone', $timeZone);
    }

    /**
     * @return array|int|mixed|null
     */
    public function getIsMobile()
    {
        return $this->getData('is_mobile');
    }

    /**
     * @param $isMobile
     * @return Store
     */
    public function setIsMobile($isMobile)
    {
        return $this->setData('is_mobile', $isMobile);
    }

    /**
     * Get the datetime created field
     *
     * @return string
     */
    public function getDatetimeCreated()
    {
        return $this->getData('datetime_created');
    }

    /**
     * Get the datetime updated field
     *
     * @return string
     */
    public function getDatetimeUpdated()
    {
        return $this->getData('datetime_updated');
    }

    /**
     * @param $datetimeCreated
     * @return Store
     */
    public function setDatetimeCreated($datetimeCreated)
    {
        return $this->setData('datetime_created', $datetimeCreated);
    }

    /**
     * @param $datetimeUpdated
     * @return Store
     */
    public function setDatetimeUpdated($datetimeUpdated)
    {
        return $this->setData('datetime_updated', $datetimeUpdated);
    }

    public function getDarkStoreId()
    {
        return $this->getData('dark_store_id');
    }

    public function setDarkStoreId($darkStoreId)
    {
        return $this->setData('dark_store_id', $darkStoreId);
    }

    public function getMobileVan()
    {
        return $this->getData('mobile_van');
    }

    public function setMobileVan($mobileVan)
    {
        return $this->setData('mobile_van', $mobileVan);
    }

    public function getIsMobilePrimaryVan()
    {
        return $this->getData('is_mobile_primary_van');
    }

    public function setIsMobilePrimaryVan($isMobilePrimaryVan)
    {
        return $this->setData('is_mobile_primary_van', $isMobilePrimaryVan);
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|StoreExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param StoreExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(\Webqem\Stores\Api\Data\StoreExtensionInterface $extensionAttributes)
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }
}
