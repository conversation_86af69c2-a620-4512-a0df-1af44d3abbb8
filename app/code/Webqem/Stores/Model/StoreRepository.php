<?php
namespace Webqem\Stores\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\EntityManager\Operation\Read\ReadExtensions;
use Webqem\Stores\Api\Data\StoreInterface;
use Webqem\Stores\Api\Data\StoreSearchResultsInterfaceFactory as SearchResultsFactory;
use Webqem\Stores\Api\Data\StoreSearchResultsInterface;
use Webqem\Stores\Api\StoreRepositoryInterface;
use Webqem\Stores\Api\StoreStoreTypeRepositoryInterface as StoreStoreTypeRepository;
use Webqem\Stores\Api\StoreTypeRepositoryInterface as StoreTypeRepository;
use Webqem\Stores\Model\ResourceModel\Store as StoreResource;
use Webqem\Stores\Model\StoreFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Webqem\Stores\Model\ResourceModel\Store\CollectionFactory as StoreCollectionFactory;
use Webqem\Stores\Model\ResourceModel\Store\Collection;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

class StoreRepository implements StoreRepositoryInterface
{
    const XML_PATH_NEAREST_STORES_LIMIT = 'webqem_stores/api/nearest_store_limit';

    /**
     * @var StoreFactory
     */
    protected $storeFactory;

    /**
     * @var StoreResource
     */
    protected $storeResource;

    /**
     * @var SearchResultsFactory
     */
    protected $searchResultsFactory;

    /**
     * @var StoreCollectionFactory
     */
    protected $storeCollectionFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    protected $storeStoreTypeRepository;

    protected $storeTypeRepository;

    protected $storeStoreTypeFactory;

    protected $storeTypeFactory;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;
    private $readExtensions;
    /**
     * @param StoreFactory $storeFactory
     * @param StoreResource $storeResource
     * @param SearchResultsFactory $searchResultsFactory
     * @param StoreCollectionFactory $storeCollectionFactory
     * @param CollectionProcessorInterface $collectionProcessor
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param StoreStoreTypeRepository $storeStoreTypeRepository
     * @param StoreTypeRepository $storeTypeRepository
     * @param StoreStoreTypeFactory $storeStoreTypeFactory
     * @param StoreTypeFactory $storeTypeFactory
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        StoreFactory $storeFactory,
        StoreResource $storeResource,
        SearchResultsFactory $searchResultsFactory,
        StoreCollectionFactory $storeCollectionFactory,
        CollectionProcessorInterface $collectionProcessor,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        StoreStoreTypeRepository $storeStoreTypeRepository,
        StoreTypeRepository $storeTypeRepository,
        StoreStoreTypeFactory $storeStoreTypeFactory,
        StoreTypeFactory $storeTypeFactory,
        ScopeConfigInterface $scopeConfig,
        ReadExtensions $readExtensions
    ) {
        $this->storeFactory = $storeFactory;
        $this->storeResource = $storeResource;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->storeStoreTypeRepository = $storeStoreTypeRepository;
        $this->storeTypeRepository = $storeTypeRepository;
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeTypeFactory = $storeTypeFactory;
        $this->scopeConfig = $scopeConfig;
        $this->readExtensions = $readExtensions;
    }

    /**
     * Save store data
     *
     * @param StoreInterface $store
     * @return StoreInterface
     * @throws LocalizedException
     */
    public function save(StoreInterface $store)
    {
        // Check if the store code already exists
        $storeCode = $store->getStoreCode();
        $existingStore = $this->getStoreByCode($storeCode);
        if ($existingStore && (int)$existingStore->getId() !== (int)$store->getId()) {
            throw new LocalizedException(__('Cant create new store, a store with the code "%1" already exists.', $storeCode));
        }

        try {
            $this->storeResource->save($store);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not save the store: %1', $e->getMessage()));
        }

        return $this->getById($store->getId());
    }

    /**
     * Get store by ID
     *
     * @param int $storeId
     * @return StoreInterface
     * @throws NoSuchEntityException
     */
    public function getById($storeId)
    {
        $store = $this->storeFactory->create();
        $this->storeResource->load($store, $storeId);

        if (!$store->getId()) {
            throw new NoSuchEntityException(__('Store with ID "%1" does not exist.', $storeId));
        }

        return $store;
    }

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return StoreSearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        $collection = $this->storeCollectionFactory->create();

        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);

        $searchResults->setItems($collection->getItems());
        $this->addExtensionAttributes($collection);

        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * Add extension attributes to loaded items.
     *
     * @param Collection $collection
     * @return void
     */
    private function addExtensionAttributes(Collection $collection): void
    {
        foreach ($collection->getItems() as $item) {
            $this->readExtensions->execute($item);
        }
    }

    /**
     * Delete store
     *
     * @param StoreInterface $store
     * @return bool
     * @throws LocalizedException
     */
    public function delete(StoreInterface $store)
    {
        try {
            $this->storeResource->delete($store);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Could not delete the store: %1', $e->getMessage()));
        }
        return true;
    }

    /**
     * Delete store by ID
     *
     * @param int $storeId
     * @return bool
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById($storeId)
    {
        $store = $this->getById($storeId);
        return $this->delete($store);
    }


    public function getStoreByCode($storeCode)
    {
        $store = $this->storeCollectionFactory->create()
            ->addFieldToFilter('store_code', $storeCode)
            ->getFirstItem();
        if ($store->getId()) {
            $store = $this->getById($store->getId());
        }
        return $store->getId() ? $store : null; // Return store if found, else null
    }

    public function update(StoreInterface $store)
    {
        // Check if the store code already exists
        $storeCode = $store->getStoreCode();
        $existingStore = $this->getStoreByCode($storeCode);
        if ($existingStore) {
            $existingStore->setDatetimeUpdated(null);
            $storeExtensionAttributes = $existingStore->getExtensionAttributes();
            foreach ($store->getData() as $key => $value) {
                if ($store->getExtensionAttributes()->getStoreServices()) {
                    $storeExtensionAttributes->setStoreServices($store->getExtensionAttributes()->getStoreServices());
                }
                if ($store->getExtensionAttributes()->getStoreOpeningTime()) {
                    $storeExtensionAttributes->setStoreOpeningTime($store->getExtensionAttributes()->getStoreOpeningTime());
                }
                if ($store->getExtensionAttributes()->getStoreMobilePostcode()) {
                    $storeExtensionAttributes->setStoreMobilePostcode($store->getExtensionAttributes()->getStoreMobilePostcode());
                }
                if ($store->getExtensionAttributes()->getType()) {
                    $storeExtensionAttributes->setType($store->getExtensionAttributes()->getType());
                }
                $existingStore->setData($key, $value);
            }
            $existingStore->setExtensionAttributes($storeExtensionAttributes);
            $storeTypeExtensionAttribute = $storeExtensionAttributes->getType();
            if ($storeTypeExtensionAttribute) {
                $storeType = $this->storeTypeRepository->getByType($storeTypeExtensionAttribute->getType());
                if ($storeType->getId() != null) {
                    $storeStoreType = $this->storeStoreTypeFactory->create()
                        ->setStoreId($existingStore->getId())
                        ->setTypeId($storeType->getId());
                } else {
                    $storeTypeModel = $this->storeTypeFactory->create()
                        ->setType($storeTypeExtensionAttribute->getType());
                    $storeType = $this->storeTypeRepository->save($storeTypeModel);
                    $storeStoreType = $this->storeStoreTypeFactory->create()
                        ->setStoreId($existingStore->getId())
                        ->setTypeId($storeType->getId());
                }
                $storeLinkedType = $this->storeStoreTypeRepository->getStoreStoreTypeByStoreId($existingStore->getId());
                if ($storeLinkedType->getId()) {
                    $storeStoreType->setId($storeLinkedType->getId());
                    $storeLinkedType->setTypeId($storeStoreType->getTypeId());
                    $storeLinkedType->setStoreId($storeStoreType->getStoreId());
                    $this->storeStoreTypeRepository->save($storeStoreType);
                } else {
                    $storeStoreType = $this->storeStoreTypeFactory->create()
                        ->setStoreId($existingStore->getId())
                        ->setTypeId($storeType->getId());
                    $this->storeStoreTypeRepository->save($storeStoreType);
                }
                $existingStore->setStoreType($storeType->getId());
            }
            try {
                $this->storeResource->save($existingStore);
            } catch (\Exception $e) {
                throw new LocalizedException(__('Could not update the store: %1', $e->getMessage()));
            }
        } else {
            throw new LocalizedException(__('Store with code "%1" does not exist. Cannot update.', $storeCode));
        }

        return $this->getById($existingStore->getId());
    }

    /**
     * Get nearest stores
     *
     * @param float $latitude
     * @param float $longitude
     * @return SearchResultsInterface
     * @throws LocalizedException
     */
    public function getNearestStores($latitude, $longitude)
    {
        $connection = $this->storeResource->getConnection();
        $tableName = $this->storeResource->getMainTable();
        $limit = $this->scopeConfig->getValue(self::XML_PATH_NEAREST_STORES_LIMIT) ?? 5;

        $select = $connection->select()
        ->from(
            ['main_table' => $tableName],
            ['id', new \Zend_Db_Expr(
                "( 6371 * acos( cos( radians(:latitude) ) * cos( radians( main_table.lat ) ) * cos( radians( main_table.long ) - radians(:longitude) ) + sin( radians(:latitude) ) * sin( radians( main_table.lat ) ) ) ) AS distance"
            )]
        )
        ->where('main_table.enabled = 1')
        ->order('distance ASC')
        ->limit($limit);

        $bind = ['latitude' => $latitude, 'longitude' => $longitude];
        $storesData = $connection->fetchAll($select, $bind);

        $storeIds = array_column($storesData, 'id');

        $searchCriteria = $this->searchCriteriaBuilder
        ->addFilter('id', $storeIds, 'in')
        ->create();

        $searchResults = $this->getList($searchCriteria);
        // Add distances to the search results
        $items = $searchResults->getItems();
        foreach ($items as $item) {
            $storeId = $item->getId();
            $distancesIndex = array_search($storeId, $storeIds);
            if ($distancesIndex !== false) {
                $extensionAttributes = $item->getExtensionAttributes();
                $extensionAttributes->setDistance($storesData[$distancesIndex]['distance']);
            }
        }
        $searchResults->setItems($items);

        return $searchResults;
    }

    public function getByStoreCode($storeCode)
    {
        $storeStoreTypeCollection = $this->storeCollectionFactory->create()
            ->addFieldToFilter('store_code', $storeCode);
        return $storeStoreTypeCollection->getFirstItem();
    }
}
