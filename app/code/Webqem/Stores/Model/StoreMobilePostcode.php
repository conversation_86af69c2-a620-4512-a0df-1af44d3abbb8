<?php
namespace Webqem\Stores\Model;

use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttributesFactory;
use Magento\Framework\Model\AbstractModel;
use Webqem\Stores\Model\ResourceModel\StoreMobilePostcode as StoreMobilePostcodeResource;
use Magento\Framework\Model\Context;

class StoreMobilePostcode extends \Magento\Framework\Model\AbstractExtensibleModel implements \Webqem\Stores\Api\Data\StoreMobilePostcodeInterface
{
    /**
     * @param Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ExtensionAttributesFactory $extensionFactory
     * @param AttributeValueFactory $customAttributeFactory
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Registry $registry,
        ExtensionAttributesFactory $extensionFactory, AttributeValueFactory $customAttributeFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    )
    {
        parent::__construct($context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $resource,
            $resourceCollection,
            $data
        );
    }


    /**
     * Store Model constructor
     */
    protected $_idFieldName = 'id';

    protected $_resourceModel = StoreMobilePostcodeResource::class;

    /**
     * Define the table name for the model
     */
    protected $_table = 'mycar_stores_mobile_postcodes';


    protected function _construct()
    {
        $this->_init(StoreMobilePostcodeResource::class);
        parent::_construct();
    }
    /**
     * Store constructor
     */
    public function getStoreId()
    {
        return $this->getData('store_id');
    }

    public function setStoreId($storeId)
    {
        return $this->setData('store_id', $storeId);
    }

    public function getPostcode()
    {
        return $this->getData('postcode');
    }

    public function setPostcode($postcode)
    {
        return $this->setData('postcode', $postcode);
    }
}
