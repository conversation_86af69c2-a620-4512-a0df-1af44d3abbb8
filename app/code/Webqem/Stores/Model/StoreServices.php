<?php
namespace Webqem\Stores\Model;

use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttributesFactory;
use Webqem\Stores\Api\Data\StoreExtensionInterface;
use Magento\Framework\Model\AbstractModel;
use Webqem\Stores\Model\ResourceModel\StoreServices as StoreServicesResource;
use Magento\Framework\Model\Context;

class StoreServices extends \Magento\Framework\Model\AbstractExtensibleModel implements \Webqem\Stores\Api\Data\StoreServicesInterface
{
    /**
     * @param Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ExtensionAttributesFactory $extensionFactory
     * @param AttributeValueFactory $customAttributeFactory
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Registry $registry,
        ExtensionAttributesFactory $extensionFactory, AttributeValueFactory $customAttributeFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    )
    {
        parent::__construct($context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $resource,
            $resourceCollection,
            $data
        );
    }

    /**
     * Store Model constructor
     */
    protected $_idFieldName = 'id';

    protected $_resourceModel = StoreServicesResource::class;

    /**
     * Define the table name for the model
     */
    protected $_table = 'mycar_stores_services';

    /**
     * Store constructor
     */
    protected function _construct()
    {
        $this->_init(StoreServicesResource::class);
        parent::_construct();
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|StoreExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param StoreExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(\Webqem\Stores\Api\Data\StoreExtensionInterface $extensionAttributes)
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @return int|mixed|null
     */
    public function getStoreId()
    {
        return $this->getData('store_id');
    }

    /**
     * @param $storeId
     * @return StoreServices
     */
    public function setStoreId($storeId)
    {
        return $this->setData('store_id', $storeId);
    }

    /**
     * @return int|mixed|null
     */
    public function getProductId()
    {
        return $this->getData('product_id');
    }

    /**
     * @param $productId
     * @return StoreServices
     */
    public function setProductId($productId)
    {
        return $this->setData('product_id', $productId);
    }

    /**
     * @return int|mixed|null
     */
    public function getAvailable()
    {
        return $this->getData('available');
    }

    /**
     * @param $isAvailable
     * @return StoreServices
     */
    public function setAvailable($isAvailable)
    {
        return $this->setData('available', $isAvailable);
    }
}
