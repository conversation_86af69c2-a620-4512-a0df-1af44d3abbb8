<?php
namespace Webqem\Stores\Model\ResourceModel\StoreStoreType;


class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'id';

    /**
     * Dependency Initilization
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init(
            \Webqem\Stores\Model\StoreStoreType::class,
            \Webqem\Stores\Model\ResourceModel\StoreStoreType::class
        );
    }
}
