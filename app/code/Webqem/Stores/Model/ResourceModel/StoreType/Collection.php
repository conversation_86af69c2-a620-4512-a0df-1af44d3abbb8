<?php
namespace Webqem\Stores\Model\ResourceModel\StoreType;


class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'id';

    /**
     * Dependency Initilization
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init(
            \Webqem\Stores\Model\StoreType::class,
            \Webqem\Stores\Model\ResourceModel\StoreType::class
        );
    }
}
