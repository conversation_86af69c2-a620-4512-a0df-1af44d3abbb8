<?php
namespace Webqem\Stores\Model\ResourceModel;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\EntityManager\EntityManager;
use Magento\Framework\EntityManager\MetadataPool;
class Store extends AbstractDb
{
    protected $entityManager;

    /**
     * @var MetadataPool
     */
    private $metadataPool;
    public function __construct(
        EntityManager $entityManager,
        \Magento\Framework\Model\ResourceModel\Db\Context $context,
        $connectionName = null
    )
    {
        $this->entityManager = $entityManager;
        parent::__construct($context, $connectionName);
    }

    /**
     * Define the primary key field and table name
     */
    protected function _construct()
    {
        $this->_init('mycar_stores', 'id');
    }

    /**
     * Save entity's attributes into the object's resource
     *
     * @param AbstractModel $object
     * @return $this
     * @throws \Exception
     * @since 101.0.0
     */
    public function save(AbstractModel $object)
    {
        $this->entityManager->save($object);
        return $this;
    }

    public function load(\Magento\Framework\Model\AbstractModel $object, $value, $field = null)
    {
        $this->entityManager->load($object, $value);
        return $this;
    }

    public function delete(AbstractModel $object)
    {
        $this->entityManager->delete($object);
        return $this;
    }
}
