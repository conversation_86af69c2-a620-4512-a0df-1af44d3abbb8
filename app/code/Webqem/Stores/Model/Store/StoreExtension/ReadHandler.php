<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Webqem\Stores\Model\Store\StoreExtension;

use Webqem\Stores\Api\StoreMobilePostcodeRepositoryInterface as StoreMobilePostcodeRepository;
use Magento\Framework\EntityManager\Operation\ExtensionInterface;
use Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface as StoreOpeningTimeRepository;
use Webqem\Stores\Api\StoreServicesRepositoryInterface as StoreServicesRepository;
use Webqem\Stores\Api\StoreStoreTypeRepositoryInterface as StoreStoreTypeRepository;
use Webqem\Stores\Api\StoreTypeRepositoryInterface as StoreTypeRepository;
use Webqem\Stores\Model\StoreStoreTypeFactory;
use Webqem\Stores\Model\StoreTypeFactory;

/**
 * Class ReadHandler
 */
class ReadHandler implements ExtensionInterface
{
    /**
     * @var StoreMobilePostcodeRepository
     */
    protected $storeMobilePostcodeRepository;

    /**
     * @var StoreOpeningTimeRepository
     */
    protected $storeOpeningTimeRepository;

    /**
     * @var StoreServicesRepository
     */
    protected $storeServicesRepository;

    protected $storeStoreTypeRepository;

    protected $storeTypeRepository;

    protected $storeStoreTypeFactory;

    protected $storeTypeFactory;

    /**
     * @param StoreMobilePostcodeRepository $storeMobilePostcodeRepository
     * @param StoreOpeningTimeRepository $storeOpeningTimeRepository
     * @param StoreServicesRepository $storeServicesRepository
     */
    public function __construct(
        StoreMobilePostcodeRepository $storeMobilePostcodeRepository,
        StoreOpeningTimeRepository $storeOpeningTimeRepository,
        StoreServicesRepository $storeServicesRepository,
        StoreStoreTypeRepository $storeStoreTypeRepository,
        StoreTypeRepository $storeTypeRepository,
        StoreStoreTypeFactory $storeStoreTypeFactory,
        StoreTypeFactory $storeTypeFactory
    )
    {
        $this->storeMobilePostcodeRepository = $storeMobilePostcodeRepository;
        $this->storeOpeningTimeRepository = $storeOpeningTimeRepository;
        $this->storeServicesRepository = $storeServicesRepository;
        $this->storeStoreTypeRepository = $storeStoreTypeRepository;
        $this->storeTypeRepository = $storeTypeRepository;
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeTypeFactory = $storeTypeFactory;
    }

    /**
     * @param object $entity
     * @param array $arguments
     * @return \Webqem\Stores\Api\Data\StoreMobilePostcodeInterface|object
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute($entity, $arguments = [])
    {
        $entityExtension = $entity->getExtensionAttributes();
        $storeMobileCode = $this->storeMobilePostcodeRepository->getListByStoreId($entity->getId());
        if ($storeMobileCode) {
            $entityExtension->setStoreMobilePostcode($storeMobileCode);
        }
        $storeOpeningTime = $this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($entity->getId());
        if ($storeOpeningTime) {
            $entityExtension->setStoreOpeningTime($storeOpeningTime);
        }
        $storeServices = $this->storeServicesRepository->getListByStoreId($entity->getId());
        if ($storeServices) {
            $entityExtension->setStoreServices($storeServices);
        }
        $storeLinkedType = $this->storeStoreTypeRepository->getStoreStoreTypeByStoreId($entity->getId());
        if ($storeLinkedType->getId()) {
            $storeType = $this->storeTypeRepository->getById($storeLinkedType->getTypeId());
            $entityExtension->setType($storeType);
        }
        $entity->setExtensionAttributes($entityExtension);
        return $entity;
    }
}
