<?php
namespace Webqem\Stores\Model\Store\StoreExtension;

use Webqem\Stores\Api\StoreMobilePostcodeRepositoryInterface as StoreMobilePostcodeRepository;
use Magento\Framework\EntityManager\Operation\ExtensionInterface;
use Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface as StoreOpeningTimeRepository;
use Webqem\Stores\Api\StoreServicesRepositoryInterface as StoreServicesRepository;
use Webqem\Stores\Api\StoreStoreTypeRepositoryInterface as StoreStoreTypeRepository;
use Webqem\Stores\Api\StoreTypeRepositoryInterface as StoreTypeRepository;
use Webqem\Stores\Model\StoreStoreTypeFactory;
use Webqem\Stores\Model\StoreTypeFactory;

/**
 * Class UpdateHandler
 */
class UpdateHandler implements ExtensionInterface
{
    /**
     * @var StoreMobilePostcodeRepository
     */
    protected $storeMobilePostcodeRepository;

    /**
     * @var StoreOpeningTimeRepository
     */
    protected $storeOpeningTimeRepository;

    /**
     * @var StoreServicesRepository
     */
    protected $storeServicesRepository;

    protected $storeStoreTypeRepository;

    protected $storeTypeRepository;

    protected $storeStoreTypeFactory;

    protected $storeTypeFactory;

    /**
     * @param StoreMobilePostcodeRepository $storeMobilePostcodeRepository
     * @param StoreOpeningTimeRepository $storeOpeningTimeRepository
     * @param StoreServicesRepository $storeServicesRepository
     */
    public function __construct(
        StoreMobilePostcodeRepository $storeMobilePostcodeRepository,
        StoreOpeningTimeRepository $storeOpeningTimeRepository,
        StoreServicesRepository $storeServicesRepository,
        StoreStoreTypeRepository $storeStoreTypeRepository,
        StoreTypeRepository $storeTypeRepository,
        StoreStoreTypeFactory $storeStoreTypeFactory,
        StoreTypeFactory $storeTypeFactory
    )
    {
        $this->storeMobilePostcodeRepository = $storeMobilePostcodeRepository;
        $this->storeOpeningTimeRepository = $storeOpeningTimeRepository;
        $this->storeServicesRepository = $storeServicesRepository;
        $this->storeStoreTypeRepository = $storeStoreTypeRepository;
        $this->storeTypeRepository = $storeTypeRepository;
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeTypeFactory = $storeTypeFactory;
    }

    /**
     * @param object $entity
     * @param array $arguments
     * @return \Webqem\Stores\Api\Data\StoreMobilePostcodeInterface|object
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute($entity, $arguments = [])
    {
        $storeMobilePostcodes = $entity->getExtensionAttributes()->getStoreMobilePostcode() ?: [];
        $storeOpeningTimes = $entity->getExtensionAttributes()->getStoreOpeningTime() ?: [];
        $storeServices = $entity->getExtensionAttributes()->getStoreServices() ?: [];
        $storeTypeExtensionAttribute = $entity->getExtensionAttributes()->getType() ?: null;
        $updatedAccessCode = [];
        $oldMobilePostcodes = $this->storeMobilePostcodeRepository->getListByStoreId($entity->getId());
        $oldStoreOpeningTime = $this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($entity->getId());
        $oldStoreServices = $this->storeServicesRepository->getListByStoreId($entity->getId());
        foreach ($storeMobilePostcodes as $item) {
            if ($item->getId()) {
                $updatedAccessCode[$item->getId()] = true;
            }
            $item->setStoreId($entity->getId());
            $this->storeMobilePostcodeRepository->save($item);
        }
        /** @var \Magento\Catalog\Api\Data\ProductInterface $entity */
        foreach ($oldMobilePostcodes as $item) {
            if (!isset($updatedAccessCode[$item->getId()])) {
                $this->storeMobilePostcodeRepository->delete($item);
            }
        }
        foreach ($storeOpeningTimes as $item) {
            if ($item->getId()) {
                $updatedOpeningTime[$item->getId()] = true;
            }
            $item->setStoreId($entity->getId());
            $this->storeOpeningTimeRepository->save($item);
        }
        foreach ($oldStoreOpeningTime as $item) {
            if (!isset($updatedOpeningTime[$item->getId()])) {
                $this->storeOpeningTimeRepository->delete($item);
            }
        }
        foreach ($storeServices as $item) {
            if ($item->getId()) {
                $updatedService[$item->getId()] = true;
            }
            $item->setStoreId($entity->getId());
            $this->storeServicesRepository->save($item);
        }
        foreach ($oldStoreServices as $item) {
            if (!isset($updatedService[$item->getId()])) {
                $this->storeServicesRepository->delete($item);
            }
        }
        return $entity;
    }
}
