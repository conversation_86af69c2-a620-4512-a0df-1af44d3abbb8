<?php

namespace Webqem\Stores\Model\Store\StoreExtension;

use Webqem\Stores\Api\StoreMobilePostcodeRepositoryInterface as StoreMobilePostcodeRepository;
use Magento\Framework\EntityManager\Operation\ExtensionInterface;
use Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface as StoreOpeningTimeRepository;
use Webqem\Stores\Api\StoreServicesRepositoryInterface as StoreServicesRepository;

/**
 * Class DeleteHandler
 */
class DeleteHandler implements ExtensionInterface
{
    /**
     * @var StoreMobilePostcodeRepository
     */
    protected $storeMobilePostcodeRepository;

    /**
     * @var StoreOpeningTimeRepository
     */
    protected $storeOpeningTimeRepository;

    /**
     * @var StoreServicesRepository
     */
    protected $storeServicesRepository;

    /**
     * @param StoreMobilePostcodeRepository $storeMobilePostcodeRepository
     * @param StoreOpeningTimeRepository $storeOpeningTimeRepository
     * @param StoreServicesRepository $storeServicesRepository
     */
    public function __construct(
        StoreMobilePostcodeRepository $storeMobilePostcodeRepository,
        StoreOpeningTimeRepository $storeOpeningTimeRepository,
        StoreServicesRepository $storeServicesRepository
    )
    {
        $this->storeMobilePostcodeRepository = $storeMobilePostcodeRepository;
        $this->storeOpeningTimeRepository = $storeOpeningTimeRepository;
        $this->storeServicesRepository = $storeServicesRepository;
    }

    /**
     * @param object $entity
     * @param array $arguments
     * @return \Webqem\Stores\Api\Data\StoreMobilePostcodeInterface|object
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute($entity, $arguments = [])
    {
        foreach ($this->storeMobilePostcodeRepository->getListByStoreId($entity->getId()) as $item) {
            $this->storeMobilePostcodeRepository->delete($item);
        }
        foreach ($this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($entity->getId()) as $item) {
            $this->storeOpeningTimeRepository->delete($item);
        }
        foreach ($this->storeServicesRepository->getListByStoreId($entity->getId()) as $item) {
            $this->storeServicesRepository->delete($item);
        }
        return $entity;
    }
}
