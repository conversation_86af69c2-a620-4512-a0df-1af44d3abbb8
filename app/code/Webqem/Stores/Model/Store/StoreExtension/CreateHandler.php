<?php

namespace Webqem\Stores\Model\Store\StoreExtension;

use Webqem\Stores\Api\StoreMobilePostcodeRepositoryInterface as StoreMobilePostcodeRepository;
use Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface as StoreOpeningTimeRepository;
use Webqem\Stores\Api\StoreServicesRepositoryInterface as StoreServicesRepository;
use Webqem\Stores\Api\StoreStoreTypeRepositoryInterface as StoreStoreTypeRepository;
use Webqem\Stores\Api\StoreTypeRepositoryInterface as StoreTypeRepository;
use Webqem\Stores\Model\StoreStoreTypeFactory;
use Magento\Framework\EntityManager\Operation\ExtensionInterface;
use Webqem\Stores\Model\StoreTypeFactory;

/**
 * Class CreateHandler
 */
class CreateHandler implements ExtensionInterface
{
    /**
     * @var StoreMobilePostcodeRepository
     */
    protected $storeMobilePostcodeRepository;

    /**
     * @var StoreOpeningTimeRepository
     */
    protected $storeOpeningTimeRepository;

    /**
     * @var StoreServicesRepository
     */
    protected $storeServicesRepository;

    protected $storeStoreTypeRepository;

    protected $storeTypeRepository;

    protected $storeStoreTypeFactory;

    protected $storeTypeFactory;

    /**
     * @param StoreMobilePostcodeRepository $storeMobilePostcodeRepository
     * @param StoreOpeningTimeRepository $storeOpeningTimeRepository
     * @param StoreServicesRepository $storeServicesRepository
     */
    public function __construct(
        StoreMobilePostcodeRepository $storeMobilePostcodeRepository,
        StoreOpeningTimeRepository $storeOpeningTimeRepository,
        StoreServicesRepository $storeServicesRepository,
        StoreStoreTypeRepository $storeStoreTypeRepository,
        StoreTypeRepository $storeTypeRepository,
        StoreStoreTypeFactory $storeStoreTypeFactory,
        StoreTypeFactory $storeTypeFactory
    )
    {
        $this->storeMobilePostcodeRepository = $storeMobilePostcodeRepository;
        $this->storeOpeningTimeRepository = $storeOpeningTimeRepository;
        $this->storeServicesRepository = $storeServicesRepository;
        $this->storeStoreTypeRepository = $storeStoreTypeRepository;
        $this->storeTypeRepository = $storeTypeRepository;
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeTypeFactory = $storeTypeFactory;
    }

    /**
     * @param object $entity
     * @param array $arguments
     * @return \Webqem\Stores\Api\Data\StoreMobilePostcodeInterface|object
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute($entity, $arguments = [])
    {
        $storeMobilePostcodes = $entity->getExtensionAttributes()->getStoreMobilePostcode() ?: [];
        foreach ($storeMobilePostcodes as $item) {
            $item->setId(null);
            $item->setStoreId($entity->getId());
            $this->storeMobilePostcodeRepository->save($item);
        }
        $storeOpeningTimes = $entity->getExtensionAttributes()->getStoreOpeningTime() ?: [];
        foreach ($storeOpeningTimes as $item) {
            $item->setId(null);
            $item->setStoreId($entity->getId());
            $this->storeOpeningTimeRepository->save($item);
        }
        $storeServices = $entity->getExtensionAttributes()->getStoreServices() ?: [];
        foreach ($storeServices as $item) {
            $item->setId(null);
            $item->setStoreId($entity->getId());
            $this->storeServicesRepository->save($item);
        }
        $storeTypeExtensionAttribute = $entity->getExtensionAttributes()->getType() ?: [];
        if ($storeTypeExtensionAttribute) {
            $storeType = $this->storeTypeRepository->getByType($storeTypeExtensionAttribute['type']);
            if ($storeType->getId() != null) {
                $storeStoreType = $this->storeStoreTypeFactory->create()
                    ->setStoreId($entity->getId())
                    ->setTypeId($storeType->getId());
            } else {
                $storeTypeModel = $this->storeTypeFactory->create()
                    ->setType($storeTypeExtensionAttribute['type']);
                $storeType = $this->storeTypeRepository->save($storeTypeModel);
                $storeStoreType = $this->storeStoreTypeFactory->create()
                    ->setStoreId($entity->getId())
                    ->setTypeId($storeType->getId());
            }
            $this->storeStoreTypeRepository->save($storeStoreType);
            $entity->setStoreType($storeType->getId());
            $entity->save();
        }
        return $entity;
    }
}
