<?php

namespace Webqem\Stores\Model;

use Webqem\Stores\Model\ResourceModel\Store\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Store\Model\StoreManagerInterface;
use Webqem\Stores\Model\ResourceModel\StoreMobilePostcode\CollectionFactory as StoreMobilePostcodeCollectionFactory;
use Webqem\Stores\Model\ResourceModel\StoreOpeningTime\CollectionFactory as StoreOpeningTimeCollectionFactory;
use Webqem\Stores\Model\ResourceModel\StoreServices\CollectionFactory as StoreServicesCollectionFactory;

class DataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var $_loadedData
     */
    protected $_loadedData;

    /**
     * @var ResourceModel\Store\Collection
     */
    protected $collection;

    protected $storeManager;

    protected $resource;

    protected $dataPersistor;

    protected $storeMobilePostcodeCollectionFactory;

    protected $storeOpeningTimeCollectionFactory;

    protected $storeServicesCollectionFactory;

    /**
     * DataProvider constructor.
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        StoreManagerInterface $storeManager,
        StoreMobilePostcodeCollectionFactory $storeMobilePostcodeCollectionFactory,
        StoreOpeningTimeCollectionFactory $storeOpeningTimeCollectionFactory,
        StoreServicesCollectionFactory $storeServicesCollectionFactory,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->storeManager = $storeManager;
        $this->storeMobilePostcodeCollectionFactory = $storeMobilePostcodeCollectionFactory;
        $this->storeOpeningTimeCollectionFactory = $storeOpeningTimeCollectionFactory;
        $this->storeServicesCollectionFactory = $storeServicesCollectionFactory;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * @return array
     */
    public function getData()
    {

        if (isset($this->_loadedData)) {
            return $this->_loadedData;
        }
        $items = $this->collection->getItems();
        foreach ($items as $item) {
            $this->_loadedData[$item->getId()] = $item->getData();
            if ($item->getImage1()) {
                $image1Data['image1'][0]['name'] = $item->getImage1();
                $image1Data['image1'][0]['url'] = $this->getMediaUrl($item->getImage1());
                $fullData = $this->_loadedData;
                $this->_loadedData[$item->getId()] = array_merge($fullData[$item->getId()], $image1Data);
            }
            if ($item->getImage2()) {
                $image2Data['image2'][0]['name'] = $item->getImage2();
                $image2Data['image2'][0]['url'] = $this->getMediaUrl($item->getImage2());
                $fullData = $this->_loadedData;
                $this->_loadedData[$item->getId()] = array_merge($fullData[$item->getId()], $image2Data);
            }
            if ($item->getImage3()) {
                $image3Data['image3'][0]['name'] = $item->getImage3();
                $image3Data['image3'][0]['url'] = $this->getMediaUrl($item->getImage3());
                $fullData = $this->_loadedData;
                $this->_loadedData[$item->getId()] = array_merge($fullData[$item->getId()], $image3Data);
            }
            if ($item->getImage4()) {
                $image4Data['image4'][0]['name'] = $item->getImage4();
                $image4Data['image4'][0]['url'] = $this->getMediaUrl($item->getImage4());
                $fullData = $this->_loadedData;
                $this->_loadedData[$item->getId()] = array_merge($fullData[$item->getId()], $image4Data);
            }
            $this->_loadedData[$item->getId()]['store_postcodes'] = $this->getStoreMobilePostcodeData($item->getId());
            $this->_loadedData[$item->getId()]['store_openingtime'] = $this->getStoreOpeningTimeData($item->getId());
            $this->_loadedData[$item->getId()]['store_services'] = $this->getStoreServicesData($item->getId());
        }
        $data = $this->dataPersistor->get('mycar_stores');

        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->_loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('mycar_stores');
        }
        return $this->_loadedData;
    }

    public function getMediaUrl($path = '')
    {
        $mediaUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA) . 'mycarstores/image/' . $path;
        return $mediaUrl;
    }

    public function getStoreMobilePostcodeData($id)
    {
        $storeMobilePostcodeData = [];
        $storeMobilePostcodes = $this->storeMobilePostcodeCollectionFactory->create()
            ->addFieldToSelect('postcode')
            ->addFieldToFilter('store_id', $id)
            ->getItems();
        foreach ($storeMobilePostcodes as $storeMobilePostcode)
        {
            $storeMobilePostcodeData[] = [
                'postcode' => $storeMobilePostcode->getPostcode(),
            ];
        }
        return $storeMobilePostcodeData;
    }

    public function getStoreOpeningTimeData($id)
    {
        $storeOpeningTimeData = [];
        $storeOpeningTimeByStoreId = $this->storeOpeningTimeCollectionFactory->create()
            ->addFieldToSelect('day')
            ->addFieldToSelect('opening_time')
            ->addFieldToSelect('closing_time')
            ->addFieldToFilter('store_id', $id)
            ->getItems();
        foreach ($storeOpeningTimeByStoreId as $storeOpeningTime)
        {
            $storeOpeningTimeData[] = [
                'day' => $storeOpeningTime->getDay(),
                'opening_time' => $storeOpeningTime->getOpeningTime(),
                'closing_time' => $storeOpeningTime->getClosingTime()
            ];
        }
        return $storeOpeningTimeData;
    }

    public function getStoreServicesData($id)
    {
        $storeServicesData = [];
        $storeServicesByStoreId = $this->storeServicesCollectionFactory->create()
            ->addFieldToSelect('*')
            ->addFieldToFilter('store_id', $id)
            ->getItems();
        foreach ($storeServicesByStoreId as $storeService)
        {
            $storeServicesData[] = [
                'product_id' => $storeService->getProductId(),
                'available' => $storeService->getAvailable()
            ];
        }
        return $storeServicesData;
    }
}
