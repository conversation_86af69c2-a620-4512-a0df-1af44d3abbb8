<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/stores/:storeId" method="GET">
        <service class="Webqem\Stores\Api\StoreRepositoryInterface" method="getById"/>
        <resources>
            <resource ref="Webqem_Stores::get_store_information"/>
        </resources>
    </route>

    <route url="/V1/stores" method="POST">
        <service class="Webqem\Stores\Api\StoreRepositoryInterface" method="save"/>
        <resources>
            <resource ref="Webqem_Stores::create_update_store"/>
        </resources>
    </route>

    <route url="/V1/stores/:storeCode" method="PUT">
        <service class="Webqem\Stores\Api\StoreRepositoryInterface" method="update"/>
        <resources>
            <resource ref="Webqem_Stores::create_update_store"/>
        </resources>
    </route>

    <route url="/V1/stores" method="GET">
        <service class="Webqem\Stores\Api\StoreRepositoryInterface" method="getList"/>
        <resources>
            <resource ref="Webqem_Stores::get_store_information"/>
        </resources>
    </route>

    <route url="/V1/stores/:storeId" method="DELETE">
        <service class="Webqem\Stores\Api\StoreRepositoryInterface" method="deleteById"/>
        <resources>
            <resource ref="Webqem_Stores::create_update_store"/>
        </resources>
    </route>

    <route url="/V1/stores/nearest/:latitude/:longitude" method="GET">
        <service class="Webqem\Stores\Api\StoreRepositoryInterface" method="getNearestStores"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>
