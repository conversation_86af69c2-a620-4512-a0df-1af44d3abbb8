<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../lib/internal/Magento/Framework/ObjectManager/etc/config.xsd">
    <preference for="Webqem\Stores\Api\StoreRepositoryInterface" type="Webqem\Stores\Model\StoreRepository" />
    <preference for="Webqem\Stores\Api\Data\StoreInterface" type="Webqem\Stores\Model\Store" />

    <preference for="Webqem\Stores\Api\StoreServicesRepositoryInterface" type="Webqem\Stores\Model\StoreServicesRepository" />
    <preference for="Webqem\Stores\Api\Data\StoreServicesInterface" type="Webqem\Stores\Model\StoreServices" />

    <preference for="Webqem\Stores\Api\Data\StoreSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />

    <preference for="Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface" type="Webqem\Stores\Model\StoreOpeningTimeRepository" />
    <preference for="Webqem\Stores\Api\Data\StoreOpeningTimeInterface" type="Webqem\Stores\Model\StoreOpeningTime" />

    <preference for="Webqem\Stores\Api\StoreStoreTypeRepositoryInterface" type="Webqem\Stores\Model\StoreStoreTypeRepository" />
    <preference for="Webqem\Stores\Api\Data\StoreStoreTypeInterface" type="Webqem\Stores\Model\StoreStoreType" />

    <preference for="Webqem\Stores\Api\StoreTypeRepositoryInterface" type="Webqem\Stores\Model\StoreTypeRepository" />
    <preference for="Webqem\Stores\Api\Data\StoreTypeInterface" type="Webqem\Stores\Model\StoreType" />

    <preference for="Webqem\Stores\Api\StoreMobilePostcodeRepositoryInterface" type="Webqem\Stores\Model\StoreMobilePostcodeRepository" />
    <preference for="Webqem\Stores\Api\Data\StoreMobilePostcodeInterface" type="Webqem\Stores\Model\StoreMobilePostcode" />
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Webqem\Stores\Api\Data\StoreInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">mycar_stores</item>
                    <item name="identifierField" xsi:type="string">id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\EntityManager\Operation\ExtensionPool">
        <arguments>
            <argument name="extensionActions" xsi:type="array">
                <item name="Webqem\Stores\Api\Data\StoreInterface" xsi:type="array">
                    <item name="read" xsi:type="array">
                        <item name="store_read_handler" xsi:type="string">Webqem\Stores\Model\Store\StoreExtension\ReadHandler</item>
                    </item>
                    <item name="create" xsi:type="array">
                        <item name="store_create_handler" xsi:type="string">Webqem\Stores\Model\Store\StoreExtension\CreateHandler</item>
                    </item>
                    <item name="update" xsi:type="array">
                        <item name="store_update_handler" xsi:type="string">Webqem\Stores\Model\Store\StoreExtension\UpdateHandler</item>
                    </item>
                    <item name="delete" xsi:type="array">
                        <item name="store_delete_handler" xsi:type="string">Webqem\Stores\Model\Store\StoreExtension\DeleteHandler</item>
                    </item>
                </item>
            </argument>
        </arguments>
    </type>

<!--    <type name="Webqem\Stores\Api\StoreRepositoryInterface">-->
<!--        <plugin name="after_store_repository_plugin" type="Webqem\Stores\Plugin\StoreRepositoryPlugin"/>-->
<!--    </type>-->

<!--    <type name="Webqem\Stores\Api\Data\StoreInterface">-->
<!--        <plugin name="afterGetExtensionAttributes" type="Webqem\Stores\Plugin\StoreAttributeLoad"/>-->
<!--    </type>-->

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="mycar_stores_listing_data_source" xsi:type="string">MycarStoresCollection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="MycarStoresCollection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">mycar_stores</argument>
            <argument name="resourceModel" xsi:type="string">Webqem\Stores\Model\ResourceModel\Store</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="mycar_types_listing_datasource" xsi:type="string">MycarStoreTypesCollection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="MycarStoreTypesCollection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">mycar_store_type</argument>
            <argument name="resourceModel" xsi:type="string">Webqem\Stores\Model\ResourceModel\StoreType</argument>
        </arguments>
    </virtualType>
</config>
