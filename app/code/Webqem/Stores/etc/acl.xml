<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Webqem_Stores::stores" title="Webqem Stores" sortOrder="10">
                    <resource id="Webqem_Stores::get_store_information" title="Get Store Information" sortOrder="10"/>
                    <resource id="Webqem_Stores::create_update_store" title="Create and update store" sortOrder="10"/>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
