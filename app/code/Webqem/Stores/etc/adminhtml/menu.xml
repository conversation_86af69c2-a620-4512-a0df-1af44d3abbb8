<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <!-- Define new item under Content → Elements -->
    <menu>
        <add id="Webqem_Stores::mycar_stores"
             title="Mycar Stores"
             translate="Mycar Stores"
             module="Webqem_Stores"
             sortOrder="0"
             parent="Magento_Backend::content_elements"
             action="stores/grid/index"
             resource="Webqem_Stores::mycar_stores"/>
        <add id="Webqem_Stores::mycar_store_type"
             title="Mycar Store Type"
             translate="Mycar Store Type"
             module="Webqem_Stores"
             sortOrder="0"
             parent="Magento_Backend::content_elements"
             action="stores/types/index"
             resource="Webqem_Stores::mycar_stores"/>
    </menu>
</config>
