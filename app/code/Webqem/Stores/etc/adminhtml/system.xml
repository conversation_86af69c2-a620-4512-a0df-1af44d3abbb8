<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="webqem" translate="label" sortOrder="100">
            <label>Webqem</label>
        </tab>
        <section id="webqem_stores" translate="label" type="text" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Stores</label>
            <tab>webqem</tab>
            <resource>Webqem_Stores::stores</resource>
            <group id="api" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>API Configuration</label>
                <field id="nearest_store_limit" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Number of Nearest Stores to Return</label>
                </field>
            </group>
        </section>
    </system>
</config>
