<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Webqem\Stores\Api\Data\StoreInterface">
        <attribute code="store_services" type="Webqem\Stores\Api\Data\StoreServicesInterface[]" />
        <attribute code="store_opening_time" type="Webqem\Stores\Api\Data\StoreOpeningTimeInterface[]" />
        <attribute code="type" type="Webqem\Stores\Api\Data\StoreTypeInterface"/>
        <attribute code="store_mobile_postcode" type="Webqem\Stores\Api\Data\StoreMobilePostcodeInterface[]"/>
       <attribute code="distance" type="float"/>
    </extension_attributes>
</config>
