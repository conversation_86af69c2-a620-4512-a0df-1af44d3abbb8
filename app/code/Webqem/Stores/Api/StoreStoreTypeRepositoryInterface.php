<?php
namespace Webqem\Stores\Api;

use Webqem\Stores\Api\Data\StoreStoreTypeInterface;

interface StoreStoreTypeRepositoryInterface
{
    /**
     * Save Store.
     *
     * @param StoreStoreTypeInterface $storeStoreType
     * @return StoreStoreTypeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(StoreStoreTypeInterface $storeStoreType): StoreStoreTypeInterface;

    /**
     * Retrieve Store.
     *
     * @param int $storeStoreTypeId
     * @return StoreStoreTypeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById(int $storeStoreTypeId): StoreStoreTypeInterface;

    /**
     * Retrieve Store.
     *
     * @param int $storeId
     * @return StoreStoreTypeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getStoreStoreTypeByStoreId($storeId);

    /**
     * Delete Store.
     *
     * @param StoreStoreTypeInterface $storeStoreType
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(StoreStoreTypeInterface $storeStoreType);
}
