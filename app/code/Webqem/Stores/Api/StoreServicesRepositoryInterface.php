<?php
namespace Webqem\Stores\Api;

use Magento\Framework\Api\SearchCriteriaInterface;
use Webqem\Stores\Api\Data\StoreServicesInterface;

interface StoreServicesRepositoryInterface
{
    /**
     * Save Store.
     *
     * @param StoreServicesInterface $storeService
     * @return StoreServicesInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(StoreServicesInterface $storeService);

    /**
     * Retrieve Store.
     *
     * @param int $storeServiceId
     * @return StoreServicesInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($storeServiceId);

    /**
     * Retrieve Store.
     *
     * @param int $storeId
     * @return StoreServicesInterface[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getListByStoreId($storeId);

    /**
     * Retrieve Store Services by product ID.
     *
     * @param int $productId
     * @return StoreServicesInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getByProductId($productId);

    /**
     * Delete Store.
     *
     * @param StoreServicesInterface $storeService
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(StoreServicesInterface $storeService);
}
