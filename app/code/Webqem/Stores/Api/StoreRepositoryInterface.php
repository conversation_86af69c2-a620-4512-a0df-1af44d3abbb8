<?php
namespace Webqem\Stores\Api;

use Webqem\Stores\Api\Data\StoreInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

interface StoreRepositoryInterface
{
    /**
     * Save Store.
     *
     * @param StoreInterface $store
     * @return StoreInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(StoreInterface $store);

    /**
     * Save Store.
     *
     * @param StoreInterface $store
     * @return StoreInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function update(StoreInterface $store);

    /**
     * Retrieve Store.
     *
     * @param int $storeId
     * @return StoreInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($storeId);

    /**
     * Retrieve StoreList matching the specified criteria.
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Webqem\Stores\Api\Data\StoreSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Delete Store.
     *
     * @param StoreInterface $store
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(StoreInterface $store);

    /**
     * Delete Store by ID.
     *
     * @param int $storeId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($storeId);

    /**
     * Get nearest stores.
     *
     * @param float $latitude
     * @param float $longitude
     * @return \Webqem\Stores\Api\Data\StoreSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getNearestStores($latitude, $longitude);

    /**
     * @param string $storeCode
     * @return StoreInterface
     */
    public function getByStoreCode(string $storeCode);
}
