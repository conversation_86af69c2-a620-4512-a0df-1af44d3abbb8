<?php
namespace Webqem\Stores\Api;

use Webqem\Stores\Api\Data\StoreInterface;
use Webqem\Stores\Api\Data\StoreMobilePostcodeInterface;
use Webqem\Stores\Api\Data\StoreServicesInterface;

interface StoreMobilePostcodeRepositoryInterface
{
    /**
     * Save Store Mobile Postcode.
     *
     * @param StoreMobilePostcodeInterface $storeMobilePostcode
     * @return StoreMobilePostcodeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(StoreMobilePostcodeInterface $storeMobilePostcode): StoreMobilePostcodeInterface;

    /**
     * Retrieve Store Mobile Postcode By Id.
     *
     * @param int $storeMobilePostcodeId
     * @return StoreMobilePostcodeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById(int $storeMobilePostcodeId): StoreMobilePostcodeInterface;

    /**
     * Retrieve Store Mobile Postcode by postcode.
     *
     * @param int $postCode
     * @return StoreMobilePostcodeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getByPostcode($postCode);

    /**
     * Retrieve Store Mobile Postcode by StoreID.
     *
     * @param int $storeId
     * @return StoreMobilePostcodeInterface[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getListByStoreId($storeId);

    /**
     * Delete Store.
     *
     * @param StoreMobilePostcodeInterface $storeMobilePostcode
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(StoreMobilePostcodeInterface $storeMobilePostcode);
}
