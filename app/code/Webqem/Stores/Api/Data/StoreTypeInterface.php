<?php
namespace Webqem\Stores\Api\Data;

interface StoreTypeInterface
{
    /**
     * Get ID of the store type
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get ID of the store type
     *
     * @return int|null
     */
    public function setId($id);

    /**
     * Get type of the store
     *
     * @return string|null
     */
    public function getType();

    /**
     * Set type of the store
     *
     * @param string $type
     * @return $this
     */
    public function setType($type);
}
