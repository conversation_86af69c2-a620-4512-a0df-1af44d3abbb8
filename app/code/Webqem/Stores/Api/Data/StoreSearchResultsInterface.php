<?php

namespace Webqem\Stores\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

interface StoreSearchResultsInterface extends SearchResultsInterface
{
    /**
     * @return \Webqem\Stores\Api\Data\StoreInterface[]
     */
    public function getItems();

    /**
     * @param \Webqem\Stores\Api\Data\StoreInterface[] $items
     * @return void
     */
    public function setItems(array $items);
}
