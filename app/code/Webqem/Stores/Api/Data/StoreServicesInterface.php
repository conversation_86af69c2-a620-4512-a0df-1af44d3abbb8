<?php
namespace Webqem\Stores\Api\Data;

interface StoreServicesInterface
{
    /**
     * Get ID of the store service
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get ID of the store service
     *
     * @return int|null
     */
    public function setId($id);

    /**
     * Get ID of the store
     *
     * @return int|null
     */
    public function getStoreId();

    /**
     * Set the ID of the store
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId($storeId);

    /**
     * Get product ID link to the store
     *
     * @return int|null
     */
    public function getProductId();

    /**
     * Set product ID link to the store
     *
     * @param int $productId
     * @return $this
     */
    public function setProductId($productId);

    /**
     * Get availability of the store service
     *
     * @return int|null
     */
    public function getAvailable();

    /**
     * Set availability of the store service
     *
     * @param int $isAvailable
     * @return $this
     */
    public function setAvailable($isAvailable);
}
