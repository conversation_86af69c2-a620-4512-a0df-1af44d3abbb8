<?php
namespace Webqem\Stores\Api\Data;

interface StoreOpeningTimeInterface
{
    /**
     * Get ID of the store service
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get ID of the store service
     *
     * @return int|null
     */
    public function setId($id);

    /**
     * Get ID of the store
     *
     * @return int|null
     */
    public function getStoreId();

    /**
     * Set the ID of the store
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId($storeId);

    /**
     * Get store opening day
     *
     * @return string|null
     */
    public function getDay();

    /**
     * Set store opening day
     *
     * @param string $day
     * @return $this
     */
    public function setDay($day);

    /**
     * Get store opening time
     *
     * @return string|null
     */
    public function getOpeningTime();

    /**
     * Set store opening day
     *
     * @param string $openingTime
     * @return $this
     */
    public function setOpeningTime($openingTime);

    /**
     * Get store closing time
     *
     * @return string|null
     */
    public function getClosingTime();

    /**
     * Set store opening day
     *
     * @param string $closingTime
     * @return $this
     */
    public function setClosingTime($closingTime);
}
