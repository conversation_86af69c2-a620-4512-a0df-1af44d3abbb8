<?php
namespace Webqem\Stores\Api\Data;

interface StoreStoreTypeInterface
{
    /**
     * Get ID of the store store type
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get ID of the store store type
     *
     * @return int|null
     */
    public function setId($id);

    /**
     * Get ID of the store
     *
     * @return int|null
     */
    public function getStoreId();

    /**
     * Set the ID of the store
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId($storeId);

    /**
     * Get type ID link to the store
     *
     * @return int|null
     */
    public function getTypeId();

    /**
     * Set type ID link to the store
     *
     * @param int $typeId
     * @return $this
     */
    public function setTypeId($typeId);
}
