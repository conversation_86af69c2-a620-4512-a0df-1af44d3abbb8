<?php
namespace Webqem\Stores\Api\Data;

interface StoreInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{
    /**
     * Get ID of the store
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set the ID of the store
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * Get the datetime when the store was created
     *
     * @return string|null
     */
    public function getDatetimeCreated();

    /**
     * Set the datetime when the store was created
     *
     * @param string $datetimeCreated
     * @return $this
     */
    public function setDatetimeCreated($datetimeCreated);

    /**
     * Get the datetime when the store was last updated
     *
     * @return string|null
     */
    public function getDatetimeUpdated();

    /**
     * Set the datetime when the store was last updated
     *
     * @param string $datetimeUpdated
     * @return $this
     */
    public function setDatetimeUpdated($datetimeUpdated);

    /**
     * Get store code
     *
     * @return string|null
     */
    public function getStoreCode();

    /**
     * Set store code
     *
     * @param string $storeCode
     * @return $this
     */
    public function setStoreCode($storeCode);

    /**
     * Get store name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Set store name
     *
     * @param string $name
     * @return $this
     */
    public function setName($name);

    /**
     * Get latitude of the store
     *
     * @return float|null
     */
    public function getLat();

    /**
     * Set latitude of the store
     *
     * @param float $lat
     * @return $this
     */
    public function setLat($lat);

    /**
     * Get longitude of the store
     *
     * @return float|null
     */
    public function getLong();

    /**
     * Set longitude of the store
     *
     * @param float $long
     * @return $this
     */
    public function setLong($long);

    /**
     * Get description of the store
     *
     * @return string|null
     */
    public function getDescription();

    /**
     * Set description of the store
     *
     * @param string $description
     * @return $this
     */
    public function setDescription($description);

    /**
     * Get phone number of the store
     *
     * @return string|null
     */
    public function getPhone();

    /**
     * Set phone number of the store
     *
     * @param string $phone
     * @return $this
     */
    public function setPhone($phone);

    /**
     * Get address of the store
     *
     * @return string|null
     */
    public function getAddress();

    /**
     * Set address of the store
     *
     * @param string $address
     * @return $this
     */
    public function setAddress($address);

    /**
     * Get state of the store
     *
     * @return string|null
     */
    public function getState();

    /**
     * Set state of the store
     *
     * @param string $state
     * @return $this
     */
    public function setState($state);

    /**
     * Get postcode of the store
     *
     * @return string|null
     */
    public function getPostcode();

    /**
     * Set postcode of the store
     *
     * @param string $postcode
     * @return $this
     */
    public function setPostcode($postcode);

    /**
     * Get electric vehicle (EV) flag for the store
     *
     * @return int|null
     */
    public function getEv();

    /**
     * Set electric vehicle (EV) flag for the store
     *
     * @param int $ev
     * @return $this
     */
    public function setEv($ev);

    /**
     * Get EV charging flag for the store
     *
     * @return int|null
     */
    public function getEvCharging();

    /**
     * Set EV charging flag for the store
     *
     * @param int $evCharging
     * @return $this
     */
    public function setEvCharging($evCharging);

    /**
     * Get enabled status of the store
     *
     * @return int|null
     */
    public function getEnabled();

    /**
     * Set enabled status of the store
     *
     * @param int $enabled
     * @return $this
     */
    public function setEnabled($enabled);

    /**
     * Get driving instructions for the store
     *
     * @return string|null
     */
    public function getDrivingInstructions();

    /**
     * Set driving instructions for the store
     *
     * @param string $drivingInstructions
     * @return $this
     */
    public function setDrivingInstructions($drivingInstructions);

    /**
     * Get public transport instructions for the store
     *
     * @return string|null
     */
    public function getPublicTransportInstructions();

    /**
     * Set public transport instructions for the store
     *
     * @param string $publicTransportInstructions
     * @return $this
     */
    public function setPublicTransportInstructions($publicTransportInstructions);

    /**
     * Get image 1 URL for the store
     *
     * @return string|null
     */
    public function getImage1();

    /**
     * Set image 1 URL for the store
     *
     * @param string $image1
     * @return $this
     */
    public function setImage1($image1);

    /**
     * Get image 2 URL for the store
     *
     * @return string|null
     */
    public function getImage2();

    /**
     * Set image 2 URL for the store
     *
     * @param string $image2
     * @return $this
     */
    public function setImage2($image2);

    /**
     * Get image 3 URL for the store
     *
     * @return string|null
     */
    public function getImage3();

    /**
     * Set image 3 URL for the store
     *
     * @param string $image3
     * @return $this
     */
    public function setImage3($image3);

    /**
     * Get image 4 URL for the store
     *
     * @return string|null
     */
    public function getImage4();

    /**
     * Set image 4 URL for the store
     *
     * @param string $image4
     * @return $this
     */
    public function setImage4($image4);

    /**
     * Get store type for the store
     *
     * @return int|null
     */
    public function getStoreType();

    /**
     * Set store type for the store
     *
     * @param int $storeType
     * @return $this
     */
    public function setStoreType($storeType);

    /**
     * Get waiting room flag for the store
     *
     * @return int|null
     */
    public function getHasWaitingRoom();

    /**
     * Set waiting room flag for the store
     *
     * @param int $hasWaitingRoom
     * @return $this
     */
    public function setHasWaitingRoom($hasWaitingRoom);

    /**
     * Get key drop-off flag for the store
     *
     * @return int|null
     */
    public function getHasKeyDropoff();

    /**
     * Set key drop-off flag for the store
     *
     * @param int $hasKeyDropOff
     * @return $this
     */
    public function setHasKeyDropoff($hasKeyDropOff);

    /**
     * Get store manager name for the store
     *
     * @return string|null
     */
    public function getStoreManager();

    /**
     * Set store manager name for the store
     *
     * @param string $storeManager
     * @return $this
     */
    public function setStoreManager($storeManager);

    /**
     * Get link store flag for the store
     *
     * @return int|null
     */
    public function getIsLinkStore();

    /**
     * Set link store flag for the store
     *
     * @param int $isLinkStore
     * @return $this
     */
    public function setIsLinkStore($isLinkStore);

    /**
     * Get dark store flag for the store
     *
     * @return int|null
     */
    public function getIsDarkStore();

    /**
     * Set dark store flag for the store
     *
     * @param int $isDarkStore
     * @return $this
     */
    public function setIsDarkStore($isDarkStore);

    /**
     * Get time zone for the store
     *
     * @return string|null
     */
    public function getTimezone();

    /**
     * Set time zone for the store
     *
     * @param string $timeZone
     * @return $this
     */
    public function setTimezone($timeZone);

    /**
     * Get mobile flag for the store
     *
     * @return int|null
     */
    public function getIsMobile();

    /**
     * Set mobile flag for the store
     *
     * @param int $isMobile
     * @return $this
     */
    public function setIsMobile($isMobile);

    /**
     * Get Dark Store ID for the store
     *
     * @return int|null
     */
    public function getDarkStoreId();

    /**
     * Set Dark Store ID for the store
     *
     * @param int $darkStoreId
     * @return $this
     */
    public function setDarkStoreId($darkStoreId);

    /**
     * @return string|null
     */
    public function getMobileVan();

    /**
     * @param string $mobileVan
     * @return $this
     */
    public function setMobileVan($mobileVan);

    /**
     *
     * @return int|null
     */
    public function getIsMobilePrimaryVan();

    /**
     * Set mobile flag for the store
     *
     * @param int $isMobilePrimaryVan
     * @return $this
     */
    public function setIsMobilePrimaryVan($isMobilePrimaryVan);

    /**
     * Retrieve existing extension attributes object or create a new one.
     *
     * @return \Webqem\Stores\Api\Data\StoreExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     *
     * @param \Webqem\Stores\Api\Data\StoreExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(\Webqem\Stores\Api\Data\StoreExtensionInterface $extensionAttributes);
}
