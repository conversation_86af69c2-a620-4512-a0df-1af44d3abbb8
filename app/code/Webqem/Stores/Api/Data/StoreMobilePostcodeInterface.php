<?php
namespace Webqem\Stores\Api\Data;

interface StoreMobilePostcodeInterface
{
    /**
     *
     * @return int|null
     */
    public function getId();

    /**
     *
     * @return int|null
     */
    public function setId($id);

    /**
     *
     * @return int|null
     */
    public function getStoreId();

    /**
     *
     * @param int $storeId
     * @return $this
     */
    public function setStoreId($storeId);

    /**
     *
     * @return string|null
     */
    public function getPostcode();

    /**
     *
     * @param string $postcode
     * @return $this
     */
    public function setPostcode($postcode);
}
