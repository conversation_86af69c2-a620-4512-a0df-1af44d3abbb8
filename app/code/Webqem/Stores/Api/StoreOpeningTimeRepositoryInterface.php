<?php
namespace Webqem\Stores\Api;

use Webqem\Stores\Api\Data\StoreOpeningTimeInterface;
use Webqem\Stores\Api\Data\StoreServicesInterface;

interface StoreOpeningTimeRepositoryInterface
{
    /**
     * Save Store.
     *
     * @param StoreOpeningTimeInterface $storeOpeningTime
     * @return StoreOpeningTimeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(StoreOpeningTimeInterface $storeOpeningTime): StoreOpeningTimeInterface;

    /**
     * Retrieve Store.
     *
     * @param int $storeOpeningTimeId
     * @return StoreOpeningTimeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById(int $storeOpeningTimeId): StoreOpeningTimeInterface;

    /**
     * Retrieve Store.
     *
     * @param int $storeId
     * @return StoreOpeningTimeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getStoreOpeningTimeByStoreId($storeId);

    /**
     * Delete Store.
     *
     * @param StoreOpeningTimeInterface $storeOpeningTime
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(StoreOpeningTimeInterface $storeOpeningTime);
}
