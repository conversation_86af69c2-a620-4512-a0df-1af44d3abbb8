<?php
namespace Webqem\Stores\Api;

use Webqem\Stores\Api\Data\StoreTypeInterface;

interface StoreTypeRepositoryInterface
{
    /**
     * Save Store.
     *
     * @param StoreTypeInterface $storeType
     * @return StoreTypeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(StoreTypeInterface $storeType): StoreTypeInterface;

    /**
     * Retrieve Store Type By Id.
     *
     * @param int $storeTypeId
     * @return StoreTypeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById(int $storeTypeId): StoreTypeInterface;

    /**
     * @param string $type
     * @return StoreTypeInterface
     */
    public function getByType(string $type);

    /**
     * Delete Store Type.
     *
     * @param StoreTypeInterface $storeType
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(StoreTypeInterface $storeType);
}
