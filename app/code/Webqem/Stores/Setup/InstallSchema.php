<?php
namespace Webqem\Stores\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\DB\Ddl\Table;

class InstallSchema implements InstallSchemaInterface
{
    /**
     * {@inheritdoc}
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;
        $installer->startSetup();
        if (!$installer->tableExists('mycar_stores')) {
            $table = $installer->getConnection()->newTable(
                $installer->getTable('mycar_stores')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Store ID'
                )
                ->addColumn(
                    'datetime_created',
                    Table::TYPE_TIMESTAMP,
                    null,
                    ['nullable' => false, 'default' => Table::TIMESTAMP_INIT],
                    'Date Time Created'
                )
                ->addColumn(
                    'datetime_updated',
                    Table::TYPE_TIMESTAMP,
                    null,
                    ['nullable' => false, 'default' => Table::TIMESTAMP_INIT_UPDATE],
                    'Date Time Updated'
                )
                ->addColumn(
                    'store_code',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => false],
                    'Store Code'
                )
                ->addColumn(
                    'name',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => false],
                    'Store Name'
                )
                ->addColumn(
                    'lat',
                    Table::TYPE_DECIMAL,
                    null,
                    [
                        'nullable' => false,
                        'precision' => 10,
                        'scale' => 6
                    ],
                    'Latitude'
                )
                ->addColumn(
                    'long',
                    Table::TYPE_DECIMAL,
                    null,
                    [
                        'nullable' => false,
                        'precision' => 10,
                        'scale' => 6
                    ],
                    'Longitude',

                )
                ->addColumn(
                    'description',
                    Table::TYPE_TEXT,
                    null,
                    ['nullable' => true],
                    'Store Description'
                )
                ->addColumn(
                    'phone',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Phone Number'
                )
                ->addColumn(
                    'address',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Store Address'
                )
                ->addColumn(
                    'state',
                    Table::TYPE_TEXT,
                    100,
                    ['nullable' => true],
                    'State'
                )
                ->addColumn(
                    'postcode',
                    Table::TYPE_TEXT,
                    null,
                    ['nullable' => true],
                    'Postcode'
                )
                ->addColumn(
                    'ev',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => 0],
                    'Electric Vehicle Availability'
                )
                ->addColumn(
                    'ev_charging',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => 0],
                    'EV Charging Availability'
                )
                ->addColumn(
                    'enabled',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => false, 'default' => 1],
                    'Is Store Enabled'
                )
                ->addColumn(
                    'driving_instructions',
                    Table::TYPE_TEXT,
                    null,
                    ['nullable' => true],
                    'Driving Instructions'
                )
                ->addColumn(
                    'public_transport_instructions',
                    Table::TYPE_TEXT,
                    null,
                    ['nullable' => true],
                    'Public Transport Instructions'
                )
                ->addColumn(
                    'image1',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Image 1 URL'
                )
                ->addColumn(
                    'image2',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Image 2 URL'
                )
                ->addColumn(
                    'image3',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Image 3 URL'
                )
                ->addColumn(
                    'image4',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Image 4 URL'
                )
                ->addColumn(
                    'store_type',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => true],
                    'Store Type'
                )
                ->addColumn(
                    'has_waiting_room',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => 0],
                    'Has Waiting Room'
                )
                ->addColumn(
                    'has_key_dropoff',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => 0],
                    'Has Key Drop Off'
                )
                ->addColumn(
                    'store_manager',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Store Manager Name'
                )
                ->addColumn(
                    'is_link_store',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => 0],
                    'Is Link Store'
                )
                ->addColumn(
                    'is_dark_store',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => NULL],
                    'Is Dark Store'
                )
                ->addColumn(
                    'dark_store_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => true],
                    'Dark Store ID'
                )
                ->addColumn(
                    'timezone',
                    Table::TYPE_TEXT,
                    100,
                    ['nullable' => true],
                    'Time Zone'
                )
                ->addColumn(
                    'is_mobile',
                    Table::TYPE_BOOLEAN,
                    null,
                    ['nullable' => true, 'default' => 0],
                    'Is Mobile Store'
                )
                ->setComment('Stores Table for MyCar')
                ->setOption('type', 'InnoDB')
                ->setOption('charset', 'utf8');

            $installer->getConnection()->createTable($table);
        }

        if (!$installer->tableExists('mycar_stores_services')) {
            $serviceTable = $installer->getConnection()->newTable(
                $installer->getTable('mycar_stores_services')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Service ID'
                )
                ->addColumn(
                    'store_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Store ID'
                )
                ->addColumn(
                    'product_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Product ID'
                )
                ->addColumn(
                    'available',
                    Table::TYPE_SMALLINT,
                    null,
                    ['nullable' => false, 'default' => 0],
                    'Available'
                )
                ->addForeignKey(
                    $installer->getFkName(
                        'mycar_stores_services',
                        'store_id',
                        'mycar_stores',
                        'id'
                    ),
                    'store_id',
                    $installer->getTable('mycar_stores'),
                    'id',
                    Table::ACTION_CASCADE
                )
                ->addForeignKey(
                    $installer->getFkName(
                        'mycar_stores_services',
                        'product_id',
                        'catalog_product_entity',
                        'entity_id'
                    ),
                    'product_id',
                    $installer->getTable('catalog_product_entity'),
                    'entity_id',
                    Table::ACTION_CASCADE
                )
                ->setComment('Stores Service Table for MyCar')
                ->setOption('type', 'InnoDB')
                ->setOption('charset', 'utf8');

            $installer->getConnection()->createTable($serviceTable);
        }

        if (!$installer->tableExists('mycar_stores_opening_times')) {
            $storeOpeningTimeTable = $installer->getConnection()->newTable(
                $installer->getTable('mycar_stores_opening_times')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Store Opening Time ID'
                )
                ->addColumn(
                    'store_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Store ID'
                )
                ->addColumn(
                    'day',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Store Opening Day'
                )
                ->addColumn(
                    'opening_time',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Store Opening Time'
                )
                ->addColumn(
                    'closing_time',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Store Closing Time'
                )
                ->addForeignKey(
                    $installer->getFkName(
                        'mycar_stores_opening_times',
                        'store_id',
                        'mycar_stores',
                        'id'
                    ),
                    'store_id',
                    $installer->getTable('mycar_stores'),
                    'id',
                    Table::ACTION_CASCADE
                )
                ->setComment('Stores Opening Time Table for MyCar')
                ->setOption('type', 'InnoDB')
                ->setOption('charset', 'utf8');

            $installer->getConnection()->createTable($storeOpeningTimeTable);
        }


        if (!$installer->tableExists('mycar_store_store_type')) {
            $storeTypeTable = $installer->getConnection()->newTable(
                $installer->getTable('mycar_store_store_type')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Store Opening Time ID'
                )
                ->addColumn(
                    'store_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Store ID'
                )
                ->addColumn(
                    'type_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Store ID'
                )
                ->addForeignKey(
                    $installer->getFkName(
                        'mycar_store_store_type',
                        'store_id',
                        'mycar_stores',
                        'id'
                    ),
                    'store_id',
                    $installer->getTable('mycar_stores'),
                    'id',
                    Table::ACTION_CASCADE
                )
                ->addForeignKey(
                    $installer->getFkName(
                        'mycar_store_store_type',
                        'type_id',
                        'mycar_store_type',
                        'id'
                    ),
                    'type_id',
                    $installer->getTable('mycar_store_type'),
                    'id',
                    Table::ACTION_CASCADE
                )
                ->setComment('Stores Type Table for MyCar')
                ->setOption('type', 'InnoDB')
                ->setOption('charset', 'utf8');

            $installer->getConnection()->createTable($storeTypeTable);
        }

        if (!$installer->tableExists('mycar_store_type')) {
            $mycarStoreType = $installer->getConnection()->newTable(
                $installer->getTable('mycar_store_type')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Store Opening Time ID'
                )
                ->addColumn(
                    'type',
                    Table::TYPE_TEXT,
                    255,
                    ['nullable' => true],
                    'Store Type'
                )
                ->setComment('Stores Type Table for MyCar')
                ->setOption('type', 'InnoDB')
                ->setOption('charset', 'utf8');

            $installer->getConnection()->createTable($mycarStoreType);
        }

        if (!$installer->tableExists('mycar_stores_mobile_postcodes')) {
            $mycarStoreMobilePostcodes = $installer->getConnection()->newTable(
                $installer->getTable('mycar_stores_mobile_postcodes')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Store Opening Time ID'
                )
                ->addColumn(
                    'store_id',
                    Table::TYPE_INTEGER,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Store ID'
                )
                ->addColumn(
                    'postcode',
                    Table::TYPE_TEXT,
                    null,
                    ['nullable' => false, 'unsigned' => true],
                    'Store ID'
                )
                ->addForeignKey(
                    $installer->getFkName(
                        'mycar_stores_mobile_postcodes',
                        'store_id',
                        'mycar_stores',
                        'id'
                    ),
                    'store_id',
                    $installer->getTable('mycar_stores'),
                    'id',
                    Table::ACTION_CASCADE
                )
                ->setComment('Stores Mobile Postcodes Table for MyCar')
                ->setOption('type', 'InnoDB')
                ->setOption('charset', 'utf8');

            $installer->getConnection()->createTable($mycarStoreMobilePostcodes);
        }

        $installer->endSetup();
    }
}
