<?php

namespace Webqem\Stores\Block\Adminhtml\Grid\Edit;

class Form extends \Magento\Backend\Block\Widget\Form\Generic implements \Magento\Backend\Block\Widget\Tab\TabInterface
{
    protected $_assetRepo;

    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Framework\View\Asset\Repository $assetRepo,
        array $data = []
    ) {
        $this->_assetRepo = $assetRepo;
        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Prepare form
     *
     * @return $this
     */
    protected function _prepareForm()
    {

        $params = [
            'area' => 'backend'
        ];
        $path = $this->_assetRepo->getUrl("Webqem_Stores::importsample/import_stores.csv", $params);

        $form = $this->_formFactory->create([
            'data' => [
                'id' => 'edit_form',
                'action' => $this->getUrl('*/*/importcsv'),
                'method' => 'post',
                'enctype' => 'multipart/form-data',
            ]
        ]);
        $fieldset = $form->addFieldset(
            'base_fieldset',
            [
                'legend' => __(''),
                'class'  => 'fieldset-wide'
            ]
        );

        $fieldset->addField(
            'file',
            'file',
            [
                'name'  => 'file',
                'label' => __('Upload File'),
                'title' => __('Upload File'),
                'required' => true,
                'note' => __(
                    'Allow File Types: .csv'
                ),
            ]
        )->setAfterElementHtml("
        <span id='sample-file-span' ><a id='sample-file-link' href='".$path."'  download>Download Sample File</a></span>
        ");
        $fieldset->addField(
            'import',
            'submit',
            [
                'name' => 'submit',
                'label' => __(''),
                'title' => __('Import File'),
                'value' => 'Import File'
            ]
        );

        $form->setUseContainer(true);

        $this->setForm($form);
        return parent::_prepareForm();
    }

    /**
     * Prepare label for tab
     *
     * @return string
     */
    public function getTabLabel()
    {
        return __('Import');
    }

    /**
     * Prepare title for tab
     *
     * @return string
     */
    public function getTabTitle()
    {
        return $this->getTabLabel();
    }

    /**
     * Can show tab in tabs
     *
     * @return boolean
     */
    public function canShowTab()
    {
        return true;
    }

    /**
     * Tab is hidden
     *
     * @return boolean
     */
    public function isHidden()
    {
        return false;
    }
}
