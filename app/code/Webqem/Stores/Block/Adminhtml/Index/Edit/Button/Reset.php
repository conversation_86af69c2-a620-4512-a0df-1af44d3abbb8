<?php
declare(strict_types=1);

namespace Webqem\Stores\Block\Adminhtml\Index\Edit\Button;
use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;

/**
 * Reset Button Block
 */
class Reset extends GenericButton implements ButtonProviderInterface
{
    /**
     * @return array
     */
    public function getButtonData()
    {
        return [
            'label' => __('Reset'),
            'on_click' => 'javascript: location.reload();', 'class' => 'reset', 'sort_order' => 30];
    }
}
