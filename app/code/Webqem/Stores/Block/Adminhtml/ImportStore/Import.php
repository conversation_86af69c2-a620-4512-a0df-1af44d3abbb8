<?php

namespace Webqem\Stores\Block\Adminhtml\ImportStore;

class Import extends \Magento\Backend\Block\Widget\Form\Container
{

    protected $_coreRegistry = null;

    public function __construct(
        \Magento\Backend\Block\Widget\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\Response\RedirectInterface $redirect,
        array $data = []
    ) {
        $this->_coreRegistry = $registry;
        $this->redirect = $redirect;
        parent::__construct($context, $data);
    }

    protected function _construct()
    {
        $this->_blockGroup = 'Webqem_Stores';
        $this->_controller = 'adminhtml_grid';
        parent::_construct();
        $this->buttonList->remove('save');
    }

    public function getHeaderText()
    {
        return __('Import Csv');
    }


    public function _prepareLayout()
    {
        $this->_formScripts[] = "
            function toggleEditor() {
                if (tinyMCE.getInstanceById('page_content') == null) {
                    tinyMCE.execCommand('mceAddControl', false, 'page_content');
                } else {
                    tinyMCE.execCommand('mceRemoveControl', false, 'page_content');
                }
            };
        ";
        return parent::_prepareLayout();
    }
}
