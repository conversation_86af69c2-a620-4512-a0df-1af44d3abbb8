<?php

namespace Webqem\Stores\Block\Index;

use Webqem\Stores\Api\StoreRepositoryInterface;
use Magento\Catalog\Block\Product\Context;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\View\Element\Template;

class View extends Template
{
    /**
     * @var StoreRepositoryInterface
     */
    protected $storeRepository;

    /**
     * @var ProductRepositoryInterface
     */
    protected $productRepository;

    /**
     * @param StoreRepositoryInterface $storeRepository
     * @param Context $context
     * @param ProductRepositoryInterface $productRepository
     * @param array $data
     */
    public function __construct(
        StoreRepositoryInterface $storeRepository,
        Context $context,
        ProductRepositoryInterface $productRepository,
        array $data = []
    ) {
        $this->storeRepository = $storeRepository;
        $this->productRepository = $productRepository;
        parent::__construct($context, $data);
    }

    /**
     * @return View
     */
    protected function _prepareLayout()
    {
        $this->store_id = $store_id = $this->getRequest()->getParam('store_id');
        return parent::_prepareLayout();
    }

    /**
     * @return array|null
     */
    public function getStoreDetails()
    {
        try {
            $currentStore = $this->storeRepository->getById($this->store_id);
            $storeData = [
                'name' => $currentStore->getName(),
                'opening_time' => $currentStore->getExtensionAttributes()->getStoreOpeningTime(),
                'services' => $currentStore->getExtensionAttributes()->getStoreServices()
            ];
            return $storeData;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * @param $product_id
     * @return string|null
     */
    public function getServiceName($product_id)
    {
        try {
            $service = $this->productRepository->getById($product_id);
            return $service->getName();
        } catch (\Exception $e) {
            return null;
        }
    }
}
