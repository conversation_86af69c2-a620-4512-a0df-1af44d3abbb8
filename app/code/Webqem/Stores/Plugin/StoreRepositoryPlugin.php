<?php

namespace Webqem\Stores\Plugin;

use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;

class StoreRepositoryPlugin
{
    /**
     * @var \Webqem\Stores\Api\StoreServicesRepositoryInterface
     */
    protected $storeServicesRepository;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var \Webqem\Stores\Model\ResourceModel\StoreServices\CollectionFactory
     */
    protected $storeServicesCollectionFactory;

    /**
     * @var \Webqem\Stores\Api\StoreRepositoryInterface
     */
    protected $storeRepositoryInterface;

    /**
     * @var \Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface
     */
    protected $storeOpeningTimeRepository;

    /**
     * @var \Webqem\Stores\Api\StoreStoreTypeRepositoryInterface
     */
    protected $storeStoreTypeRepository;

    protected $storeTypeRepository;

    protected $storeTypeFactory;

    protected $storeStoreTypeFactory;

    protected $storeMobilePostcodeRepository;

    /**
     * @param \Webqem\Stores\Api\StoreServicesRepositoryInterface $storeServicesRepository
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Webqem\Stores\Model\ResourceModel\StoreServices\CollectionFactory $storeServicesCollectionFactory
     * @param \Webqem\Stores\Api\StoreRepositoryInterface $storeRepositoryInterface
     * @param \Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface $storeOpeningTimeRepository
     * @param \Webqem\Stores\Api\StoreStoreTypeRepositoryInterface $storeStoreTypeRepository
     */
    public function __construct(
        \Webqem\Stores\Api\StoreServicesRepositoryInterface $storeServicesRepository,
        \Psr\Log\LoggerInterface                            $logger,
        \Webqem\Stores\Model\ResourceModel\StoreServices\CollectionFactory $storeServicesCollectionFactory,
        \Webqem\Stores\Api\StoreRepositoryInterface $storeRepositoryInterface,
        \Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface $storeOpeningTimeRepository,
        \Webqem\Stores\Api\StoreStoreTypeRepositoryInterface $storeStoreTypeRepository,
        \Webqem\Stores\Api\StoreTypeRepositoryInterface $storeTypeRepository,
        \Webqem\Stores\Model\StoreTypeFactory $storeTypeFactory,
        \Webqem\Stores\Model\StoreStoreTypeFactory $storeStoreTypeFactory,
        \Webqem\Stores\Api\StoreMobilePostcodeRepositoryInterface $storeMobilePostcodeRepository
    )
    {
        $this->storeServicesRepository = $storeServicesRepository;
        $this->logger = $logger;
        $this->storeServicesCollectionFactory = $storeServicesCollectionFactory;
        $this->storeRepositoryInterface = $storeRepositoryInterface;
        $this->storeOpeningTimeRepository = $storeOpeningTimeRepository;
        $this->storeStoreTypeRepository = $storeStoreTypeRepository;
        $this->storeTypeRepository = $storeTypeRepository;
        $this->storeTypeFactory = $storeTypeFactory;
        $this->storeStoreTypeFactory = $storeStoreTypeFactory;
        $this->storeMobilePostcodeRepository = $storeMobilePostcodeRepository;
    }

    /**
     * @param \Webqem\Stores\Api\StoreRepositoryInterface $subject
     * @param \Webqem\Stores\Api\Data\StoreInterface $result
     * @param \Webqem\Stores\Api\Data\StoreInterface $entity
     * @return \Webqem\Stores\Api\Data\StoreInterface
     * @throws LocalizedException
     */
    public function afterSave(
        \Webqem\Stores\Api\StoreRepositoryInterface $subject,
        \Webqem\Stores\Api\Data\StoreInterface      $result,
        \Webqem\Stores\Api\Data\StoreInterface      $entity
    )
    {
        $extensionAttributes = $entity->getExtensionAttributes();
        $storeServicesData = $extensionAttributes->getStoreServices();
        $storeOpeningTimeData = $extensionAttributes->getStoreOpeningTime();
        $storeTypeData = $extensionAttributes->getType();
        $storeMobilePostcodeData = $extensionAttributes->getStoreMobilePostcode();
        if ($storeServicesData) {
            foreach ($storeServicesData as $storeService) {
                $existingStoreService = $this->storeServicesRepository->getByProductId($storeService->getProductId());
                if ($existingStoreService) {
                    $storeService['id'] = $existingStoreService->getId();
                }
                $storeService['store_id'] = $entity->getId();
                try {
                    $this->storeServicesRepository->save($storeService);
                } catch (\Exception $e) {
                    $this->logger->info($e->getMessage());
                }
            }
            $extensionAttributes->setStoreServices($storeServicesData);
        }
        if ($storeOpeningTimeData) {
            $existingStoreOpeningTime = $this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($entity->getId());
            if ($existingStoreOpeningTime) {
                $storeOpeningTimeData->setId($existingStoreOpeningTime->getId());
            }
            $storeOpeningTimeData->setStoreId($entity->getId());
            try {
                $this->storeOpeningTimeRepository->save($storeOpeningTimeData);
            } catch (\Exception $e) {
                $this->logger->info($e->getMessage());
            }
            $extensionAttributes->setStoreOpeningTime($storeOpeningTimeData);
        }
        if ($storeTypeData) {
            $existingStoreStoreType = $this->storeStoreTypeRepository->getStoreStoreTypeByStoreId($entity->getId());
            if ($existingStoreStoreType) {
                $existingStoreStoreType->setId($existingStoreStoreType->getId());
                $existingStoreStoreType->setStoreId($entity->getId());
            }
            try {
                $existingStoreType = $this->storeTypeRepository->getByType($storeTypeData['type']);
                if (!$existingStoreType) {
                    $newStoreType = $this->storeTypeFactory->create();
                    $newStoreType->setType($storeTypeData['type']);
                    $storeType = $this->storeTypeRepository->save($newStoreType);
                    $newStoreStoreType = $this->storeStoreTypeFactory->create();
                    $newStoreStoreType->setStoreId($storeType->getId());
                    $newStoreStoreType->setTypeId($storeType->getId());
                    $storeStoreType = $this->storeStoreTypeRepository->save($newStoreStoreType);
                } else {
                    $existingStoreType->setType($storeTypeData['type']);
                    $storeType = $this->storeTypeRepository->save($existingStoreType);
                    $existingStoreStoreType->setTypeId($storeType->getId());
                    $storeStoreType = $this->storeStoreTypeRepository->save($existingStoreStoreType);
                }
                $extensionAttributes->setStoreType($storeStoreType);
                $extensionAttributes->setType($storeTypeData);
            } catch (\Exception $e) {
                $this->logger->info($e->getMessage());
            }
        }
        if ($storeMobilePostcodeData) {
            foreach ($storeMobilePostcodeData as $storeMobilePostcode) {
                $existingMobilePostcode = $this->storeMobilePostcodeRepository->getByPostcode($storeMobilePostcode->getPostcode());
                if ($existingMobilePostcode) {
                    $storeMobilePostcode['id'] = $existingMobilePostcode->getId();
                }
                $storeMobilePostcode['store_id'] = $entity->getId();
                try {
                    $this->storeMobilePostcodeRepository->save($storeMobilePostcode);
                } catch (\Exception $e) {
                    $this->logger->info($e->getMessage());
                }
            }
            $extensionAttributes->setStoreMobilePostcode($storeMobilePostcodeData);
        }
        $result->setExtensionAttributes($extensionAttributes);
        try {
            $store = $this->storeRepositoryInterface->getById($result->getId());
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }
        return $store;
    }

    /**
     * @param \Webqem\Stores\Api\StoreRepositoryInterface $subject
     * @param \Webqem\Stores\Api\Data\StoreInterface $resultStore
     * @return \Webqem\Stores\Api\Data\StoreInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterGetById(
        \Webqem\Stores\Api\StoreRepositoryInterface $subject,
        \Webqem\Stores\Api\Data\StoreInterface $resultStore
    ) {
        $extensionAttributes = $resultStore->getExtensionAttributes();
        try {
            $storeServicesList = $this->storeServicesRepository->getListByStoreId($resultStore->getId());
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
            $storeServicesList = [];
        }
        try {
            $storeOpeningTime = $this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($resultStore->getId());
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
            $storeOpeningTime = null;
        }
        try {
            $storeStoreType = $this->storeStoreTypeRepository->getStoreStoreTypeByStoreId($resultStore->getId());
            if ($storeStoreType->getTypeId()) {
                $storeType = $this->storeTypeRepository->getById($storeStoreType->getTypeId());
                $extensionAttributes->setType($storeType);
            }
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
            $storeStoreType = null;
        }
        $extensionAttributes->setStoreServices($storeServicesList);
        $extensionAttributes->setStoreOpeningTime($storeOpeningTime);
        $extensionAttributes->setStoreType($storeStoreType);
        $resultStore->setExtensionAttributes($extensionAttributes);
        return $resultStore;
    }

    public function afterGetList(
        \Webqem\Stores\Api\StoreRepositoryInterface $subject,
        \Magento\Framework\Api\SearchResults $searchResults
    ) : \Magento\Framework\Api\SearchResults {
        $products = [];
        foreach ($searchResults->getItems() as $entity) {
            $extensionAttributes = $entity->getExtensionAttributes();
            $storeServicesList = $this->storeServicesRepository->getListByStoreId($entity->getId());
            $storeOpeningTime = $this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($entity->getId());
            $storeStoreType = $this->storeStoreTypeRepository->getStoreStoreTypeByStoreId($entity->getId());
            if ($storeStoreType->getTypeId()) {
                $storeType = $this->storeTypeRepository->getById($storeStoreType->getTypeId());
                $extensionAttributes->setType($storeType);
            }
            $extensionAttributes->setStoreServices($storeServicesList);
            $extensionAttributes->setStoreOpeningTime($storeOpeningTime);
            $extensionAttributes->setStoreType($storeStoreType);
            $entity->setExtensionAttributes($extensionAttributes);

            $products[] = $entity;
        }
        $searchResults->setItems($products);
        return $searchResults;
    }
}
