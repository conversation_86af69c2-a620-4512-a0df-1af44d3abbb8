<?php

namespace Webqem\Stores\Plugin;

class StoreGet
{
    /**
     * @var \Webqem\Stores\Api\StoreServicesRepositoryInterface
     */
    protected $storeServicesRepository;

    /**
     * @var \Webqem\Stores\Model\StoreFactory
     */
    protected $storeFactory;

    /**
     * @var \Webqem\Stores\Api\Data\StoreExtensionFactory
     */
    protected $storeExtensionFactory;

    /**
     * @var \Webqem\Stores\Api\StoreRepositoryInterface
     */
    protected $storeRepository;

    /**
     * @var \Webqem\Stores\Model\StoreServicesFactory
     */
    protected $storeServicesFactory;

    protected $storeOpeningTimeRepository;

    /**
     * @param \Webqem\Stores\Api\StoreServicesRepositoryInterface $storeServicesRepository
     * @param \Webqem\Stores\Model\StoreFactory $storeFactory
     * @param \Webqem\Stores\Api\Data\StoreExtensionFactory $storeExtensionFactory
     * @param \Webqem\Stores\Api\StoreRepositoryInterface $storeRepository
     * @param \Webqem\Stores\Model\StoreServicesFactory $storeServicesFactory
     * @param \Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface $storeOpeningTimeRepository
     */
    public function __construct(
        \Webqem\Stores\Api\StoreServicesRepositoryInterface $storeServicesRepository,
        \Webqem\Stores\Model\StoreFactory $storeFactory,
        \Webqem\Stores\Api\Data\StoreExtensionFactory $storeExtensionFactory,
        \Webqem\Stores\Api\StoreRepositoryInterface $storeRepository,
        \Webqem\Stores\Model\StoreServicesFactory $storeServicesFactory,
        \Webqem\Stores\Api\StoreOpeningTimeRepositoryInterface $storeOpeningTimeRepository
    )
    {
        $this->storeServicesRepository = $storeServicesRepository;
        $this->storeFactory = $storeFactory;
        $this->storeExtensionFactory = $storeExtensionFactory;
        $this->storeRepository = $storeRepository;
        $this->storeServicesFactory = $storeServicesFactory;
        $this->storeOpeningTimeRepository = $storeOpeningTimeRepository;
    }

    /**
     * @param \Webqem\Stores\Api\StoreRepositoryInterface $subject
     * @param \Webqem\Stores\Api\Data\StoreInterface $resultStore
     * @return \Webqem\Stores\Api\Data\StoreInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterGetById(
        \Webqem\Stores\Api\StoreRepositoryInterface $subject,
        \Webqem\Stores\Api\Data\StoreInterface $resultStore
    ) {
        $storeServicesList = $this->storeServicesRepository->getListByStoreId($resultStore->getId());
        $storeOpeningTime = $this->storeOpeningTimeRepository->getStoreOpeningTimeByStoreId($resultStore->getId());
        $extensionAttributes = $resultStore->getExtensionAttributes();
        $extensionAttributes->setStoreServices($storeServicesList);
        $extensionAttributes->setStoreOpeningTime($storeOpeningTime);
        $resultStore->setExtensionAttributes($extensionAttributes);
        return $resultStore;
    }
}
