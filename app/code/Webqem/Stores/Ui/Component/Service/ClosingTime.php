<?php

namespace Webqem\Stores\Ui\Component\Service;

class ClosingTime implements \Magento\Framework\Data\OptionSourceInterface
{
    public function toOptionArray()
    {
        $timeArray[] = [
            'value' => 'Closed',
            'label' => 'Closed'
        ];
        $time = strtotime('12:00 AM');

        for ($i = 0; $i < 48; $i++) {
            $formattedTime = date('h:i A', $time);
            $timeArray[] = [
                'value' => $formattedTime,
                'label' => __($formattedTime)
            ];
            $time = strtotime('+30 minutes', $time);
        }
        return $timeArray;
    }
}
