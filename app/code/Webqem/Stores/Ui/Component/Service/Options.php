<?php
namespace Webqem\Stores\Ui\Component\Service;

class Options implements \Magento\Framework\Data\OptionSourceInterface
{
    protected $productCollectionFactory;

    protected $attributeSetCollectionFactory;

    public function __construct(
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Eav\Model\ResourceModel\Entity\Attribute\Set\CollectionFactory $attributeSetCollectionFactory
    )
    {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->attributeSetCollectionFactory = $attributeSetCollectionFactory;
    }

    public function getServiceAttributeSetId()
    {
        $serviceAttributeSet = $this->attributeSetCollectionFactory->create()
            ->addFieldToFilter('attribute_set_name', 'Services')
            ->addFieldToFilter('entity_type_id', 4)
            ->getFirstItem();
        return $serviceAttributeSet->getData('attribute_set_id');
    }

    public function getServiceProducts()
    {
        return $this->productCollectionFactory->create()
            ->addAttributeToSelect('*')
            ->addFieldToFilter('attribute_set_id', $this->getServiceAttributeSetId());
    }

    public function toOptionArray()
    {
        $serviceProductsData = [];
        if (count($this->getServiceProducts()) > 0) {
            foreach ($this->getServiceProducts() as $serviceProduct) {
                $serviceProductsData[] = [
                    'value' => $serviceProduct->getId(),
                    'label' => $serviceProduct->getName(),
                ];
            }
            return $serviceProductsData;
        } else {
            return [];
        }
    }
}
