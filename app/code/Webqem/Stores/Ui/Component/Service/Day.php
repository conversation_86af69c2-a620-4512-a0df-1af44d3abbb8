<?php

namespace Webqem\Stores\Ui\Component\Service;

class Day implements \Magento\Framework\Data\OptionSourceInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => 'Monday', 'label' => __('Monday')],
            ['value' => 'Tuesday', 'label' => __('Tuesday')],
            ['value' => 'Wednesday', 'label' => __('Wednesday')],
            ['value' => 'Thursday', 'label' => __('Thursday')],
            ['value' => 'Friday', 'label' => __('Friday')],
            ['value' => 'Saturday', 'label' => __('Saturday')],
            ['value' => 'Sunday', 'label' => __('Sunday')]
        ];
    }
}
