<?php

declare(strict_types=1);

namespace Webqem\Stores\Ui\Component\Listing\Grid\Column;

use Magento\Framework\Escaper;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Store\Model\System\Store as SystemStore;
use Magento\Config\Model\Config\Source\Locale\Timezone;

/**
 *
 * @api
 */
class TimezoneOptions implements OptionSourceInterface
{
    /**
     * @var Escaper
     */
    protected $escaper;

    /**
     * @var SystemStore
     */
    protected $systemStore;

    /**
     * @var array
     */
    protected $options;

    /**
     * @var Timezone
     */
    protected $timeZone;

    /**
     * @var array
     */
    protected $currentOptions = [];

    /**
     * Constructor
     *
     * @param SystemStore $systemStore
     * @param Escaper $escaper
     * @param Timezone $timeZone
     */
    public function __construct(SystemStore $systemStore, Escaper $escaper, Timezone $timeZone)
    {
        $this->systemStore = $systemStore;
        $this->escaper = $escaper;
        $this->timeZone = $timeZone;
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        if ($this->options !== null) {
            return $this->options;
        }


        $allTimeZone = $this->timeZone->toOptionArray();
        foreach ($allTimeZone as $item) {
            if (str_contains($item['value'], 'Australia')) {
                $this->options[] = ['label' => $item['label'], 'value' => $item['value']];
            }
        }

        return $this->options;
    }
}
