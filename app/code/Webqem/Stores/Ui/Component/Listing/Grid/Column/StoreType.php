<?php

declare(strict_types=1);

namespace Webqem\Stores\Ui\Component\Listing\Grid\Column;

use Adyen\Model\Management\Store;
use Magento\Framework\Escaper;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Store\Model\System\Store as SystemStore;
use Webqem\Stores\Model\ResourceModel\StoreType\CollectionFactory as StoreTypeCollectionFactory;

/**
 *
 * @api
 */
class StoreType implements OptionSourceInterface
{
    /**
     * @var Escaper
     */
    protected $escaper;

    /**
     * @var SystemStore
     */
    protected $systemStore;

    /**
     * @var array
     */
    protected $options;

    /**
     * @var StoreTypeCollectionFactory
     */
    protected $storeTypeCollectionFactory;

    /**
     * @var array
     */
    protected $currentOptions = [];

    /**
     * Constructor
     *
     * @param SystemStore $systemStore
     * @param Escaper $escaper
     * @param StoreTypeCollectionFactory $storeTypeCollectionFactory
     */
    public function __construct(SystemStore $systemStore, Escaper $escaper, StoreTypeCollectionFactory $storeTypeCollectionFactory)
    {
        $this->systemStore = $systemStore;
        $this->escaper = $escaper;
        $this->storeTypeCollectionFactory = $storeTypeCollectionFactory;
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        if ($this->options !== null) {
            return $this->options;
        }

        $storeTypeCollection = $this->storeTypeCollectionFactory->create();
        $this->options = [];
        foreach ($storeTypeCollection as $storeType) {
            $this->options[] =
                [
                    'value' => $storeType->getId(),
                    'label' => $storeType->getType()
                ];
        }

        return $this->options;
    }
}
