<?php

declare(strict_types=1);

namespace Webqem\Stores\Ui\Component\Listing\Grid\Column;

use Magento\Framework\Escaper;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Store\Model\System\Store as SystemStore;
use Magento\Directory\Model\Country;

/**
 *
 * @api
 */
class StoreStateOptions implements OptionSourceInterface
{
    /**
     * @var Escaper
     */
    protected $escaper;

    /**
     * @var SystemStore
     */
    protected $systemStore;

    /**
     * @var array
     */
    protected $options;

    /**
     * @var Country
     */
    protected $country;

    /**
     * @var array
     */
    protected $currentOptions = [];

    /**
     * Constructor
     *
     * @param SystemStore $systemStore
     * @param Escaper $escaper
     * @param Timezone $timeZone
     */
    public function __construct(SystemStore $systemStore, Escaper $escaper, Country $country)
    {
        $this->systemStore = $systemStore;
        $this->escaper = $escaper;
        $this->country = $country;
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        if ($this->options !== null) {
            return $this->options;
        }


        $regionCollection = $this->country->loadByCode('AU')->getRegions();
        $regions = $regionCollection->loadData()->toOptionArray();
        return $regions;
    }
}
