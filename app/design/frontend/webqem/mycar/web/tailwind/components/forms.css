/**
 * different styles can be found at https://tailwindcss-forms.vercel.app/
 **/
.form-input,
.form-email,
.form-select,
.form-multiselect,
.form-textarea {
    @apply border border-gray-300 rounded min-h-a11y text-sm;

    &:focus {
        @apply border-primary-lighter ring ring-primary ring-opacity-50;
    }
}

.form-label {
    @apply hidden absolute z-10 text-sm text-primary-lighter min-h-a11y items-center px-3 py-0;
}

.field:has(input:placeholder-shown) .form-label,
.field:has(textarea:placeholder-shown) .form-label {
    @apply flex;
}

.field:has(input:focus) .form-label,
.field:has(textarea:focus) .form-label {
    @apply hidden;
}

.field:has(select) .form-label {
    @apply hidden;
}

.form-select:disabled {
    @apply bg-panel border-panel;
}

[type='checkbox'] {
    @apply border-field rounded w-5 h-5;

    &:focus {
        @apply bg-accent text-accent outline-accent;
    }
}

[type='radio'] {
    @apply border-field w-5 h-5;

    &:focus {
        @apply bg-accent text-accent outline-accent;
    }
}