const {
  spacing
} = require('tailwindcss/defaultTheme');

const colors = require('tailwindcss/colors');

const hyvaModules = require('@hyva-themes/hyva-modules');

module.exports = hyvaModules.mergeTailwindConfig({
  theme: {
    extend: {
      screens: {
        'sm': '640px',
        // => @media (min-width: 640px) { ... }
        'md': '768px',
        // => @media (min-width: 768px) { ... }
        'lg': '1024px',
        // => @media (min-width: 1024px) { ... }
        'xl': '1280px',
        // => @media (min-width: 1280px) { ... }
        '2xl': '1536px' // => @media (min-width: 1536px) { ... }
      },
      fontFamily: {
        sans: ["CircularStd", "sans-serif"]
      },
      colors: {
        primary: {
          lighter: colors.blue['300'],
          "DEFAULT": "#00A6E9",
          darker: colors.blue['900']
        },
        secondary: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        background: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        body: '#222222',
        green: colors.emerald,
        yellow: colors.amber,
        purple: colors.violet,
        accent: {
          lightest: '#26B3ED',
          lighter: '#2E90E0',
          "DEFAULT": '#00A6E9',
          darker: '#004B88',
          darkest: '#06041A'
        }
      },
      textColor: {
        orange: colors.orange,
        red: { ...colors.red,
          "DEFAULT": colors.red['500']
        },
        primary: {
          lighter: "#808285",
          "DEFAULT": "#525455",
          darker: colors.gray['900']
        },
        secondary: {
          lighter: colors.gray['400'],
          "DEFAULT": colors.gray['600'],
          darker: colors.gray['800']
        },
        field: '#525455',
        card: '#525455',
        link: '#808285',
        body: '#222222',
      },
      backgroundColor: {
        primary: {
          lighter: colors.blue['600'],
          "DEFAULT": colors.blue['700'],
          darker: colors.blue['800']
        },
        secondary: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        container: {
          lighter: colors.white,
          "DEFAULT": "#F3F4F6",
          darker: colors.neutral['100']
        },
        tag: {
          red: colors.red,
          yellow: '#FFE486',
          green: colors.green,
          "DEFAULT": '#2E90E0'
        },
        panel: '#E1E3E4',
        confirmation: '#07A843'
      },
      borderColor: {
        primary: {
          lighter: colors.blue['600'],
          "DEFAULT": colors.blue['700'],
          darker: colors.blue['800']
        },
        secondary: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        container: {
          lighter: colors.neutral['100'],
          "DEFAULT": '#e7e7e7',
          darker: '#b6b6b6'
        },
        accent: '#00A6E9',
        panel: '#E1E3E4',
        field: '#D5D6D7',
        button: '#97989B'
      },
      boxShadow: {
        header: "0px 1px 2px 0px rgba(0, 0, 0, 0.06), 0px 1px 3px 0px rgba(0, 0, 0, 0.10)"
      },
      minHeight: {
        a11y: spacing["12"],
        'screen-25': '25vh',
        'screen-50': '50vh',
        'screen-75': '75vh',
        'header-mobile': '3.5rem',
        'header-desktop': '5rem',
        'button': '3rem'
      },
      maxHeight: {
        'screen-25': '25vh',
        'screen-50': '50vh',
        'screen-75': '75vh'
      },
      container: {
        center: true,
        padding: spacing["4"]
      }
    }
  },
  plugins: [require('@tailwindcss/forms'), require('@tailwindcss/typography')],
  // Examples for excluding patterns from purge
  content: [
    // this theme's phtml and layout XML files
    '../../**/*.phtml',
    '../../*/layout/*.xml',
    '../../*/page_layout/override/base/*.xml',
    // parent theme in Vendor (if this is a child-theme)
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml',
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/layout/*.xml',
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/page_layout/override/base/*.xml',
    // app/code phtml files (if need tailwind classes from app/code modules)
    '../../../../../../../app/code/**/*.phtml',
  ]
});
