<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
?>
<div class="text-gray-700 body-font bg-container-darker border-t border-container-darker shadow pb-16">
    <div class="container py-8 mx-auto">
        <div class="flex flex-wrap order-first gap-y-16">
            <?php foreach ($block->getChildNames() as $childName): ?>
                <?= $childName !== 'footer-copyright' ? $block->getBlockHtml($childName) : '' ?>
            <?php endforeach; ?>
        </div>
    </div>
    <div class="bg-container border-t border-b border-container">
        <div class="container py-6 mx-auto flex items-center sm:flex-row flex-col">
            <a  href="https://hyva.io"
                class="flex title-font font-medium items-center md:justify-start justify-center text-gray-900">
                <span class="ml-3 text-xl">Hyvä Themes</span>
            </a>
            <p>
                <?= $block->getChildHtml('copyright') ?>
            </p>
        </div>
    </div>
</div>



<script>
    function initFooter () {
        return {
            init() {}
        }
    }
</script>

<section class="footer-contact bg-accent-darker">
    <div class="container flex flex-col md:flex-row gap-4 md:gap-12 py-[30px] md:py-[60px] text-center">
        <div class="footer-card bg-white rounded-lg py-[30px] md:py-[40px] px-[20px] flex-1">
            <svg alt="Chat icon" class="mx-auto mb-4" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.17534 22.1251C4.68645 19.6131 4.16565 16.644 4.71072 13.7753C5.25579 10.9065 6.82925 8.33537 9.13569 6.5445C11.4421 4.75364 14.323 3.86623 17.2373 4.04888C20.1517 4.23152 22.8992 5.47165 24.964 7.53646C27.0288 9.60127 28.2689 12.3488 28.4516 15.2631C28.6342 18.1775 27.7468 21.0583 25.9559 23.3647C24.1651 25.6712 21.5939 27.2446 18.7252 27.7897C15.8564 28.3348 12.8873 27.814 10.3753 26.3251V26.3251L6.22534 27.5001C6.05531 27.5498 5.87503 27.5529 5.70341 27.509C5.53178 27.4651 5.37513 27.3758 5.24986 27.2506C5.1246 27.1253 5.03534 26.9686 4.99144 26.797C4.94753 26.6254 4.95061 26.4451 5.00034 26.2751L6.17534 22.1251Z" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <h3 class="text-xl font-semibold mb-2">Chat with us</h3>
            <ul class="text-card text-sm md:text-base min-h-[4em]">
                <li>Mon to Fri 8.00am - 6.30pm</li>
                <li>Saturday 8.00am - 5.00pm</li>
            </ul>
            <a class="text-link text-sm md:text-base underline" href="#">Chat now</a>
        </div>
        <div alt="Telephone icon" class="footer-card bg-white rounded-lg py-[30px] md:py-[40px] px-[20px] flex-1">
            <svg class="mx-auto mb-4" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.5625 15.6C12.5915 17.7251 14.3098 19.439 16.4375 20.4625C16.5944 20.5369 16.768 20.569 16.9412 20.5559C17.1143 20.5427 17.2811 20.4847 17.425 20.3875L20.55 18.3C20.6881 18.2064 20.8476 18.1493 21.0137 18.134C21.1799 18.1187 21.3472 18.1457 21.5 18.2125L27.35 20.725C27.5499 20.8083 27.717 20.9549 27.8254 21.1424C27.9338 21.3298 27.9776 21.5477 27.95 21.7625C27.7646 23.2097 27.0582 24.5397 25.9631 25.5038C24.8679 26.4678 23.459 26.9997 22 27C17.4913 27 13.1673 25.209 9.97919 22.0209C6.79107 18.8327 5 14.5087 5 10C5.00033 8.54104 5.53227 7.13214 6.49628 6.03699C7.4603 4.94183 8.79033 4.23546 10.2375 4.05004C10.4523 4.02245 10.6702 4.06623 10.8577 4.17465C11.0452 4.28307 11.1918 4.4501 11.275 4.65004L13.7875 10.5125C13.8528 10.663 13.8802 10.8272 13.8671 10.9907C13.854 11.1541 13.8009 11.3119 13.7125 11.45L11.625 14.625C11.5321 14.7686 11.4775 14.9337 11.4666 15.1044C11.4556 15.2751 11.4887 15.4457 11.5625 15.6V15.6Z" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <h3 class="text-xl font-semibold mb-2">Call us 13 13 28</h3>
            <p class="text-card text-sm md:text-base min-h-[4em]">Speak to our local team</p>
            <a class="text-link text-sm md:text-base underline" href="tel:131328">Call us on 13 13 28</a>
        </div>
        <div alt="Store icon" class="footer-card bg-white rounded-lg py-[30px] md:py-[40px] px-[20px] flex-1">
            <svg class="mx-auto mb-4" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.5 17.4502V26.0002C6.5 26.2654 6.60536 26.5198 6.79289 26.7073C6.98043 26.8948 7.23478 27.0002 7.5 27.0002H25.5C25.7652 27.0002 26.0196 26.8948 26.2071 26.7073C26.3946 26.5198 26.5 26.2654 26.5 26.0002V17.4502" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7.25 5H25.75C25.9669 5.00179 26.1776 5.0732 26.3508 5.20373C26.5241 5.33425 26.6509 5.51698 26.7125 5.725L28.5 12H4.5L6.2875 5.725C6.34909 5.51698 6.47587 5.33425 6.64916 5.20373C6.82244 5.0732 7.03306 5.00179 7.25 5V5Z" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12.5 12V14C12.5 15.0609 12.0786 16.0783 11.3284 16.8284C10.5783 17.5786 9.56087 18 8.5 18C7.43913 18 6.42172 17.5786 5.67157 16.8284C4.92143 16.0783 4.5 15.0609 4.5 14V12" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20.5 12V14C20.5 15.0609 20.0786 16.0783 19.3284 16.8284C18.5783 17.5786 17.5609 18 16.5 18C15.4391 18 14.4217 17.5786 13.6716 16.8284C12.9214 16.0783 12.5 15.0609 12.5 14V12" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M28.5 12V14C28.5 15.0609 28.0786 16.0783 27.3284 16.8284C26.5783 17.5786 25.5609 18 24.5 18C23.4391 18 22.4217 17.5786 21.6716 16.8284C20.9214 16.0783 20.5 15.0609 20.5 14V12" stroke="#00A6E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <h3 class="text-xl font-semibold mb-2">Visit us in-store</h3>
            <p class="text-card text-sm md:text-base min-h-[4em]">With over 260 stores mycar is just around the corner.</p>
            <a class="text-link text-sm md:text-base underline" href="#">Find your nearest store</a>
        </div>
    </div>
</section>

<section class="footer-content bg-white">
    <div class="container py-5 md:py-[60px] grid grid-cols-[1fr] md:grid-cols-[2fr_7fr] gap-5 md:gap-[64px]">
    
        <div class="footer-meta order-2 md:order-1">
            <svg class="footer-logo mb-5" width="103" height="41" viewBox="0 0 103 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M28.2997 13.5647C28.2997 15.5132 28.2997 17.5396 28.2997 19.488C28.2997 19.7219 28.2218 19.7219 27.988 19.7219C26.8189 19.7219 25.6498 19.7219 24.4807 19.7219C24.2469 19.7219 24.2469 19.7219 24.2469 19.488C24.2469 15.747 24.2469 11.928 24.2469 8.18694C24.2469 6.55022 23.1558 4.99145 21.597 4.52382C19.2589 3.74443 16.7648 5.22527 16.3751 7.7193C16.2972 7.95312 16.2972 8.26487 16.2972 8.49869C16.2972 12.1618 16.2972 15.8249 16.2972 19.488C16.2972 19.7219 16.2192 19.7998 15.9854 19.7998C14.8163 19.7998 13.6473 19.7998 12.4782 19.7998C12.2444 19.7998 12.1664 19.7219 12.1664 19.488C12.1664 15.747 12.1664 12.0839 12.1664 8.34281C12.1664 7.17373 11.7767 6.16053 10.9194 5.38114C10.0621 4.60176 9.04889 4.29 7.87981 4.36794C6.00928 4.44588 4.52844 5.77084 4.21669 7.7193C4.21669 7.87518 4.21669 8.109 4.21669 8.26487C4.21669 12.0059 4.21669 15.747 4.21669 19.488C4.21669 19.7219 4.13875 19.7219 3.90493 19.7219C2.73585 19.7219 1.48883 19.7219 0.319754 19.7219C0.163876 19.7219 0.0859375 19.7219 0.0859375 19.488C0.0859375 15.747 0.0859375 11.928 0.0859375 8.18694C0.0859375 4.21206 2.81379 1.17245 6.39897 0.393066C8.89301 -0.152505 11.2312 0.393066 13.2576 2.02978C13.4914 2.26359 13.8031 2.49741 14.037 2.73123C14.1149 2.80917 14.1928 2.8871 14.2708 2.73123C15.4399 1.48421 16.8428 0.70482 18.4795 0.393066C22.7661 -0.46426 27.0527 1.95184 28.0659 6.55022C28.1439 7.09579 28.2218 7.56343 28.2218 8.109C28.2997 9.90159 28.2997 11.7721 28.2997 13.5647Z" fill="#00A6E9"/>
                <path d="M75.3759 16.3706C75.4538 16.3706 75.6097 16.3706 75.6877 16.3706C75.7656 16.3706 75.9215 16.3706 75.9994 16.3706C78.2596 16.1368 79.8184 14.8898 80.6757 12.7854C81.1434 11.6164 81.2213 10.3693 81.0654 9.12232C80.8316 7.40767 80.0522 6.08271 78.6493 5.0695C77.4803 4.29012 76.0774 3.97836 74.6745 4.13424C72.726 4.36806 71.2452 5.38126 70.3099 7.09591C69.6084 8.42087 69.4526 9.9017 69.6864 11.3825C69.8423 12.5516 70.3099 13.6428 71.1672 14.578C72.2584 15.825 73.6613 16.3706 75.3759 16.3706ZM81.1434 17.15C80.9096 17.4618 80.7537 17.7735 80.5199 18.0073C79.3508 19.2543 77.9479 19.9558 76.3112 20.1896C73.8171 20.5793 71.479 20.1117 69.3746 18.6308C67.8938 17.6176 66.8806 16.2147 66.2571 14.5001C65.6336 12.7854 65.4777 11.0708 65.5556 9.27819C65.6336 7.79736 65.9453 6.39446 66.6468 5.0695C67.582 3.27691 68.9849 1.95196 70.8555 1.09463C72.1804 0.471121 73.6613 0.237305 75.1421 0.237305C76.8567 0.237305 78.4935 0.782876 79.8964 1.87402C80.2861 2.18577 80.6757 2.6534 80.9875 3.0431C80.9875 3.12104 81.0654 3.19898 81.1434 3.19898C81.2213 3.19898 81.2213 3.12104 81.2213 3.0431C81.2213 2.34165 81.2993 1.6402 81.2993 1.01669C81.2993 0.860814 81.2993 0.782876 81.5331 0.782876C82.7022 0.782876 83.8712 0.782876 85.0403 0.782876C85.1962 0.782876 85.2741 0.782876 85.2741 1.01669C85.2741 7.17385 85.2741 13.331 85.2741 19.5661C85.2741 19.722 85.1962 19.7999 85.0403 19.7999C83.8712 19.7999 82.7801 19.7999 81.611 19.7999C81.4551 19.7999 81.3772 19.7999 81.3772 19.5661C81.3772 18.9426 81.2993 18.2411 81.2213 17.6176C81.1434 17.4618 81.1434 17.3059 81.1434 17.15Z" fill="#808285"/>
                <path d="M33.3652 14.4218C34.1446 15.2791 35.0799 15.8247 36.249 16.1365C37.3401 16.3703 38.4312 16.4482 39.4444 15.9806C40.9253 15.435 41.7047 14.2659 42.0164 12.7851C42.0944 12.3175 42.1723 11.7719 42.1723 11.2263C41.7047 11.5381 41.315 11.7719 40.8473 11.9278C39.6783 12.3954 38.5092 12.5513 37.2622 12.4733C34.3784 12.1616 32.2741 10.7587 30.9491 8.1867C30.4036 7.09556 30.0918 5.92648 30.0918 4.67946C30.0918 3.43244 30.0918 2.18543 30.0918 0.938406C30.0918 0.782528 30.0918 0.70459 30.3256 0.70459C31.5726 0.70459 32.7417 0.70459 33.9887 0.70459C34.1446 0.70459 34.2225 0.782528 34.2225 0.938406C34.2225 2.10749 34.2225 3.27657 34.2225 4.44565C34.2225 5.84854 34.7681 6.93969 35.9372 7.71907C38.0415 9.19991 41.3929 8.34258 42.0944 5.38091C42.1723 5.06916 42.1723 4.7574 42.1723 4.44565C42.1723 3.27657 42.1723 2.10749 42.1723 1.01634C42.1723 0.860467 42.1723 0.782529 42.4061 0.782529C43.6531 0.782529 44.8222 0.782529 46.0692 0.782529C46.2251 0.782529 46.303 0.782529 46.303 1.01634C46.303 4.7574 46.303 8.42052 46.303 12.1616C46.303 14.0321 45.7575 15.7468 44.6663 17.3055C43.5752 18.8643 42.0944 19.7216 40.2238 20.1893C39.0547 20.4231 37.9636 20.4231 36.7945 20.3451C34.5343 20.1113 32.6638 19.0981 31.105 17.4614C31.0271 17.3835 30.9491 17.3055 31.105 17.2276C31.6506 16.2144 32.5079 15.3571 33.3652 14.4218Z" fill="#00A6E9"/>
                <path d="M57.3706 20.3455C55.3442 20.2676 53.3957 19.8779 51.6811 18.7088C49.8885 17.4618 48.7194 15.825 48.1738 13.7986C47.4724 11.2267 47.4724 8.73262 48.4076 6.23858C49.4208 3.43279 51.3693 1.6402 54.253 0.782876C55.3442 0.315243 56.5133 0.237305 57.6044 0.237305C59.7867 0.315243 61.7352 1.01669 63.3719 2.41959C63.6057 2.57547 63.7616 2.80928 63.9954 2.96516C64.1513 3.0431 64.0733 3.12104 63.9954 3.27691C63.2939 3.97836 62.6704 4.67981 61.969 5.38126C61.891 5.4592 61.8131 5.53714 61.7352 5.69301C61.5793 5.84889 61.5013 5.84889 61.4234 5.69301C61.1116 5.38126 60.722 5.14744 60.2543 4.91363C58.15 3.90042 56.1236 3.90042 54.0972 5.22538C52.9281 6.00477 52.2266 7.17385 51.9149 8.57675C51.5252 10.2135 51.6811 11.7722 52.4605 13.331C53.3957 15.2015 54.9545 16.1368 57.0588 16.2927C58.3838 16.3706 59.5529 16.1368 60.644 15.4354C61.0337 15.2015 61.3455 14.8898 61.6572 14.578C61.7352 14.5001 61.8131 14.5001 61.891 14.578C62.5145 15.2795 63.216 15.9809 63.9174 16.6824C63.9954 16.7603 64.0733 16.8382 64.1513 16.9162C64.463 17.3059 64.463 17.3059 64.1513 17.6176C62.7484 18.9426 61.1896 19.8779 59.2411 20.1896C58.6176 20.2676 57.9941 20.3455 57.3706 20.3455Z" fill="#808285"/>
                <path d="M92.4433 2.80929C92.6771 2.41959 92.9888 2.0299 93.3006 1.71815C94.08 0.938759 95.0152 0.549066 96.1064 0.31525C97.821 0.00349502 99.3798 0.237311 100.939 1.0167C101.328 1.25051 101.718 1.48433 102.108 1.79609C102.186 1.87402 102.186 1.95196 102.186 2.0299C101.64 3.12104 101.094 4.21218 100.549 5.30333C100.471 5.53714 100.393 5.4592 100.237 5.38127C99.5357 4.83569 98.8342 4.446 97.899 4.36806C96.2623 4.21218 94.7814 4.52394 93.6123 5.77096C92.9109 6.47241 92.5991 7.40767 92.4433 8.34294C92.3653 8.65469 92.3653 9.04438 92.3653 9.35614C92.3653 12.7075 92.3653 16.0589 92.3653 19.4102C92.3653 19.644 92.3653 19.644 92.1315 19.644C90.9624 19.644 89.7154 19.644 88.5463 19.644C88.3904 19.644 88.3125 19.5661 88.3125 19.4102C88.3125 13.2531 88.3125 7.09592 88.3125 0.938759C88.3125 0.782882 88.3125 0.704943 88.5463 0.704943C89.6375 0.704943 90.8065 0.704943 91.8977 0.704943C92.0536 0.704943 92.1315 0.704943 92.1315 0.938759C92.2094 1.48433 92.2094 2.0299 92.2874 2.65341C92.3653 2.73135 92.3653 2.80929 92.4433 2.80929Z" fill="#808285"/>
                <path d="M17.3865 30.8672V38.2714H15.2822V30.8672H12.3984V28.8408H20.3482V30.8672H17.3865Z" fill="#808285"/>
                <path d="M21.2859 40.8428L22.7667 37.4135L19.9609 31.646H22.2212L23.8579 35.2312L25.3387 31.646H27.4431L23.3902 40.8428H21.2859Z" fill="#808285"/>
                <path d="M32.7419 33.6732C32.5081 33.5953 32.2743 33.5953 32.1184 33.5953C31.339 33.5953 30.5596 34.0629 30.5596 35.3878V38.2716H28.5332V31.6468H30.4817V32.5041C30.7934 31.7247 31.6508 31.5688 32.1963 31.5688C32.4301 31.5688 32.586 31.5688 32.7419 31.6468V33.6732Z" fill="#808285"/>
                <path d="M38.0417 34.2186C38.0417 33.673 37.652 33.1274 36.7168 33.1274C35.8595 33.1274 35.4698 33.7509 35.3918 34.2186H38.0417ZM39.9123 36.4009C39.6005 37.5699 38.5094 38.5052 36.8727 38.5052C35.0801 38.5052 33.4434 37.1802 33.4434 34.998C33.4434 32.8936 35.0021 31.4907 36.7168 31.4907C38.7432 31.4907 40.0681 32.7377 40.0681 34.92C40.0681 35.2318 40.0681 35.5435 39.9902 35.5435H35.3918C35.4698 36.3229 36.0933 36.7906 36.8727 36.7906C37.5741 36.7906 38.0417 36.4788 38.1976 35.9332L39.9123 36.4009Z" fill="#808285"/>
                <path d="M47.3937 31.958L47.6275 32.1918L48.0172 31.958C48.4069 31.6462 48.5628 31.3345 48.5628 31.0227C48.5628 30.5551 48.1731 30.2433 47.7054 30.2433C47.3157 30.2433 46.9261 30.4772 46.9261 31.0227C46.9261 31.4124 47.1599 31.7242 47.3937 31.958ZM48.7966 36.0887L47.3157 34.53L47.2378 34.6079C46.7702 34.9197 46.5364 35.2314 46.5364 35.777C46.5364 36.2446 46.9261 36.7123 47.5496 36.7123C48.0172 36.7123 48.4069 36.5564 48.7966 36.0887ZM49.9657 37.4137C49.3422 38.1152 48.4848 38.5048 47.4716 38.5048C45.6011 38.5048 44.5879 37.1799 44.5879 35.9329C44.5879 34.7638 45.2114 34.0623 46.2246 33.3609L46.0687 33.205C45.6011 32.7374 45.0555 32.0359 45.0555 31.1786C45.0555 29.5419 46.3805 28.6846 47.7054 28.6846C49.2642 28.6846 50.4333 29.6978 50.4333 31.1007C50.4333 32.0359 49.8098 32.8153 49.1083 33.283L48.7966 33.5168L50.0436 34.8417L52.5376 32.2697V34.7638L51.2127 36.0887L53.317 38.349H50.745L49.9657 37.4137Z" fill="#808285"/>
                <path d="M59.6301 34.4521H61.8903L60.7212 31.1787L59.6301 34.4521ZM62.5918 36.4006H58.9286L58.2272 38.349H56.0449L59.6301 28.9185H62.0462L65.5534 38.2711H63.2932L62.5918 36.4006Z" fill="#808285"/>
                <path d="M70.3081 37.6473C69.9964 38.1928 69.217 38.4267 68.5155 38.4267C66.8788 38.4267 66.0215 37.2576 66.0215 35.8547V31.646H68.0479V35.3871C68.0479 36.0106 68.3596 36.5561 69.139 36.5561C69.8405 36.5561 70.2302 36.0885 70.2302 35.3871V31.646H72.2566V37.1017C72.2566 37.7252 72.3345 38.1928 72.3345 38.2708H70.386C70.386 38.1928 70.3081 37.8032 70.3081 37.6473Z" fill="#808285"/>
                <path d="M76.4652 31.6464H77.7902V33.439H76.4652V35.933C76.4652 36.4786 76.777 36.6345 77.1666 36.6345C77.4005 36.6345 77.5563 36.5565 77.7122 36.5565V38.2712C77.6343 38.3491 77.3225 38.4271 76.6211 38.4271C75.2182 38.4271 74.4388 37.6477 74.4388 36.3227V33.5169H73.3477V31.7243H73.6594C74.3609 31.7243 74.6726 31.2567 74.6726 30.6332V29.7759H76.4652V31.6464Z" fill="#808285"/>
                <path d="M83.7146 34.998C83.7146 33.9068 83.0132 33.3613 82.2338 33.3613C81.4544 33.3613 80.753 33.9068 80.753 34.998C80.753 36.0891 81.4544 36.6347 82.2338 36.6347C82.9352 36.6347 83.7146 36.0891 83.7146 34.998ZM85.741 34.998C85.741 37.0244 84.1823 38.5052 82.2338 38.5052C80.2853 38.5052 78.7266 37.0244 78.7266 34.998C78.7266 32.9716 80.2853 31.4907 82.2338 31.4907C84.1823 31.4907 85.741 32.8936 85.741 34.998Z" fill="#808285"/>
            </svg>
            <p class="footer-country text-sm mb-6">We acknowledge the Traditional Owners of the lands on which we work and live. We pay our respect to Elders past, present and emerging and recognise their continuing connection to land, waters and culture.</p>
            <ul class="footer-social flex gap-6 mb-2">
                <li>
                    <a href="#" target="_blank">
                        <svg alt="Facebook" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 17.9895 4.3882 22.954 10.125 23.8542V15.4688H7.07812V12H10.125V9.35625C10.125 6.34875 11.9166 4.6875 14.6576 4.6875C15.9701 4.6875 17.3438 4.92188 17.3438 4.92188V7.875H15.8306C14.34 7.875 13.875 8.80008 13.875 9.75V12H17.2031L16.6711 15.4688H13.875V23.8542C19.6118 22.954 24 17.9895 24 12Z" fill="#525455"/>
                            <path d="M16.6711 15.4688L17.2031 12H13.875V9.75C13.875 8.80102 14.34 7.875 15.8306 7.875H17.3438V4.92188C17.3438 4.92188 15.9705 4.6875 14.6576 4.6875C11.9166 4.6875 10.125 6.34875 10.125 9.35625V12H7.07812V15.4688H10.125V23.8542C11.3674 24.0486 12.6326 24.0486 13.875 23.8542V15.4688H16.6711Z" fill="white"/>
                        </svg>
                    </a>
                </li>
                <li>
                    <a href="#" target="_blank">
                        <svg alt="Instagram" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2.16094C15.2063 2.16094 15.5859 2.175 16.8469 2.23125C18.0188 2.28281 18.6516 2.47969 19.0734 2.64375C19.6313 2.85938 20.0344 3.12188 20.4516 3.53906C20.8734 3.96094 21.1313 4.35938 21.3469 4.91719C21.5109 5.33906 21.7078 5.97656 21.7594 7.14375C21.8156 8.40937 21.8297 8.78906 21.8297 11.9906C21.8297 15.1969 21.8156 15.5766 21.7594 16.8375C21.7078 18.0094 21.5109 18.6422 21.3469 19.0641C21.1313 19.6219 20.8687 20.025 20.4516 20.4422C20.0297 20.8641 19.6313 21.1219 19.0734 21.3375C18.6516 21.5016 18.0141 21.6984 16.8469 21.75C15.5813 21.8062 15.2016 21.8203 12 21.8203C8.79375 21.8203 8.41406 21.8062 7.15313 21.75C5.98125 21.6984 5.34844 21.5016 4.92656 21.3375C4.36875 21.1219 3.96563 20.8594 3.54844 20.4422C3.12656 20.0203 2.86875 19.6219 2.65313 19.0641C2.48906 18.6422 2.29219 18.0047 2.24063 16.8375C2.18438 15.5719 2.17031 15.1922 2.17031 11.9906C2.17031 8.78438 2.18438 8.40469 2.24063 7.14375C2.29219 5.97187 2.48906 5.33906 2.65313 4.91719C2.86875 4.35938 3.13125 3.95625 3.54844 3.53906C3.97031 3.11719 4.36875 2.85938 4.92656 2.64375C5.34844 2.47969 5.98594 2.28281 7.15313 2.23125C8.41406 2.175 8.79375 2.16094 12 2.16094ZM12 0C8.74219 0 8.33438 0.0140625 7.05469 0.0703125C5.77969 0.126563 4.90313 0.332812 4.14375 0.628125C3.35156 0.9375 2.68125 1.34531 2.01563 2.01562C1.34531 2.68125 0.9375 3.35156 0.628125 4.13906C0.332812 4.90313 0.126563 5.775 0.0703125 7.05C0.0140625 8.33437 0 8.74219 0 12C0 15.2578 0.0140625 15.6656 0.0703125 16.9453C0.126563 18.2203 0.332812 19.0969 0.628125 19.8563C0.9375 20.6484 1.34531 21.3188 2.01563 21.9844C2.68125 22.65 3.35156 23.0625 4.13906 23.3672C4.90313 23.6625 5.775 23.8687 7.05 23.925C8.32969 23.9812 8.7375 23.9953 11.9953 23.9953C15.2531 23.9953 15.6609 23.9812 16.9406 23.925C18.2156 23.8687 19.0922 23.6625 19.8516 23.3672C20.6391 23.0625 21.3094 22.65 21.975 21.9844C22.6406 21.3188 23.0531 20.6484 23.3578 19.8609C23.6531 19.0969 23.8594 18.225 23.9156 16.95C23.9719 15.6703 23.9859 15.2625 23.9859 12.0047C23.9859 8.74688 23.9719 8.33906 23.9156 7.05938C23.8594 5.78438 23.6531 4.90781 23.3578 4.14844C23.0625 3.35156 22.6547 2.68125 21.9844 2.01562C21.3188 1.35 20.6484 0.9375 19.8609 0.632812C19.0969 0.3375 18.225 0.13125 16.95 0.075C15.6656 0.0140625 15.2578 0 12 0Z" fill="#525455"/>
                            <path d="M12 5.83594C8.59688 5.83594 5.83594 8.59688 5.83594 12C5.83594 15.4031 8.59688 18.1641 12 18.1641C15.4031 18.1641 18.1641 15.4031 18.1641 12C18.1641 8.59688 15.4031 5.83594 12 5.83594ZM12 15.9984C9.79219 15.9984 8.00156 14.2078 8.00156 12C8.00156 9.79219 9.79219 8.00156 12 8.00156C14.2078 8.00156 15.9984 9.79219 15.9984 12C15.9984 14.2078 14.2078 15.9984 12 15.9984Z" fill="#525455"/>
                            <path d="M19.8469 5.59214C19.8469 6.38902 19.2 7.03121 18.4078 7.03121C17.6109 7.03121 16.9688 6.38433 16.9688 5.59214C16.9688 4.79526 17.6156 4.15308 18.4078 4.15308C19.2 4.15308 19.8469 4.79995 19.8469 5.59214Z" fill="#525455"/>
                        </svg>
                    </a>
                </li>
                <li>
                    <a href="#" target="_blank">
                        <svg alt="LinkedIn" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22.2283 0H1.77167C1.30179 0 0.851161 0.186657 0.518909 0.518909C0.186657 0.851161 0 1.30179 0 1.77167V22.2283C0 22.6982 0.186657 23.1488 0.518909 23.4811C0.851161 23.8133 1.30179 24 1.77167 24H22.2283C22.6982 24 23.1488 23.8133 23.4811 23.4811C23.8133 23.1488 24 22.6982 24 22.2283V1.77167C24 1.30179 23.8133 0.851161 23.4811 0.518909C23.1488 0.186657 22.6982 0 22.2283 0ZM7.15333 20.445H3.545V8.98333H7.15333V20.445ZM5.34667 7.395C4.93736 7.3927 4.53792 7.2692 4.19873 7.04009C3.85955 6.81098 3.59584 6.48653 3.44088 6.10769C3.28591 5.72885 3.24665 5.31259 3.32803 4.91145C3.40941 4.51032 3.6078 4.14228 3.89816 3.85378C4.18851 3.56529 4.55782 3.36927 4.95947 3.29046C5.36112 3.21165 5.77711 3.25359 6.15495 3.41099C6.53279 3.56838 6.85554 3.83417 7.08247 4.17481C7.30939 4.51546 7.43032 4.91569 7.43 5.325C7.43386 5.59903 7.38251 5.87104 7.27901 6.1248C7.17551 6.37857 7.02198 6.6089 6.82757 6.80207C6.63316 6.99523 6.40185 7.14728 6.14742 7.24915C5.893 7.35102 5.62067 7.40062 5.34667 7.395ZM20.4533 20.455H16.8467V14.1933C16.8467 12.3467 16.0617 11.7767 15.0483 11.7767C13.9783 11.7767 12.9283 12.5833 12.9283 14.24V20.455H9.32V8.99167H12.79V10.58H12.8367C13.185 9.875 14.405 8.67 16.2667 8.67C18.28 8.67 20.455 9.865 20.455 13.365L20.4533 20.455Z" fill="#525455"/>
                        </svg>
                    </a>
                </li>
                <li>
                    <a href="#" target="_blank">
                        <svg alt="TikTok" width="21" height="24" viewBox="0 0 448 512" xmlns="http://www.w3.org/2000/svg">
                            <path d="M448 209.9a210.1 210.1 0 0 1 -122.8-39.3V349.4A162.6 162.6 0 1 1 185 188.3V278.2a74.6 74.6 0 1 0 52.2 71.2V0l88 0a121.2 121.2 0 0 0 1.9 22.2h0A122.2 122.2 0 0 0 381 102.4a121.4 121.4 0 0 0 67 20.1z"/>
                        </svg>
                    </a>
                </li>
            </ul>
        </div>

        <nav class="footer-nav order-1 md:order-2">
            <ul class="flex flex-col md:flex-row md:gap-[32px] justify-between">
                <li class="border-b md:border-0 border-panel pb-2 mb-3" x-data="{open: false}">
                    <div class="flex gap-4 items-center justify-between mb-2" >
                        <a class="font-semibold hover:underline" href="#">Buy Car Tyres</a>
                        <button class="md:hidden text-[0] w-4 h-4" @click="open = !open" :class="{'rotate-180': !open}">
                            <svg class="block" alt="Toggle menu" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 10L8 5L13 10" stroke="#808285" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <ul class="md:block" :class="{'block': open, 'hidden': !open}">
                        <li class="text-sm mb-2 hover:underline"><a href="#">Find tyres for your car</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Find tyres by size</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Wheel alignment</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Wheel balancing</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Mobile tyre fitting</a></li>
                    </ul>
                </li>
                <li class="border-b md:border-0 border-panel pb-2 mb-3" x-data="{open: false}">
                    <div class="flex gap-4 items-center justify-between mb-2">
                        <a class="font-semibold hover:underline" href="#">Book a Service</a>
                       <button class="md:hidden text-[0] w-4 h-4" @click="open = !open" :class="{'rotate-180': !open}">
                            <svg class="block" alt="Toggle menu" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 10L8 5L13 10" stroke="#808285" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <ul class="md:block" :class="{'block': open, 'hidden': !open}">
                        <li class="text-sm mb-2 hover:underline"><a href="#">Logbook servicing</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Basic servicing</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Roadworthy inspections</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Fleet servicing</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Car batteries</a></li>
                    </ul>
                </li>
                <li class="border-b md:border-0 border-panel pb-2 mb-3" x-data="{open: false}">
                    <div class="flex gap-4 items-center justify-between mb-2">
                        <a class="font-semibold hover:underline" href="#">Get Repairs</a>
                        <button class="md:hidden text-[0] w-4 h-4" @click="open = !open" :class="{'rotate-180': !open}">
                            <svg class="block" alt="Toggle menu" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 10L8 5L13 10" stroke="#808285" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <ul class="md:block" :class="{'block': open, 'hidden': !open}">
                        <li class="text-sm mb-2 hover:underline"><a href="#">Auto electrical</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Brake Repairs</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Clutch & transmission</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Exhaust systems</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Suspension & shocks</a></li>
                    </ul>
                </li>
                <li class="border-b md:border-0 border-panel pb-2 mb-3" x-data="{open: false}">
                    <div class="flex gap-4 items-center justify-between mb-2">
                        <a class="font-semibold hover:underline" href="#">mycar Tyre and Auto</a>
                        <button class="md:hidden text-[0] w-4 h-4" @click="open = !open" :class="{'rotate-180': !open}">
                            <svg class="block" alt="Toggle menu" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 10L8 5L13 10" stroke="#808285" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <ul class="md:block" :class="{'block': open, 'hidden': !open}">
                        <li class="text-sm mb-2 hover:underline"><a href="#">About us</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Find a mycar store</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Careers</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">News & car advice</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Terms & policies</a></li>
                        <li class="text-sm mb-2 hover:underline"><a href="#">Privacy policy</a></li>
                    </ul>
                </li>
            </ul>
        </nav>

    </div>
</section>
<section class="footer-legal bg-white">
    <div class="container flex flex-col md:flex-row gap-4 justify-between border-t border-panel py-[30px]">
        <p class="footer-copyright text-sm text-center md:text-left">&copy; Tyre and Auto Pty Ltd trading as mycar Tyre &amp; Auto. ABN **************. WA Lic No. MRB5465.</p>
        <ul class="footer-payment-methods flex justify-center md:justify-end gap-2">
            <li>
                <svg alt="Mastercard" width="35" height="24" viewBox="0 0 35 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.5" width="34" height="23" rx="3.5" fill="white" stroke="#E1E3E4"/>
                    <path d="M22.1758 5C25.9444 5 29 8.09091 29 11.9033C28.9998 15.7156 25.9443 18.8057 22.1758 18.8057C20.4863 18.8056 18.9416 18.1832 17.75 17.1543C16.5584 18.1832 15.0137 18.8056 13.3242 18.8057C9.55567 18.8057 6.50018 15.7156 6.5 11.9033C6.5 8.09091 9.55555 5 13.3242 5C15.0134 5.00011 16.5584 5.62186 17.75 6.65039C18.9416 5.62186 20.4866 5.00011 22.1758 5Z" fill="#ED0006"/>
                    <path d="M22.1758 5C25.9444 5 29 8.09091 29 11.9033C28.9998 15.7156 25.9443 18.8057 22.1758 18.8057C20.4863 18.8056 18.9416 18.1832 17.75 17.1543C19.2164 15.8882 20.1474 14.0064 20.1475 11.9033C20.1475 9.79982 19.2168 7.91652 17.75 6.65039C18.9416 5.62186 20.4866 5.00011 22.1758 5Z" fill="#F9A000"/>
                    <path d="M17.749 6.65088C19.2157 7.917 20.1465 9.79949 20.1465 11.9028C20.1465 14.0059 19.2154 15.8877 17.749 17.1538C16.2827 15.8877 15.3516 14.0059 15.3516 11.9028C15.3516 9.79951 16.2824 7.917 17.749 6.65088Z" fill="#FF5E00"/>
                </svg>
            </li>
            <li>
                <svg alt="Visa" width="35" height="24" viewBox="0 0 35 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.5" width="34" height="23" rx="3.5" fill="white" stroke="#E1E3E4"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.6253 16.2582H8.50494L6.91495 10.1924C6.83949 9.91334 6.67925 9.66666 6.44354 9.5504C5.85531 9.25823 5.20712 9.0257 4.5 8.90843V8.67489H7.91567C8.38708 8.67489 8.74064 9.0257 8.79957 9.43313L9.62454 13.8086L11.7438 8.67489H13.8052L10.6253 16.2582ZM14.9838 16.2582H12.9813L14.6302 8.67489H16.6327L14.9838 16.2582ZM19.2234 10.7757C19.2823 10.3673 19.6358 10.1337 20.0483 10.1337C20.6965 10.0751 21.4026 10.1924 21.9919 10.4835L22.3454 8.85081C21.7562 8.61727 21.108 8.5 20.5197 8.5C18.5762 8.5 17.162 9.55041 17.162 11.0082C17.162 12.1173 18.1637 12.6996 18.8708 13.0504C19.6358 13.4002 19.9305 13.6338 19.8716 13.9835C19.8716 14.5082 19.2823 14.7418 18.6941 14.7418C17.9869 14.7418 17.2798 14.5669 16.6327 14.2747L16.2791 15.9085C16.9862 16.1996 17.7512 16.3169 18.4584 16.3169C20.6376 16.3745 21.9919 15.3251 21.9919 13.75C21.9919 11.7665 19.2234 11.6502 19.2234 10.7757ZM29 16.2582L27.41 8.67489H25.7022C25.3486 8.67489 24.9951 8.90843 24.8772 9.25823L21.933 16.2582H23.9943L24.4058 15.1502H26.9386L27.1743 16.2582H29ZM25.9968 10.7171L26.585 13.5751H24.9361L25.9968 10.7171Z" fill="#172B85"/>
                </svg>
            </li>
            <li>
                <svg alt="American Express" xmlns="http://www.w3.org/2000/svg" width="35" height="24" viewBox="0 0 35 24" fill="none">
                    <rect x="0.5" y="0.5" width="34" height="23" rx="3.5" fill="#1F72CD" stroke="#E1E3E4"/>
                    <path d="M10.0547 9.35156V8.5H13.9668L14.8252 10.3701L15.6631 8.5H25.4258L26.416 9.60449L27.4775 8.5H32.0156L28.7305 12.1436L32.0156 15.7559L27.4062 15.7471L26.3848 14.6211L25.2939 15.7471H16.3193V14.8467L15.9346 15.7471H13.7031L13.3184 14.8652V15.7471H9.00293L8.51758 14.5918H7.40625L6.91992 15.7471H3L6.27441 8.5H9.62012L10.0547 9.35156ZM4.73633 14.7285H6.13379L6.62695 13.5635H9.29785L9.79199 14.7295H12.3994V10.6543L14.2568 14.7295H15.3906L17.2383 10.6543L17.249 14.7295H18.5166V9.5293H16.4336L14.8877 13.0527L13.21 9.5293H11.1162V14.4434L8.94434 9.5293H7.0332C7.01638 9.56738 4.73633 14.7285 4.73633 14.7285ZM19.6758 14.7256H24.749L26.3848 13.0039L27.9609 14.7256H29.6084L27.2139 12.1426L29.6084 9.5293H28.0322L26.4053 11.2314L24.8291 9.5293H19.6758V14.7256ZM25.5674 12.123L24.1016 13.6494H20.9482V12.6133H23.7598V11.5547H20.9482V10.6045H24.1641L25.5674 12.123ZM8.8418 12.4854H7.08301L7.96289 10.4092L8.8418 12.4854Z" fill="white"/>
                </svg>
            </li>
            <li>
                <svg alt="Zip Pay" width="35" height="24" viewBox="0 0 35 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.5" width="34" height="23" rx="3.5" fill="white" stroke="#E1E3E4"/>
                    <path d="M5.61729 14.5252L5.83766 16.4418H13.5529L13.3004 14.2468H9.70304L9.67161 13.9748L12.9847 11.5032L12.7634 9.58301H5.04883L5.30129 11.7777H8.90462L8.93605 12.0521L5.61729 14.5252Z" fill="black"/>
                    <path d="M13.6602 9.58301L14.4493 16.4418H22.1702L21.381 9.58301H13.6602Z" fill="#AA8FFF"/>
                    <path d="M30.2652 12.0522C30.0872 10.5107 28.953 9.57637 27.411 9.58304H22.2754L23.0642 16.4422H25.3748L25.2166 15.0703H27.6615C29.5856 15.0699 30.4661 13.7901 30.2652 12.0522ZM27.4117 13.1472L24.9962 13.1501L24.807 11.504L27.236 11.5061C27.8071 11.5135 28.0993 11.8569 28.1466 12.3267C28.1757 12.629 28.046 13.1469 27.4117 13.1469V13.1472Z" fill="black"/>
                    <path d="M15.9658 8.67188C16.3722 8.20619 16.3014 7.43066 15.8076 6.93968C15.3139 6.44871 14.5843 6.42822 14.1779 6.89391C13.7716 7.35961 13.8424 8.13514 14.3361 8.62611C14.8298 9.11708 15.5595 9.13758 15.9658 8.67188Z" fill="black"/>
                </svg>
            </li>
            <li>
                <svg alt="Afterpay" width="35" height="24" viewBox="0 0 35 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.5" width="34" height="23" rx="3.5" fill="#AFF4DE" stroke="#E1E3E4"/>
                    <path d="M25.3498 7.64505L20.1216 4.75026C18.5868 3.90054 16.6686 4.96238 16.6686 6.66307V6.96003C16.6686 7.2283 16.818 7.4766 17.0599 7.61011L18.0471 8.15663C18.337 8.31759 18.6986 8.1167 18.6986 7.79603V7.05736C18.6986 6.68802 19.115 6.45719 19.4479 6.64186L23.9763 9.14984C24.3096 9.33451 24.3096 9.79618 23.9763 9.97962L19.4479 12.4876C19.115 12.6723 18.6986 12.4414 18.6986 12.0721V11.679C18.6986 9.97834 16.7804 8.91527 15.2446 9.76621L10.0164 12.661C8.48153 13.5108 8.48153 15.6369 10.0164 16.4866L15.2446 19.3814C16.7789 20.2311 18.6986 19.1693 18.6986 17.4686V17.1717C18.6986 16.9034 18.5492 16.6563 18.3073 16.5216L17.32 15.9738C17.0302 15.8129 16.6686 16.0137 16.6686 16.3344V17.0731C16.6686 17.4424 16.2526 17.6732 15.9193 17.4886L11.3909 14.9806C11.058 14.7959 11.058 14.3343 11.3909 14.1496L15.9193 11.6416C16.2526 11.4569 16.6686 11.6878 16.6686 12.0571V12.4502C16.6686 14.1508 18.5868 15.2139 20.1216 14.363L25.3498 11.4682C26.8847 10.6209 26.8847 8.49477 25.3498 7.64505Z" fill="black"/>
                </svg>
            </li>
        </ul>
    </div>
</section>


