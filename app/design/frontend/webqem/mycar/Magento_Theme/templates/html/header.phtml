<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Checkout\Block\Cart\Sidebar as SidebarCart;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(SidebarCart::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);
?>
<script>
    function initHeader () {
        return {
            searchOpen: false,
            cart: {},
            isCartOpen: false,
            getData(data) {
                if (data.cart) { this.cart = data.cart }
            },
            isCartEmpty() {
                return !this.cart.summary_count
            },
            toggleCart(event) {
                if (event.detail && event.detail.isOpen !== undefined) {
                    this.isCartOpen = event.detail.isOpen
                    if (!this.isCartOpen && this.$refs && this.$refs.cartButton) {
                        this.$refs.cartButton.focus()
                    }
                } else {
                    <?php
                    /*
                     * The toggle-cart event was previously dispatched without parameter to open the drawer (not toggle).
                     * Keeping this in here for backwards compatibility.
                     */
                    ?>
                    this.isCartOpen = true
                }
            }
        }
    }
    function initCompareHeader() {
        return {
            compareProducts: null,
            itemCount: 0,
            receiveCompareData(data) {
                if (data['compare-products']) {
                    this.compareProducts = data['compare-products'];
                    this.itemCount = this.compareProducts.count;
                }
            }
        }
    }
</script>
<div id="header"
     class="relative z-30 w-full bg-white border-b border-panel"
     x-data="initHeader()"
     @private-content-loaded.window="getData(event.detail.data)"
>
    <div class="container flex flex-wrap gap-2 items-center justify-between w-full min-h-header-mobile md:min-h-header-desktop">

        <!--Main Navigation-->
        <?= $block->getChildHtml('topmenu') ?>

        <!--Logo-->
        <?= $block->getChildHtml('logo'); ?>

        <!--Icons-->
        <div class="flex items-center gap-3 md:gap-8 order-4">
            <!--Compare Icon-->
            <a id="compare-link"
               class="relative inline-block hidden"
               :class="{ 'hidden': !(itemCount > 0) }"
               href="<?= $escaper->escapeUrl($block->getUrl('catalog/product_compare/index')) ?>"
               title="<?= $escaper->escapeHtmlAttr(__('Compare Products')) ?>"
               x-data="initCompareHeader()"
               @private-content-loaded.window="receiveCompareData($event.detail.data)"
               :aria-label="`
                    <?= $escaper->escapeHtmlAttr(__('Compare Products')) ?>,
                    ${itemCount > 1
                        ? hyva.str('<?= $escaper->escapeJs(__('%1 Items')) ?>', itemCount)
                        : hyva.str('<?= $escaper->escapeJs(__('%1 Item')) ?>', itemCount)
                    }`"
            >
                <?= $heroicons->scaleHtml("md:h-6 md:w-6", 28, 28, ["aria-hidden" => "true"]) ?>
                <span
                    x-text="itemCount"
                    class="absolute -top-1.5 -right-1.5 h-5 px-2 py-1 rounded-full bg-yellow-500 text-white
                        text-xs font-semibold leading-none text-center uppercase tabular-nums"
                    aria-hidden="true"
                ></span>
            </a>

            <!--Search Icon-->
            <button
                id="menu-search-icon"
                class="relative inline-block p-1 hidden md:inline-block md:-mt-4"
                @click.prevent="
                    searchOpen = !searchOpen;
                    $dispatch('search-open');
                "
                aria-label="<?= $escaper->escapeHtmlAttr(__('Toggle search form')) ?>"
                aria-haspopup="true"
                :aria-expanded="searchOpen"
                x-ref="searchButton"
            >
                <svg width="24" height="24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path d="M9.0625 15.625C12.6869 15.625 15.625 12.6869 15.625 9.0625C15.625 5.43813 12.6869 2.5 9.0625 2.5C5.43813 2.5 2.5 5.43813 2.5 9.0625C2.5 12.6869 5.43813 15.625 9.0625 15.625Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M13.7031 13.7031L17.5 17.5" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="absolute top-full left-[50%] translate-x-[-50%] text-xs hidden md:block">Search</span>
            </button>

            <!-- Chat Icon -->
            <button class="relative inline-block p-1 md:-mt-4">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.2565 16.5937C3.13983 14.7097 2.74924 12.4829 3.15804 10.3313C3.56684 8.17977 4.74693 6.2514 6.47677 4.90826C8.2066 3.56511 10.3672 2.89955 12.553 3.03654C14.7388 3.17352 16.7994 4.10362 18.348 5.65222C19.8966 7.20083 20.8267 9.26144 20.9637 11.4472C21.1007 13.633 20.4351 15.7936 19.0919 17.5234C17.7488 19.2533 15.8204 20.4334 13.6689 20.8422C11.5173 21.251 9.29049 20.8604 7.4065 19.7437V19.7437L4.294 20.6249C4.16648 20.6622 4.03128 20.6646 3.90256 20.6316C3.77384 20.5987 3.65635 20.5318 3.5624 20.4378C3.46845 20.3439 3.4015 20.2264 3.36858 20.0976C3.33565 19.9689 3.33796 19.8337 3.37525 19.7062L4.2565 16.5937Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="absolute top-full left-[50%] translate-x-[-50%] text-xs hidden md:block whitespace-nowrap">Need Help?</span>
            </button>

            <!-- Location Icon -->
            <button class="relative inline-block p-1 md:-mt-4">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 12.75C13.6569 12.75 15 11.4069 15 9.75C15 8.09315 13.6569 6.75 12 6.75C10.3431 6.75 9 8.09315 9 9.75C9 11.4069 10.3431 12.75 12 12.75Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M19.5 9.75C19.5 16.5 12 21.75 12 21.75C12 21.75 4.5 16.5 4.5 9.75C4.5 7.76088 5.29018 5.85322 6.6967 4.4467C8.10322 3.04018 10.0109 2.25 12 2.25C13.9891 2.25 15.8968 3.04018 17.3033 4.4467C18.7098 5.85322 19.5 7.76088 19.5 9.75V9.75Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="absolute top-full left-[50%] translate-x-[-50%] text-xs hidden md:block">Stores</span>
            </button>

            <!-- Additional Header Elements -->
            <?= $block->getChildHtml('additional') ?>

            <!--Customer Icon & Dropdown-->
            <?= $block->getChildHtml('customer') ?>

            <!--Cart Icon-->
            <?php if ($showMiniCart): ?>
                <button
            <?php else: ?>
                <a
            <?php endif ?>
                id="menu-cart-icon"
                class="relative inline-block rounded min-h-[36px] md:min-h-[48px] px-4 md:px-6 py-1 bg-accent text-white rounded-lg"
                x-ref="cartButton"
                :aria-disabled="isCartEmpty()"
                :aria-label="`
                    <?= $escaper->escapeHtmlAttr($showMiniCart ? __('Toggle minicart') : __('View cart')) ?>,
                    ${isCartEmpty()
                        ? '<?= $escaper->escapeHtmlAttr(__('Cart is empty')) ?>'
                        : cart.summary_count > 1
                            ? hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 items')) ?>', cart.summary_count)
                            : hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 item')) ?>', cart.summary_count)
                    }`"
                <?php if ($showMiniCart): ?>
                    @click.prevent="() => {
                        $dispatch('toggle-cart', { isOpen: true })
                    }"
                    @toggle-cart.window="toggleCart($event)"
                    :aria-expanded="isCartOpen"
                    aria-haspopup="dialog"
                <?php else: ?>
                    href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                    title="<?= $escaper->escapeHtmlAttr(__('View cart')) ?>"
                <?php endif ?>
            >
                <span class="block md:hidden">Book</span>
                <span class="hidden md:block">Book now</span>
                <span
                    x-text="cart.summary_count"
                    x-show="!isCartEmpty()"
                    x-cloak
                    class="absolute -top-2 -right-2 min-w-6 h-6 rounded-full bg-white text-accent flex justify-center items-center
                        border border-accent text-xs font-semibold leading-none text-center uppercase tabular-nums"
                    aria-hidden="true"
                ></span>
            <?php if ($showMiniCart): ?>
                </button>
            <?php else: ?>
                </a>
            <?php endif ?>
        </div>
    </div>
    <!--Search-->
    <div 
        class="w-full bg-white border-t border-panel min-h-header-mobile flex items-center" 
        :class="{ 'md:flex': searchOpen, 'md:hidden': !searchOpen }"
        id="search-content"
    >
        <?= $block->getChildHtml('header-search'); ?>
    </div>

    <!--Cart Drawer-->
    <?= $block->getChildHtml('cart-drawer'); ?>

    <!--Authentication Pop-Up-->
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>

