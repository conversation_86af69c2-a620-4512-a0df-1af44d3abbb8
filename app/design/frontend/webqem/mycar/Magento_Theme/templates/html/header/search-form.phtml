<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Search\Helper\Data as SearchHelper;
use Magento\Search\ViewModel\ConfigProvider as SearchConfig;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Template $block */
/** @var SearchHelper $helper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var SearchHelper $helper */
$helper = $this->helper(SearchHelper::class);

/** @var SearchConfig $searchConfig */
$searchConfig = $viewModels->require(SearchConfig::class);
?>
<script>
    'use strict';

    function initMiniSearch() {
        return {
            minSearchLength: <?= (int) $helper->getMinQueryLength() ?>,
            <?php if ($searchConfig->isSuggestionsAllowed()): ?>
            suggestions: [],
            suggest() {
                const search = this.$refs.searchInput;
                if (search.value.length >= this.minSearchLength) {
                    search.setCustomValidity('');
                    search.reportValidity();
                    this.fetchSuggestions(search.value);
                } else {
                    this.suggestions = [];
                }
            },
            fetchSuggestions(term) {
                fetch(
                    window.BASE_URL + 'search/ajax/suggest?' + new URLSearchParams({q: term}),
                    {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }
                )
                .then(response => response.json())
                .then(result => this.suggestions = result);
            },
            <?php endif ?>
            search(term) {
                const search = this.$refs.searchInput;
                term = term || search.value;
                if (term.length < this.minSearchLength) {
                    search.setCustomValidity('<?= $escaper->escapeJs(
                        __('Minimum Search query length is %1', $helper->getMinQueryLength())
                    ) ?>');
                    search.reportValidity();
                } else {
                    search.setCustomValidity('');
                    search.value = term;
                    this.$refs.form.submit();
                }
            },
            focusElement(element) {
                if (element && element.nodeName === "DIV") {
                    element.focus();
                    return true;
                } else {
                    return false;
                }
            }
        }
    }
</script>
<div class="container mx-auto text-black" x-data="initMiniSearch()">
    <search title="<?= $escaper->escapeHtmlAttr(__('Store')) ?>" role="search">
        <form
            id="search_mini_form"
            class="form minisearch flex"
            action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>"
            method="get"
            @submit.prevent="search()"
            x-ref="form"
        >
            <label class="sr-only" for="search">
                <?= $escaper->escapeHtml(__('Search')) ?>
            </label>
            <input
                id="search"
                x-ref="searchInput"
                type="search"
                autocomplete="off"
                name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                value="<?= /** @noEscape */ $helper->getEscapedQueryText() ?>"
                placeholder="<?= $escaper->escapeHtmlAttr(__('Search for a store, service or help...')) ?>"
                maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                class="w-full order-2 border-0 text-sm text-field px-2 py-2"
                @search-open.window.debounce.10="$el.focus(); $el.select()"
                <?php if ($searchConfig->isSuggestionsAllowed()): ?>
                    @focus.once="suggest"
                    @input.debounce.300="suggest"
                    @keydown.arrow-down.prevent="focusElement($root.querySelector('[tabindex]'))"
                <?php endif ?>
            >
            <button
                type="submit"
                class="action search order-1 px-1 py-2"
                title="<?= $escaper->escapeHtmlAttr(__('Search')) ?>"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Search')) ?>"
            >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path d="M9.0625 15.625C12.6869 15.625 15.625 12.6869 15.625 9.0625C15.625 5.43813 12.6869 2.5 9.0625 2.5C5.43813 2.5 2.5 5.43813 2.5 9.0625C2.5 12.6869 5.43813 15.625 9.0625 15.625Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M13.7031 13.7031L17.5 17.5" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </form>
    </search>
    <?php if ($searchConfig->isSuggestionsAllowed()): ?>
        <template x-if="suggestions.length > 0">
            <nav
                aria-label="<?= $escaper->escapeHtmlAttr(__('Search Suggestions')) ?>"
                class="flex flex-col mt-1 text-grey-800 gap-1"
            >
                <template x-for="suggestion in suggestions">
                    <button
                        type="button"
                        class="flex justify-between p-2 bg-container-lighter even:bg-container border border-container hover:bg-container-darker"
                        @click="search(suggestion.title)"
                        @keydown.enter="search(suggestion.title)"
                        @keydown.arrow-up.prevent="focusElement($event.target.previousElementSibling) || $refs.searchInput.focus()"
                        @keydown.arrow-down.prevent="focusElement($event.target.nextElementSibling)"
                    >
                        <span x-text="suggestion.title"></span>
                        <span x-text="suggestion.num_results"></span>
                    </button>
                </template>
            </nav>
        </template>
    <?php endif ?>
</div>
