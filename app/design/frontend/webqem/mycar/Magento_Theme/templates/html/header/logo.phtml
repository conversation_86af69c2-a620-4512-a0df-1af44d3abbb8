<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Logo\LogoSizeResolver;
use Hyva\Theme\ViewModel\Logo\LogoPathResolver;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Header\Logo;

/** @var Logo $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$storeName = $block->getThemeName() ?: $storeConfig->getStoreConfig('general/store_information/name') ?: __('Store logo');

/** @var LogoSizeResolver $logoSizeResolver */
$logoSizeResolver = $viewModels->require(LogoSizeResolver::class);
$logoWidth = $logoSizeResolver && $logoSizeResolver->getWidth()
    ? $logoSizeResolver->getWidth()
    : $block->getLogoWidth();
$logoHeight = $logoSizeResolver && $logoSizeResolver->getHeight()
    ? $logoSizeResolver->getHeight()
    : $block->getLogoHeight();

/** @var LogoPathResolver $logoPathResolver */
$logoPathResolver = $block->getData('logoPathResolver');
$logoSrc = $logoPathResolver && method_exists($logoPathResolver, 'getLogoSrc')
    ? $logoPathResolver->getLogoSrc($block->getData('logo_file'))
    : $block->getLogoSrc();
?>
<div class="logo order-3 md:order-1 flex-1 md:flex-initial">
    <a
        class="flex items-center justify-start text-xl font-medium tracking-wide text-gray-800
            no-underline hover:no-underline font-title"
        href="<?= $escaper->escapeUrl($block->getUrl('')) ?>"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Go to Home page')) ?>"
    >
        <img
            src="<?= $escaper->escapeUrl($logoSrc) ?>"
            alt="<?= $escaper->escapeHtmlAttr($block->getLogoAlt() ?: $storeName) ?>"
            class="w-[78px] md:w-[96px]"
            <?= $logoWidth
                ? 'width="' . $escaper->escapeHtmlAttr($logoWidth) . '"'
                : 'width="200"'
            ?>
            <?= $logoHeight
                ? 'height="' . $escaper->escapeHtmlAttr($logoHeight) . '"'
                : 'height="150"'
            ?>
            loading="eager"
        />
        <?php if (!$logoSrc): ?>
            <?= $escaper->escapeHtml($storeName) ?>
        <?php endif; ?>
    </a>
</div>
