<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

?>
<div x-data="initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?>()"
     class="z-20 order-2 navigation hidden lg:flex md:flex-1"
>
    <!-- desktop -->
    <div x-ref="nav-desktop"
         @load.window="setActiveMenu($root)"
         class="hidden lg:block lg:px-4">
        <nav
            class="relative"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Main menu')) ?>"
        >
            <ul class="flex justify-start flex-wrap gap-6">
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li class="relative level-0"
                        @mouseenter="hoverPanelActiveId = '<?= /* @noEscape */ (string) $index ?>'"
                        @mouseleave="hoverPanelActiveId = 0"
                        @keyup.escape="hoverPanelActiveId = 0"
                    >
                        <span class="flex items-center gap-1">
                            <a class="w-full text-base font-medium level-0"
                               href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                               title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                               @focus="hoverPanelActiveId = 0"
                            >
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    type="button"
                                    data-sr-button-id="<?= $escaper->escapeHtmlAttr($index) ?>"
                                    :aria-expanded="hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>' ? 'true' : 'false'"
                                    @click="openMenuOnClick('<?= /* @noEscape */ (string) $index ?>')"
                                    class=""
                                >
                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-3 h-3">
                                        <path d="M9.75 4.5L6 8.25L2.25 4.5" stroke="#808285" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <span class="sr-only">
                                        <?= $escaper->escapeHtml(__('Show submenu for %1 category', $menuItem['name'])) ?>
                                    </span>
                                </button>
                            <?php endif; ?>
                        </span>
                        <?php if (!empty($menuItem['childData'])): ?>
                            <ul
                                class="absolute top-full z-10 hidden bg-white"
                                :class="{
                                    'hidden' : hoverPanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                    'block' : hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>'
                                }"
                            >
                                <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                    <li>
                                        <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                           title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                           class="block w-full aria-[current=page]:underline"
                                           @keyup.escape="$nextTick(() => document.querySelector('[data-sr-button-id=<?= $escaper->escapeJs($index) ?>]').focus())"
                                        >
                                            <span class="text-base">
                                                <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                            </span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </nav>
    </div>
</div>
<script>
    'use strict';

    const initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            hoverPanelActiveId: null,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.setAttribute('aria-current', 'page');
                    item.closest('li.level-0').setAttribute('data-active', 'true');
                });
            },
            openMenuOnClick(menuNode) {
                if (menuNode === this.hoverPanelActiveId) {
                    this.hoverPanelActiveId = 0;
                } else {
                    this.hoverPanelActiveId = menuNode
                }
            }
        }
    }
</script>
