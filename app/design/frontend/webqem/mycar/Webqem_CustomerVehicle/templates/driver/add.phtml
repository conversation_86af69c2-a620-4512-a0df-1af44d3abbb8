<?php
/** @var \Webqem\CustomerVehicle\Block\Driver\Add $block */
?>
<form action="<?= $block->getFormAction() ?>"
      method="post"
      id="form-add-driver-validate"
      enctype="multipart/form-data"
      autocomplete="off"
      x-data="{
          formData: {
              firstname: '',
              lastname: '',
              mobileNumber: '',
              email: '',
              relationshipToOwner: ''
          },
          errors: {},
          validateForm() {
              this.errors = {};

              if (!this.formData.firstname) {
                  this.errors.firstname = '<?= $block->escapeJs(__('This is a required field.')) ?>';
              }

              if (!this.formData.lastname) {
                  this.errors.lastname = '<?= $block->escapeJs(__('This is a required field.')) ?>';
              }

              if (!this.formData.mobileNumber) {
                  this.errors.mobileNumber = '<?= $block->escapeJs(__('This is a required field.')) ?>';
              } else if (!/^(04[0-9]{8})$/.test(this.formData.mobileNumber)) {
                  this.errors.mobileNumber = '<?= $block->escapeJs(__('Please enter a valid Australian mobile number.')) ?>';
              }

              if (this.formData.email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(this.formData.email)) {
                  this.errors.email = '<?= $block->escapeJs(__('Please enter a valid email address.')) ?>';
              }

              return Object.keys(this.errors).length === 0;
          },
          submitForm() {
              if (this.validateForm()) {
                  this.$el.submit();
              }
          }
      }"
      @submit.prevent="submitForm">
    <?= $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="vehicleId" value="<?= $block->escapeHtmlAttr($block->getVehicleId()) ?>">

    <fieldset class="mt-4 mb-6">
        <div class="mb-4">
            <label for="firstname" class="block text-sm font-medium mb-1">
                <?= $block->escapeHtml(__('First Name')) ?> <span class="text-red-600">*</span>
            </label>
            <input type="text"
                   id="firstname"
                   name="firstname"
                   x-model="formData.firstname"
                   class="w-full px-3 py-2 border rounded-md"
                   :class="{'border-red-500': errors.firstname}"
            />
            <div x-show="errors.firstname" x-text="errors.firstname" class="text-red-600 text-sm mt-1"></div>
        </div>

        <div class="mb-4">
            <label for="lastname" class="block text-sm font-medium mb-1">
                <?= $block->escapeHtml(__('Last Name')) ?> <span class="text-red-600">*</span>
            </label>
            <input type="text"
                   id="lastname"
                   name="lastname"
                   x-model="formData.lastname"
                   class="w-full px-3 py-2 border rounded-md"
                   :class="{'border-red-500': errors.lastname}"
            />
            <div x-show="errors.lastname" x-text="errors.lastname" class="text-red-600 text-sm mt-1"></div>
        </div>

        <div class="mb-4">
            <label for="mobileNumber" class="block text-sm font-medium mb-1">
                <?= $block->escapeHtml(__('Mobile')) ?> <span class="text-red-600">*</span>
            </label>
            <input type="tel"
                   id="mobileNumber"
                   name="mobileNumber"
                   x-model="formData.mobileNumber"
                   class="w-full px-3 py-2 border rounded-md"
                   :class="{'border-red-500': errors.mobileNumber}"
            />
            <div x-show="errors.mobileNumber" x-text="errors.mobileNumber" class="text-red-600 text-sm mt-1"></div>
        </div>

        <div class="mb-4">
            <label for="email" class="block text-sm font-medium mb-1">
                <?= $block->escapeHtml(__('Email Address')) ?>
            </label>
            <input type="email"
                   id="email"
                   name="email"
                   x-model="formData.email"
                   class="w-full px-3 py-2 border rounded-md"
                   :class="{'border-red-500': errors.email}"
            />
            <div x-show="errors.email" x-text="errors.email" class="text-red-600 text-sm mt-1"></div>
        </div>

        <div class="mb-4">
            <label for="relationshipToOwner" class="block text-sm font-medium mb-1">
                <?= $block->escapeHtml(__('Relationship To Owner')) ?>
            </label>
            <input type="text"
                   id="relationshipToOwner"
                   name="relationshipToOwner"
                   x-model="formData.relationshipToOwner"
                   class="w-full px-3 py-2 border rounded-md"
            />
        </div>

        <div class="mt-6">
            <button type="submit"
                    class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                <?= $block->escapeHtml(__('Add driver')) ?>
            </button>
        </div>
    </fieldset>
</form>
