<?php
/** @var \Webqem\CustomerVehicle\Block\Garage\Add $block */
?>
<div class="add-vehicle-form"
     x-data="vehicleLookup"
     x-init="init({
         lookupUrl: '<?= $block->escapeJs($block->getUrl('*/*/lookup')) ?>',
         getModelsUrl: '<?= $block->escapeJs($block->getUrl('*/*/modellookup')) ?>',
         getYearsUrl: '<?= $block->escapeJs($block->getUrl('*/*/yearlookup')) ?>',
         getSeriesUrl: '<?= $block->escapeJs($block->getUrl('*/*/serieslookup')) ?>',
         formKey: '<?= $block->escapeJs($block->getBlockHtml('formkey')) ?>'
     })">

    <div class="search-methods">
        <div class="method-toggle">
            <button type="button" class="action toggle"
                    :class="{'active': searchMethod === 'rego'}"
                    @click="setSearchMethod('rego')">
                <?= $block->escapeHtml(__('Find by Rego')) ?>
            </button>
            <button type="button" class="action toggle"
                    :class="{'active': searchMethod === 'manual'}"
                    @click="setSearchMethod('manual')">
                <?= $block->escapeHtml(__('Find by Make, Model and Year')) ?>
            </button>
        </div>
    </div>

    <form id="add-vehicle-form" @submit.prevent="submitForm" action="<?= $block->escapeUrl($block->getFormAction()) ?>" method="post">
        <input type="hidden" name="form_key" x-model="formKeyValue" />
        <!-- Hidden form key for AJAX requests -->
        <template x-if="false"><div x-html="formKeyElement"></div></template>

        <!-- SCENARIO 1: Registration lookup section -->
        <fieldset id="rego-search" class="fieldset" x-show.important="searchMethod === 'rego'">
            <div class="field rego required">
                <label class="label" for="rego_number"><span><?= $block->escapeHtml(__('Car Rego')) ?></span></label>
                <div class="control">
                    <input type="text" name="rego_number" id="rego_number" x-model="regoNumber"
                           title="<?= $block->escapeHtmlAttr(__('Car Rego')) ?>"
                           class="input-text" :required="searchMethod === 'rego'"/>
                </div>
            </div>

            <div class="field state required">
                <label class="label" for="state"><span><?= $block->escapeHtml(__('State')) ?></span></label>
                <div class="control">
                    <select name="state" id="state" x-model="state"
                            title="<?= $block->escapeHtmlAttr(__('State')) ?>"
                            class="select" :required="searchMethod === 'rego'">
                        <option value=""><?= $block->escapeHtml(__('Please select a state')) ?></option>
                        <?php foreach ($block->getStates() as $state): ?>
                            <option value="<?= $block->escapeHtmlAttr($state['value']) ?>">
                                <?= $block->escapeHtml($state['label']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="actions-toolbar">
                <div class="primary">
                    <button type="button" @click="findVehicleByRego"
                            :disabled="loading || !regoNumber || !state"
                            class="action find primary">
                        <span x-text="loading ? '<?= $block->escapeHtml(__('Searching...')) ?>' : '<?= $block->escapeHtml(__('Find My Car')) ?>'"></span>
                    </button>
                </div>
            </div>
        </fieldset>

        <!-- SCENARIO 2: Manual selection section -->
        <fieldset id="manual-search" class="fieldset" x-show.important="searchMethod === 'manual'">
            <legend class="legend"><span><?= $block->escapeHtml(__('Vehicle Make, Model and Year')) ?></span></legend>

            <div class="field make required">
                <label class="label" for="make_select"><span><?= $block->escapeHtml(__('Make')) ?></span></label>
                <div class="control">
                    <select name="make_select" id="make_select" x-model="make" @change="onMakeChange"
                            title="<?= $block->escapeHtmlAttr(__('Make')) ?>"
                            class="select" :required="searchMethod === 'manual'">
                        <option value=""><?= $block->escapeHtml(__('Please select a make')) ?></option>
                        <?php foreach ($block->getVehicleMakes() as $make): ?>
                            <option value="<?= $block->escapeHtmlAttr($make['Id']) ?>">
                                <?= $block->escapeHtml($make['Name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="field model required">
                <label class="label" for="model_select"><span><?= $block->escapeHtml(__('Model')) ?></span></label>
                <div class="control">
                    <select name="model_select" id="model_select" x-model="model" @change="onModelChange"
                            title="<?= $block->escapeHtmlAttr(__('Model')) ?>"
                            class="select" :required="searchMethod === 'manual'" :disabled="!modelOptions.length">
                        <option value=""><?= $block->escapeHtml(__('Please select a model')) ?></option>
                        <template x-for="option in modelOptions" :key="option.Id">
                            <option :value="option.Id" x-text="option.Name"></option>
                        </template>
                    </select>
                </div>
            </div>

            <div class="field year required">
                <label class="label" for="year_select"><span><?= $block->escapeHtml(__('Year')) ?></span></label>
                <div class="control">
                    <select name="year_select" id="year_select" x-model="year" @change="onYearChange"
                            title="<?= $block->escapeHtmlAttr(__('Year')) ?>"
                            class="select" :required="searchMethod === 'manual'" :disabled="!yearOptions.length">
                        <option value=""><?= $block->escapeHtml(__('Please select a year')) ?></option>
                        <template x-for="option in yearOptions" :key="option.value">
                            <option :value="option.value" x-text="option.value"></option>
                        </template>
                    </select>
                </div>
            </div>

            <div class="field series">
                <label class="label" for="series_select"><span><?= $block->escapeHtml(__('Series')) ?></span></label>
                <div class="control">
                    <select name="series_select" id="series_select" x-model="series"
                            title="<?= $block->escapeHtmlAttr(__('Series')) ?>"
                            class="select" :disabled="!seriesOptions.length">
                        <option value=""><?= $block->escapeHtml(__('Please select a series (optional)')) ?></option>
                        <template x-for="option in seriesOptions" :key="option.Id">
                            <option :value="option.Id" x-text="option.Name"></option>
                        </template>
                    </select>
                </div>
            </div>

            <div class="actions-toolbar">
                <div class="primary">
                    <button type="button" @click="findVehicleByManual"
                            :disabled="loading || !make || !model || !year"
                            class="action find primary">
                        <span x-text="loading ? '<?= $block->escapeHtml(__('Searching...')) ?>' : '<?= $block->escapeHtml(__('Find My Car')) ?>'"></span>
                    </button>
                </div>
            </div>
        </fieldset>

        <!-- Vehicle Details Display Section - Shown after vehicle lookup -->
        <div id="vehicle-details" x-show="vehicleFound" class="vehicle-details-container">
            <h3><?= $block->escapeHtml(__('Vehicle Details')) ?></h3>
            <div class="vehicle-details-content">
                <div class="field vehicle_name">
                    <label class="label" for="vehicle_name"><span><?= $block->escapeHtml(__('Vehicle Name')) ?></span></label>
                    <div class="control">
                        <input type="text" name="vehicle_name" id="vehicle_name" x-model="vehicleDetails.name" readonly class="input-text" />
                    </div>
                </div>

                <div class="field make">
                    <label class="label" for="make"><span><?= $block->escapeHtml(__('Make')) ?></span></label>
                    <div class="control">
                        <input type="text" name="make" id="make" x-model="vehicleDetails.make" readonly class="input-text" />
                    </div>
                </div>

                <div class="field model">
                    <label class="label" for="model"><span><?= $block->escapeHtml(__('Model')) ?></span></label>
                    <div class="control">
                        <input type="text" name="model" id="model" x-model="vehicleDetails.model" readonly class="input-text" />
                    </div>
                </div>

                <div class="field year">
                    <label class="label" for="year"><span><?= $block->escapeHtml(__('Year')) ?></span></label>
                    <div class="control">
                        <input type="text" name="year" id="year" x-model="vehicleDetails.year" readonly class="input-text" />
                    </div>
                </div>

                <div class="field series">
                    <label class="label" for="series"><span><?= $block->escapeHtml(__('Series')) ?></span></label>
                    <div class="control">
                        <input type="text" name="series" id="series" x-model="vehicleDetails.series" readonly class="input-text" />
                    </div>
                </div>

                <div class="field poll_id">
                    <label class="label" for="poll_id"><span><?= $block->escapeHtml(__('Poll ID')) ?></span></label>
                    <div class="control">
                        <input type="text" name="poll_id" id="poll_id" x-model="vehicleDetails.pollId" readonly class="input-text" />
                    </div>
                </div>

                <input type="hidden" name="vehicle_logic_id" id="vehicle_logic_id" x-model="vehicleDetails.vehicleLogicId" />
                <input type="hidden" name="search_method" id="search_method" x-model="searchMethod" />
            </div>
        </div>

        <div class="message error" id="lookup-error" x-show="errorMessage" x-text="errorMessage"></div>

        <div class="actions-toolbar final-actions">
            <div class="primary">
                <button type="button" @click="submitForm" id="add-car-button" class="action submit primary" :disabled="!vehicleFound">
                    <span><?= $block->escapeHtml(__('Add My Car to Garage')) ?></span>
                </button>
            </div>
            <div class="secondary">
                <a class="action back" href="<?= $block->escapeUrl($block->getUrl('*/*/index')) ?>">
                    <span><?= $block->escapeHtml(__('Back to My Garage')) ?></span>
                </a>
            </div>
        </div>
    </form>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('vehicleLookup', () => ({
            searchMethod: 'rego',
            regoNumber: '',
            state: '',
            make: '',
            model: '',
            year: '',
            series: '',
            loading: false,
            errorMessage: '',
            vehicleFound: false,
            formKeyElement: '',
            formKeyValue: '',
            config: {},
            vehicleDetails: {
                name: '',
                make: '',
                model: '',
                year: '',
                series: '',
                pollId: '',
                vehicleLogicId: ''
            },
            modelOptions: [],
            yearOptions: [],
            seriesOptions: [],

            init(config) {
                this.config = config;
                this.formKeyElement = config.formKey;

                // Extract form key from the template
                const parser = new DOMParser();
                const formKeyDoc = parser.parseFromString(this.formKeyElement, 'text/html');
                const formKeyInput = formKeyDoc.querySelector('input[name="form_key"]');
                this.formKeyValue = formKeyInput ? formKeyInput.value : '';
            },

            submitForm() {
                // Check if vehicle details are found
                if (!this.vehicleFound) {
                    this.errorMessage = '<?= $block->escapeJs(__('Please find a vehicle first')) ?>';
                    return;
                }

                // Submit the form
                document.getElementById('add-vehicle-form').submit();
            },

            setSearchMethod(method) {
                this.searchMethod = method;
                this.resetFormState();
            },

            resetFormState() {
                this.regoNumber = '';
                this.state = '';
                this.make = '';
                this.model = '';
                this.year = '';
                this.series = '';
                this.modelOptions = [];
                this.yearOptions = [];
                this.seriesOptions = [];
                this.vehicleFound = false;
                this.errorMessage = '';
                this.vehicleDetails = {
                    name: '',
                    make: '',
                    model: '',
                    year: '',
                    series: '',
                    pollId: '',
                    vehicleLogicId: ''
                };
            },

            findVehicleByRego() {
                if (!this.regoNumber || !this.state) {
                    this.errorMessage = !this.regoNumber
                        ? '<?= $block->escapeJs(__('Please enter a registration number')) ?>'
                        : '<?= $block->escapeJs(__('Please select a state')) ?>';
                    return;
                }

                this.loading = true;
                this.errorMessage = '';

                // Create the form data object with the correct parameters
                const formData = new URLSearchParams();
                formData.append('rego_number', this.regoNumber);
                formData.append('state', this.state);
                formData.append('search_method', 'rego');
                formData.append('form_key', this.formKeyValue);

                fetch(this.config.lookupUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            this.populateVehicleDetailsByRego(data.vehicle);
                        } else {
                            this.errorMessage = data.message || '<?= $block->escapeJs(__('Vehicle not found')) ?>';
                        }
                    })
                    .catch(error => {
                        this.errorMessage = '<?= $block->escapeJs(__('An error occurred while looking up your vehicle. Please try again.')) ?>';
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },

            findVehicleByManual() {
                if (!this.make || !this.model || !this.year) {
                    this.errorMessage = '<?= $block->escapeJs(__('Please select make, model, and year')) ?>';
                    return;
                }

                // Get text values from select options
                const makeSelect = document.getElementById('make_select');
                const modelSelect = document.getElementById('model_select');
                const seriesSelect = document.getElementById('series_select');

                const makeText = makeSelect.options[makeSelect.selectedIndex].textContent.trim();
                const modelText = modelSelect.options[modelSelect.selectedIndex].textContent.trim();
                const seriesText = this.series && seriesSelect.selectedIndex >= 0 ?
                    seriesSelect.options[seriesSelect.selectedIndex].textContent.trim() : '';

                const vehicleData = {
                    make: makeText,
                    model: modelText,
                    year: this.year,
                    series: seriesText,
                    vehicle_logic_id: `${this.make}_${this.model}_${this.year}${this.series ? '_' + this.series : ''}`
                };

                this.populateVehicleDetailsByManual(vehicleData);
            },

            onMakeChange() {
                if (!this.make) {
                    this.modelOptions = [];
                    this.yearOptions = [];
                    this.seriesOptions = [];
                    this.model = '';
                    this.year = '';
                    this.series = '';
                    return;
                }

                this.loading = true;
                this.model = '';
                this.year = '';
                this.series = '';
                this.yearOptions = [];
                this.seriesOptions = [];

                // Create the form data
                const formData = new URLSearchParams();
                formData.append('makeId', this.make);
                formData.append('form_key', this.formKeyValue);

                fetch(this.config.getModelsUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.vehicle) {
                            this.modelOptions = data.vehicle;
                        } else {
                            this.errorMessage = data.message || '<?= $block->escapeJs(__('No models available for the selected make')) ?>';
                            this.modelOptions = [];
                        }
                    })
                    .catch(error => {
                        this.errorMessage = '<?= $block->escapeJs(__('Failed to load models')) ?>';
                        console.error('Error:', error);
                        this.modelOptions = [];
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },

            onModelChange() {
                if (!this.model) {
                    this.yearOptions = [];
                    this.seriesOptions = [];
                    this.year = '';
                    this.series = '';
                    return;
                }

                this.loading = true;
                this.year = '';
                this.series = '';
                this.seriesOptions = [];

                // Create the form data
                const formData = new URLSearchParams();
                formData.append('makeId', this.make);
                formData.append('modelId', this.model);
                formData.append('form_key', this.formKeyValue);

                fetch(this.config.getYearsUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.yearRanges) {
                            this.yearOptions = data.yearRanges;
                        } else {
                            this.errorMessage = data.message || '<?= $block->escapeJs(__('No years available for the selected model')) ?>';
                            this.yearOptions = [];
                        }
                    })
                    .catch(error => {
                        this.errorMessage = '<?= $block->escapeJs(__('Failed to load years')) ?>';
                        console.error('Error:', error);
                        this.yearOptions = [];
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },

            onYearChange() {
                if (!this.year) {
                    this.seriesOptions = [];
                    this.series = '';
                    return;
                }

                this.loading = true;
                this.series = '';

                // Create the form data
                const formData = new URLSearchParams();
                formData.append('makeId', this.make);
                formData.append('modelId', this.model);
                formData.append('year', this.year);
                formData.append('form_key', this.formKeyValue);

                fetch(this.config.getSeriesUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.series) {
                            this.seriesOptions = data.series;
                        } else {
                            // Not showing error for series as it's optional
                            this.seriesOptions = [];
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.seriesOptions = [];
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },

            populateVehicleDetailsByRego(vehicle) {
                this.vehicleDetails = {
                    name: vehicle.Name || '',
                    make: vehicle.Make.Name || '',
                    model: vehicle.Model.Name || '',
                    year: vehicle.Year || '',
                    series: vehicle.Series || '',
                    pollId: vehicle.polkid || '',
                    vehicleLogicId: vehicle.vehicle_logic_id || ''
                };
                this.vehicleFound = true;
                this.scrollToVehicleDetails();
            },

            populateVehicleDetailsByManual(vehicle) {
                this.vehicleDetails = {
                    name: `${vehicle.year} ${vehicle.make} ${vehicle.model}${vehicle.series ? ' ' + vehicle.series : ''}`,
                    make: vehicle.make || '',
                    model: vehicle.model || '',
                    year: vehicle.year || '',
                    series: vehicle.series || '',
                    pollId: vehicle.poll_id || '',
                    vehicleLogicId: vehicle.vehicle_logic_id || ''
                };
                this.vehicleFound = true;
                this.scrollToVehicleDetails();
            },

            scrollToVehicleDetails() {
                setTimeout(() => {
                    document.getElementById('vehicle-details').scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 100);
            }
        }));
    });
</script>
