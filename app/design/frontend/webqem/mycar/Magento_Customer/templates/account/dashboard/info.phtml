<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>




<!-- MVC: START - temporary staging ground for garage template -->




<hr class="my-4"/><tt>Mobile - Empty garage/New Account Holder</tt><hr class="my-4"/>

<nav x-data="{index:0}" class="account-menu flex gap-2 justify-between bg-white p-2 -mx-4">
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==0)}">
        <svg class="w-8 h-8 mx-auto" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M20.625 20.25V10.8281C20.6233 10.7243 20.6009 10.6219 20.559 10.5269C20.5172 10.4319 20.4567 10.3462 20.3812 10.275L12.8812 3.45933C12.743 3.33284 12.5624 3.2627 12.375 3.2627C12.1876 3.2627 12.007 3.33284 11.8687 3.45933L4.36875 10.275C4.29328 10.3462 4.23283 10.4319 4.19097 10.5269C4.14911 10.6219 4.12668 10.7243 4.125 10.8281V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1.875 20.25H22.875" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14.625 20.25V15C14.625 14.8011 14.546 14.6103 14.4053 14.4697C14.2647 14.329 14.0739 14.25 13.875 14.25H10.875C10.6761 14.25 10.4853 14.329 10.3447 14.4697C10.204 14.6103 10.125 14.8011 10.125 15V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Home</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px]">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none" :class="{'bg-container active':(index==1)}">
            <path d="M16.125 2V6M8.125 2V6M3.125 10H21.125M5.125 4H19.125C20.2296 4 21.125 4.89543 21.125 6V20C21.125 21.1046 20.2296 22 19.125 22H5.125C4.02043 22 3.125 21.1046 3.125 20V6C3.125 4.89543 4.02043 4 5.125 4Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Bookings</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==2)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Benefits</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==3)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M2.375 11.25H23.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21.125 20.25H18.875C18.6761 20.25 18.4853 20.171 18.3447 20.0303C18.204 19.8897 18.125 19.6989 18.125 19.5V17.25H7.625V19.5C7.625 19.6989 7.54598 19.8897 7.40533 20.0303C7.26468 20.171 7.07391 20.25 6.875 20.25H4.625C4.42609 20.25 4.23532 20.171 4.09467 20.0303C3.95402 19.8897 3.875 19.6989 3.875 19.5V11.25L6.67813 4.95C6.73629 4.81672 6.83195 4.70322 6.95346 4.62333C7.07497 4.54343 7.21708 4.50058 7.3625 4.5H18.3875C18.5329 4.50058 18.675 4.54343 18.7965 4.62333C18.918 4.70322 19.0137 4.81672 19.0719 4.95L21.875 11.25V19.5C21.875 19.6989 21.796 19.8897 21.6553 20.0303C21.5147 20.171 21.3239 20.25 21.125 20.25Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Garage</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==4)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M12.625 15C15.9387 15 18.625 12.3137 18.625 9C18.625 5.68629 15.9387 3 12.625 3C9.31129 3 6.625 5.68629 6.625 9C6.625 12.3137 9.31129 15 12.625 15Z" stroke="#525455" stroke-width="1.4" stroke-miterlimit="10"/>
            <path d="M3.53125 20.25C4.45275 18.6536 5.77828 17.3278 7.37458 16.4061C8.97088 15.4844 10.7817 14.9991 12.625 14.9991C14.4683 14.9991 16.2791 15.4844 17.8754 16.4061C19.4717 17.3278 20.7972 18.6536 21.7188 20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Profile</span>
    </a>
</nav>

<header class="bg-accent -mx-4 -mb-4 px-4 pt-8 pb-10">
    <h2 class="text-white text-2xl font-bold mb-3">Welcome, Sarah</h2>
    <div class="card py-3 pl-4 pr-6 flex gap-4 items-center">
        <div class="card-icon w-[3.5rem] h-[3.5rem] flex items-center justify-center">
            <svg class="card-icon" xmlns="http://www.w3.org/2000/svg" width="34" height="29" viewBox="0 0 34 29" fill="none">
                <path d="M21.2207 24.8259C21.2207 25.6427 21.5452 26.4261 22.1228 27.0037C22.7004 27.5813 23.4838 27.9058 24.3006 27.9058C25.1175 27.9058 25.9009 27.5813 26.4785 27.0037C27.0561 26.4261 27.3806 25.6427 27.3806 24.8259C27.3806 24.009 27.0561 23.2256 26.4785 22.648C25.9009 22.0704 25.1175 21.7459 24.3006 21.7459C23.4838 21.7459 22.7004 22.0704 22.1228 22.648C21.5452 23.2256 21.2207 24.009 21.2207 24.8259V24.8259Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M5.82031 24.8259C5.82031 25.6427 6.14481 26.4261 6.72241 27.0037C7.30001 27.5813 8.08341 27.9058 8.90026 27.9058C9.71711 27.9058 10.5005 27.5813 11.0781 27.0037C11.6557 26.4261 11.9802 25.6427 11.9802 24.8259C11.9802 24.009 11.6557 23.2256 11.0781 22.648C10.5005 22.0704 9.71711 21.7459 8.90026 21.7459C8.08341 21.7459 7.30001 22.0704 6.72241 22.648C6.14481 23.2256 5.82031 24.009 5.82031 24.8259Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M27.8488 25.853H29.9459C30.4905 25.853 31.0128 25.6367 31.3978 25.2517C31.7829 24.8666 31.9992 24.3443 31.9992 23.7998C31.9894 22.1615 31.3313 20.5937 30.1688 19.4394C29.0062 18.285 27.4338 17.638 25.7955 17.6399M14.5024 8.40002C8.34249 8.40002 1.7254 12.0713 1.20113 23.7532C1.18931 24.0244 1.23248 24.2952 1.32804 24.5492C1.4236 24.8033 1.56958 25.0354 1.75718 25.2316C1.94479 25.4277 2.17015 25.5839 2.41969 25.6907C2.66924 25.7975 2.93781 25.8528 3.20925 25.853H5.26255" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M26.3557 17.6653H2.78516" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M24.1164 13.6329C27.4944 13.6329 30.2328 10.8944 30.2328 7.51644C30.2328 4.13844 27.4944 1.40002 24.1164 1.40002C20.7384 1.40002 18 4.13844 18 7.51644C18 10.8944 20.7384 13.6329 24.1164 13.6329Z" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M26.3996 8.40004C26.3996 6.85364 25.146 5.60004 23.5996 5.60004" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M28.6227 12.0237L33.1295 16.5305" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13.9141 17.3406V8.40002" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21.2223 25.8528H11.9824" stroke="#222222" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <div class="card-content flex-1">
            <h3 class="font-semibold">Let's get started!</h3>
            <a href="#" class="text-link text-sm underline underline-offset-2">Add your card to the garage</a>
        </div>
        <button class="card-closer w-5 h-5 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.625 4.375L4.375 15.625" stroke="#374151" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15.625 15.625L4.375 4.375" stroke="#374151" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>
</header>

<section class="upcoming-booking rounded-t-lg bg-white -mx-4 p-4 pb-6">
    <div class="card">
        <h2 class="font-bold text-lg mb-1">Upcoming Bookings</h2>
        <p class="text-card text-sm mb-4">Your upcoming bookings will display here.</p>
        <a href="#" class="btn btn-primary">Making a Booking</a>
    </div>
</section>

<section class="benefits-at-mycar bg-white -mx-4 px-4 pt-4 pb-4">
    <h2 class="font-bold text-[22px] mb-2">Benefits at mycar</h2>
    <p class="mb-4">Book a service, get some repairs or fit some tyres with mycar and there's a whole lot of benefits to enjoy...</p>

    <div x-data="{open: false}" class="accordion-card border rounded-lg p-4 mb-3">
        <header class="flex items-center justify-between gap-4">
            <h3 class="font-semibold">24h Free Roadside Assistance</h3>
            <button class="accordion-toggle bg-no-repeat bg-center bg-contain text-[0] w-4 h-4" @click="open = !open" :class="{'bg-[image:var(--icon-plus)]': !open, 'bg-[image:var(--icon-minus)]': open}">Open/close</button>
        </header>
        <div class="transition-all text-sm" :class="{'overflow-y-auto max-h-[60vh] pt-4': open, 'overflow-y-hidden max-h-0 pt-0': !open}">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>
    </div>

    <div x-data="{open: false}" class="accordion-card border rounded-lg p-4 mb-3">
        <header class="flex items-center justify-between gap-4">
            <h3 class="font-semibold">Brakes For Life</h3>
            <button class="accordion-toggle bg-no-repeat bg-center bg-contain text-[0] w-4 h-4" @click="open = !open" :class="{'bg-[image:var(--icon-plus)]': !open, 'bg-[image:var(--icon-minus)]': open}">Open/close</button>
        </header>
        <div class="transition-all text-sm" :class="{'overflow-y-auto max-h-[60vh] pt-4': open, 'overflow-y-hidden max-h-0 pt-0': !open}">
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </div>
    </div>

    <div x-data="{open: false}" class="accordion-card border rounded-lg p-4 mb-3">
        <header class="flex items-center justify-between gap-4">
            <h3 class="font-semibold">Tyre Care Plan</h3>
            <button class="accordion-toggle bg-no-repeat bg-center bg-contain text-[0] w-4 h-4" @click="open = !open" :class="{'bg-[image:var(--icon-plus)]': !open, 'bg-[image:var(--icon-minus)]': open}">Open/close</button>
        </header>
        <div class="transition-all text-sm" :class="{'overflow-y-auto max-h-[60vh] pt-4': open, 'overflow-y-hidden max-h-0 pt-0': !open}">
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>
    </div>

    <div x-data="{open: false}" class="accordion-card border rounded-lg p-4 mb-3">
        <header class="flex items-center justify-between gap-4">
            <h3 class="font-semibold">Tyrelife Saver Program</h3>
            <button class="accordion-toggle bg-no-repeat bg-center bg-contain text-[0] w-4 h-4" @click="open = !open" :class="{'bg-[image:var(--icon-plus)]': !open, 'bg-[image:var(--icon-minus)]': open}">Open/close</button>
        </header>
        <div class="transition-all text-sm" :class="{'overflow-y-auto max-h-[60vh] pt-4': open, 'overflow-y-hidden max-h-0 pt-0': !open}">
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>
</section>

<section class="preferred-stores bg-white -mx-4 px-4 py-4">
    <h2 class="font-bold text-xl mb-3">Preferred stores</h2>

    <div x-data="{active:false}" class="card border shadow-none grid grid-cols-[40px_1fr_auto] grid-rows-[1fr_auto] gap-x-3 mb-4">
        <svg class="w-[40px] self-center" :class="{'opacity-40':!active}" viewBox="15 15 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M60.0609 21.6001H24.6223C23.7142 21.6001 22.9299 22.2526 22.7312 23.1699L19.1992 36.34C19.1992 39.6641 21.7973 42.357 25.0042 42.357C28.2111 42.357 30.8091 39.6641 30.8091 36.34C30.8091 39.6641 33.4072 42.357 36.6141 42.357C39.821 42.357 42.419 39.6641 42.419 36.34C42.419 39.6641 42.2642 39.6641 42.2642 36.34C42.2642 39.6641 44.8622 42.357 48.0692 42.357C51.2761 42.357 53.8741 39.6641 53.8741 36.34C53.8741 39.6641 56.4721 42.357 59.6791 42.357C62.886 42.357 65.484 39.6641 65.484 36.34L61.9494 23.1699C61.7534 22.2526 60.969 21.6001 60.0609 21.6001Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M22.1543 42.3569V65.5349H50.7238" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M62.8839 47.9113V41.8408" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M58.5595 68.8819C64.3504 68.8819 69.0448 64.1875 69.0448 58.3967C69.0448 52.6058 64.3504 47.9114 58.5595 47.9114C52.7686 47.9114 48.0742 52.6058 48.0742 58.3967C48.0742 64.1875 52.7686 68.8819 58.5595 68.8819Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="#00A6E9"/>
            <path d="M64.4522 58.3714C64.4522 55.3235 61.9814 52.8528 58.9336 52.8528" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M66.2843 66.1222L74.0103 73.8482" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M29.6836 64.6446C29.6837 59.494 29.6838 54.3433 29.6839 49.1926H53.4826" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M29.6836 54.7804H48.073" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M29.6836 60.2301H48.073" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <div class="description">
            <h3 class="font-semibold">Preferred store</h3>
            <p class="text-sm" :class="{'opacity-60':!active}">No preference selected yet</p>
        </div>
        <div class="change">
            <a class="text-sm text-link underline underline-offset-4" :class="{'hidden':!active}" href="#">Change</a>
        </div>
        <footer class="col-span-3">
            <a href="#" class="btn py-1 min-h-[36px] w-full mt-3" :class="{'btn-primary':active, 'btn-secondary':!active}">Select</a>
        </footer>
    </div>

    <div x-data="{active:false}" class="card border shadow-none grid grid-cols-[40px_1fr_auto] grid-rows-[1fr_auto] gap-x-3 mb-4">
        <svg class="w-[40px] self-center" :class="{'opacity-40':!active}" viewBox="0 0 50 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7421 24.759C16.7847 24.759 19.2497 27.224 19.2497 30.2667C19.2497 33.3094 16.7847 35.7744 13.7421 35.7744C10.6994 35.7744 8.23438 33.3094 8.23438 30.2667C8.23438 27.224 10.6994 24.759 13.7421 24.759Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M34.8241 24.759C37.8668 24.759 40.3318 27.224 40.3318 30.2667C40.3318 33.3094 37.8668 35.7744 34.8241 35.7744C31.7814 35.7744 29.3164 33.3094 29.3164 30.2667C29.3164 28.0795 30.5902 26.1908 32.4366 25.3016C33.1588 24.9539 33.9686 24.759 34.8241 24.759Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M29.3159 31.9913L18.9258 32.1026" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8.66425 32.1025H4.67179C2.64496 32.1025 1 30.4576 1 28.4307V23.4822" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1.02734 23.7232V17.6797H23.9936V6.3999L32.1021 6.3999C33.4925 6.3999 34.7653 7.18566 35.3871 8.42918L39.8691 17.4153H41.0468C45.1029 17.4153 48.3904 20.7027 48.3904 24.7588V31.9911H40.139" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M28.3789 17.3531C32.6803 17.3958 37.385 17.3744 41.6864 17.4172L28.3789 17.3531Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.22794 17.2972C2.25162 15.5975 1 13.0787 1 10.2677C1 5.14947 5.14947 1 10.2677 1C15.386 1 19.5355 5.14947 19.5355 10.2677C19.5355 13.1065 18.259 15.6472 16.2488 17.3472" fill="#00A6E9"/>
            <path d="M4.22794 17.2972C2.25162 15.5975 1 13.0787 1 10.2677C1 5.14947 5.14947 1 10.2677 1C15.386 1 19.5355 5.14947 19.5355 10.2677C19.5355 13.1065 18.259 15.6472 16.2488 17.3472" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1016 6.6001C12.0346 6.6001 13.6016 8.16706 13.6016 10.1001C13.6016 12.0331 12.0346 13.6001 10.1016 13.6001C8.16853 13.6001 6.60156 12.0331 6.60156 10.1001C6.60156 8.16706 8.16853 6.6001 10.1016 6.6001Z" fill="white" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <div class="description">
            <h3 class="font-semibold">Mobile Van </h3>
            <p class="text-sm" :class="{'opacity-60':!active}">No preference selected yet</p>
        </div>
        <div class="change">
            <a class="text-sm text-link underline underline-offset-4" :class="{'hidden':!active}" href="#">Change</a>
        </div>
        <footer class="col-span-3">
            <a href="#" class="btn py-1 min-h-[36px] w-full mt-3" :class="{'btn-primary':active, 'btn-secondary':!active}">Select</a>
        </footer>
    </div>
</section>

<section class="promotions bg-white -mx-4 px-4 py-4">
    <h2 class="text-xl font-bold mb-3">Promotions</h2>
    <div class="promotions-card rounded-lg bg-accent-lightest text-white font-semibold">
        <div class="py-6 px-4">
            <h3 class="text-[32px] font-bold mt-2 mb-4 leading-[1.3]">Ready to save on servicing?</h3>
            <p class="mb-4 text-lg">For a limited time, you can save $100 off a logbook service  - thanks to Linkt Rewards.</p>
            <p class="mb-4">*T&amp;Cs apply, redeemable on booking confirmation. <a class="underline hover:opacity-70" href="#">Learn more</a></p>
            <a href="#" class="btn btn-tertiary inline-flex mt-2">Make a booking now</a>
        </div>
        <img class="block w-full h-auto" alt="" src="<?= $block->getViewFileUrl('images/promotion-bottom.png') ?>" width="344" height="245"/>
    </div>
</section>




<hr class="my-4"/><tt>Mobile - Cars In Garage with Upcoming Booking</tt><hr class="my-4"/>




<nav x-data="{index:0}" class="account-menu flex gap-2 justify-between bg-white p-2 -mx-4">
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==0)}">
        <svg class="w-8 h-8 mx-auto" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M20.625 20.25V10.8281C20.6233 10.7243 20.6009 10.6219 20.559 10.5269C20.5172 10.4319 20.4567 10.3462 20.3812 10.275L12.8812 3.45933C12.743 3.33284 12.5624 3.2627 12.375 3.2627C12.1876 3.2627 12.007 3.33284 11.8687 3.45933L4.36875 10.275C4.29328 10.3462 4.23283 10.4319 4.19097 10.5269C4.14911 10.6219 4.12668 10.7243 4.125 10.8281V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1.875 20.25H22.875" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14.625 20.25V15C14.625 14.8011 14.546 14.6103 14.4053 14.4697C14.2647 14.329 14.0739 14.25 13.875 14.25H10.875C10.6761 14.25 10.4853 14.329 10.3447 14.4697C10.204 14.6103 10.125 14.8011 10.125 15V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Home</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px]">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none" :class="{'bg-container active':(index==1)}">
            <path d="M16.125 2V6M8.125 2V6M3.125 10H21.125M5.125 4H19.125C20.2296 4 21.125 4.89543 21.125 6V20C21.125 21.1046 20.2296 22 19.125 22H5.125C4.02043 22 3.125 21.1046 3.125 20V6C3.125 4.89543 4.02043 4 5.125 4Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Bookings</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==2)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Benefits</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==3)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M2.375 11.25H23.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21.125 20.25H18.875C18.6761 20.25 18.4853 20.171 18.3447 20.0303C18.204 19.8897 18.125 19.6989 18.125 19.5V17.25H7.625V19.5C7.625 19.6989 7.54598 19.8897 7.40533 20.0303C7.26468 20.171 7.07391 20.25 6.875 20.25H4.625C4.42609 20.25 4.23532 20.171 4.09467 20.0303C3.95402 19.8897 3.875 19.6989 3.875 19.5V11.25L6.67813 4.95C6.73629 4.81672 6.83195 4.70322 6.95346 4.62333C7.07497 4.54343 7.21708 4.50058 7.3625 4.5H18.3875C18.5329 4.50058 18.675 4.54343 18.7965 4.62333C18.918 4.70322 19.0137 4.81672 19.0719 4.95L21.875 11.25V19.5C21.875 19.6989 21.796 19.8897 21.6553 20.0303C21.5147 20.171 21.3239 20.25 21.125 20.25Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Garage</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==4)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M12.625 15C15.9387 15 18.625 12.3137 18.625 9C18.625 5.68629 15.9387 3 12.625 3C9.31129 3 6.625 5.68629 6.625 9C6.625 12.3137 9.31129 15 12.625 15Z" stroke="#525455" stroke-width="1.4" stroke-miterlimit="10"/>
            <path d="M3.53125 20.25C4.45275 18.6536 5.77828 17.3278 7.37458 16.4061C8.97088 15.4844 10.7817 14.9991 12.625 14.9991C14.4683 14.9991 16.2791 15.4844 17.8754 16.4061C19.4717 17.3278 20.7972 18.6536 21.7188 20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Profile</span>
    </a>
</nav>

<header class="bg-accent -mx-4 -mb-4 px-4 pt-6 pb-10">
    <h2 class="text-white text-2xl font-bold mb-6">Welcome back, Sarah</h2>
    <div class="card py-6 pl-4 pr-6 flex gap-4 items-center bg-accent-darker relative">
        <div class="card-tag rounded-full bg-tag text-white text-sm min-h-6 px-3 absolute left-4 -top-3 flex items-center justify-center">
            CAR 555
        </div>
        <div class="card-content flex-1">
            <a href="#" class="text-white text-sm">✅ Logbook Service booked for May 15th</a>
        </div>
        <button class="card-closer w-5 h-5 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.625 4.375L4.375 15.625" stroke="#FFFFFF" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15.625 15.625L4.375 4.375" stroke="#FFFFFF" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>
</header>

<section class="upcoming-booking rounded-t-lg bg-white -mx-4 p-4 py-6">
    <h2 class="font-bold text-[22px] mb-4">Upcoming Bookings</h2>
    <div class="card border shadow-none">
        <h2 class="font-bold mb-1">Logbook Service, Tyre Fitting</h2>
        <div class="card-actions flex gap-2 items-center justify-between mb-4">
            <span class="inline-flex items-center rounded-full bg-tag-yellow text-sm px-2 min-h-6">In 1 day</span>
            <a class="inline-flex items-center gap-1 text-sm underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.25 3.125H3.75C3.40482 3.125 3.125 3.40482 3.125 3.75V16.25C3.125 16.5952 3.40482 16.875 3.75 16.875H16.25C16.5952 16.875 16.875 16.5952 16.875 16.25V3.75C16.875 3.40482 16.5952 3.125 16.25 3.125Z" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M13.75 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M6.25 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M3.125 6.875H16.875" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12.1875 11.875H7.8125" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10 9.6875V14.0625" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Add to Calendar
            </a>
        </div>
        <div class="card border shadow-none p-0 mb-4 grid grid-cols-[1fr_3fr]">
            <div class="card-car-image pl-4 py-3 pr-2 flex items-center justify-center">
                <img class="block" alt="" width="75" height="44" src="<?= $block->getViewFileUrl('images/card-car-placeholder.jpg') ?>"/>
            </div>
            <div class="card-car-details pr-4 py-3 pl-2">
                <b class="block font-semibold text-sm">Ford Territory 4SP Auto</b>
                <span class="block text-sm">4.0L 6CYL Petrol 2009</span>
                <span class="block text-link text-xs mt-2">84-Months Old</span>
            </div>
            <div x-data="{open: false}" class="card-car-inclusions px-4 py-3 col-span-2 border-t">
                <button class="accordion-toggle w-full text-left text-sm font-semibold bg-no-repeat bg-right bg-size-[16px]" @click="open = !open" :class="{'bg-[image:var(--icon-plus)]': !open, 'bg-[image:var(--icon-minus)]': open}">
                    Your service inclusions
                </button>
                <div class="transition-all text-sm" :class="{'overflow-y-auto max-h-[60vh] pt-4': open, 'overflow-y-hidden max-h-0 pt-0': !open}">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                </div>
            </div>
        </div>
        <h3 class="text-sm font-semibold mb-1">South Melbourne</h3>
        <address class="not-italic text-sm mb-4">248 Sturt Street (Corner of Miles Street) South Melbourne VIC 3205</address>
        <date class="flex gap-2 mb-4 text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M16.25 3.125H3.75C3.40482 3.125 3.125 3.40482 3.125 3.75V16.25C3.125 16.5952 3.40482 16.875 3.75 16.875H16.25C16.5952 16.875 16.875 16.5952 16.875 16.25V3.75C16.875 3.40482 16.5952 3.125 16.25 3.125Z" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13.75 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M6.25 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3.125 6.875H16.875" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            April 12 2024, 8:30am - 3:30pm
        </date>
        <p class="mb-4">
            For cancellations or changes, call the store
        </p>
        <a class="btn btn-secondary" href="tel:0292128912">Call (02) 9212 8912</a>
    </div>
    <p class="text-center text-sm my-5">
        <a class="underline text-link" href="#">View all booking activity</a>
    </p>
</section>

<section class="preferred-stores bg-white -mx-4 px-4 py-4">
    <h2 class="font-bold text-xl mb-3">Benefits</h2>
    <div class="card border shadow-none p-4">
        <h3 class="font-bold text-lg mb-4">Activated</h3>
        <div class="card p-0 mb-4">
            <h4 class="bg-accent-darker text-white font-semibold p-4 rounded-t-2xl">Brakes for life</h4>
            <div class="px-4 py-3 text-xs">
                <strong class="block font-semibold mb-1">Ford Territory 2009 (ABC 123)</strong>
                <span class="block">Cover expires 29 April, 2025</span>
            </div>
        </div>
        <div class="card p-0 mb-4">
            <h4 class="bg-accent-darker text-white text-sm font-semibold p-4 rounded-t-2xl">Tyre Care Plan</h4>
            <div class="px-4 py-3 text-xs">
                <strong class="block font-semibold mb-1">Ford Territory 2009 (ABC 123)</strong>
                <span class="block">Cover expires 29 April, 2025</span>
            </div>
            <div class="px-4 py-3 text-xs border-t">
                <strong class="block font-semibold mb-1">Ford Territory 2009 (ABC 123)</strong>
                <span class="block">Cover expires 29 April, 2025</span>
            </div>
        </div>
        <hr class="my-4"/>
        <ul class="mb-4">
            <li class="text-sm mb-2">*based off online transactions only</li>
            <li class="text-sm mb-2">*benefits redeemable post booking date </li>
        </ul>
        <a class="btn btn-secondary py-1 min-h-[36px]" href="#">View all Benefits</a>
    </div>
</section>

<section class="preferred-stores bg-white -mx-4 px-4 py-4">
    <h2 class="font-bold text-xl mb-3">Preferred stores</h2>

    <div x-data="{active:true}" class="card border shadow-none grid grid-cols-[40px_1fr_auto] grid-rows-[1fr_auto] gap-x-3 mb-4">
        <svg class="w-[40px] self-center" :class="{'opacity-40':!active}" viewBox="15 15 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M60.0609 21.6001H24.6223C23.7142 21.6001 22.9299 22.2526 22.7312 23.1699L19.1992 36.34C19.1992 39.6641 21.7973 42.357 25.0042 42.357C28.2111 42.357 30.8091 39.6641 30.8091 36.34C30.8091 39.6641 33.4072 42.357 36.6141 42.357C39.821 42.357 42.419 39.6641 42.419 36.34C42.419 39.6641 42.2642 39.6641 42.2642 36.34C42.2642 39.6641 44.8622 42.357 48.0692 42.357C51.2761 42.357 53.8741 39.6641 53.8741 36.34C53.8741 39.6641 56.4721 42.357 59.6791 42.357C62.886 42.357 65.484 39.6641 65.484 36.34L61.9494 23.1699C61.7534 22.2526 60.969 21.6001 60.0609 21.6001Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M22.1543 42.3569V65.5349H50.7238" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M62.8839 47.9113V41.8408" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M58.5595 68.8819C64.3504 68.8819 69.0448 64.1875 69.0448 58.3967C69.0448 52.6058 64.3504 47.9114 58.5595 47.9114C52.7686 47.9114 48.0742 52.6058 48.0742 58.3967C48.0742 64.1875 52.7686 68.8819 58.5595 68.8819Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="#00A6E9"></path>
            <path d="M64.4522 58.3714C64.4522 55.3235 61.9814 52.8528 58.9336 52.8528" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M66.2843 66.1222L74.0103 73.8482" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.6836 64.6446C29.6837 59.494 29.6838 54.3433 29.6839 49.1926H53.4826" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.6836 54.7804H48.073" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.6836 60.2301H48.073" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <div class="description">
            <h3 class="font-semibold">Preferred store</h3>
            <p class="text-sm" :class="{'opacity-60':!active}">Freestanding |  2.5kms away</p>
        </div>
        <div class="change">
            <a class="text-sm text-link underline underline-offset-4" :class="{'hidden':!active}" href="#">Change</a>
        </div>
        <footer class="col-span-3">
            <a href="#" class="btn py-1 min-h-[36px] w-full mt-3" :class="{'btn-primary':active, 'btn-secondary':!active}">Details</a>
        </footer>
    </div>

    <div x-data="{active:false}" class="card border shadow-none grid grid-cols-[40px_1fr_auto] grid-rows-[1fr_auto] gap-x-3 mb-4">
        <svg class="w-[40px] self-center" :class="{'opacity-40':!active}" viewBox="0 0 50 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7421 24.759C16.7847 24.759 19.2497 27.224 19.2497 30.2667C19.2497 33.3094 16.7847 35.7744 13.7421 35.7744C10.6994 35.7744 8.23438 33.3094 8.23438 30.2667C8.23438 27.224 10.6994 24.759 13.7421 24.759Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M34.8241 24.759C37.8668 24.759 40.3318 27.224 40.3318 30.2667C40.3318 33.3094 37.8668 35.7744 34.8241 35.7744C31.7814 35.7744 29.3164 33.3094 29.3164 30.2667C29.3164 28.0795 30.5902 26.1908 32.4366 25.3016C33.1588 24.9539 33.9686 24.759 34.8241 24.759Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.3159 31.9913L18.9258 32.1026" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M8.66425 32.1025H4.67179C2.64496 32.1025 1 30.4576 1 28.4307V23.4822" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M1.02734 23.7232V17.6797H23.9936V6.3999L32.1021 6.3999C33.4925 6.3999 34.7653 7.18566 35.3871 8.42918L39.8691 17.4153H41.0468C45.1029 17.4153 48.3904 20.7027 48.3904 24.7588V31.9911H40.139" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M28.3789 17.3531C32.6803 17.3958 37.385 17.3744 41.6864 17.4172L28.3789 17.3531Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.22794 17.2972C2.25162 15.5975 1 13.0787 1 10.2677C1 5.14947 5.14947 1 10.2677 1C15.386 1 19.5355 5.14947 19.5355 10.2677C19.5355 13.1065 18.259 15.6472 16.2488 17.3472" fill="#00A6E9"></path>
            <path d="M4.22794 17.2972C2.25162 15.5975 1 13.0787 1 10.2677C1 5.14947 5.14947 1 10.2677 1C15.386 1 19.5355 5.14947 19.5355 10.2677C19.5355 13.1065 18.259 15.6472 16.2488 17.3472" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1016 6.6001C12.0346 6.6001 13.6016 8.16706 13.6016 10.1001C13.6016 12.0331 12.0346 13.6001 10.1016 13.6001C8.16853 13.6001 6.60156 12.0331 6.60156 10.1001C6.60156 8.16706 8.16853 6.6001 10.1016 6.6001Z" fill="white" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <div class="description">
            <h3 class="font-semibold">Mobile Van </h3>
            <p class="text-sm" :class="{'opacity-60':!active}">No preference selected yet</p>
        </div>
        <div class="change">
            <a class="text-sm text-link underline underline-offset-4" :class="{'hidden':!active}" href="#">Change</a>
        </div>
        <footer class="col-span-3">
            <a href="#" class="btn py-1 min-h-[36px] w-full mt-3" :class="{'btn-primary':active, 'btn-secondary':!active}">Select</a>
        </footer>
    </div>
</section>

<section class="promotions bg-white -mx-4 px-4 py-4">
    <h2 class="text-xl font-bold mb-3">Promotions</h2>

    <div class="promotions-card rounded-lg bg-accent-lightest text-white p-4 grid grid-cols-[1fr_auto] gap-2 mb-6">
        <div>
            <h2 class="text-xl font-bold mb-2 leading-[1.3]">Need a replacement car?</h2>
            <p class="text-xs font-bold mb-2 text-accent-darker">$125 toward your first hire with Uber Carshare</p>
            <p class="text-xs text-accent-darker">*T&Cs apply</p>
        </div>
        <img class="flex-auto w-[60px] h-auto" alt="Uber Carshare" src="<?= $block->getViewFileUrl('images/logo-uber-carshare.png') ?>" width="736" height="400"/>
        <div class="col-span-2 rounded-lg bg-white text-body px-4 py-2 flex items-center justify-between gap-2">
            <span class="text-sm font-semibold uppercase">SD333188!</span>
            <a class="btn btn-secondary py-1 min-h-[36px]" href="#">Redeem</a>
        </div>
    </div>

    <div class="promotions-card rounded-lg bg-accent-lightest text-white font-semibold">
        <div class="py-6 px-4">
            <h2 class="text-[32px] font-bold mt-2 mb-4 leading-[1.3]">Ready to save on servicing?</h2>
            <p class="mb-4 text-lg">For a limited time, you can save $100 off a logbook service  - thanks to Linkt Rewards.</p>
            <p class="mb-4">*T&amp;Cs apply, redeemable on booking confirmation. <a class="underline hover:opacity-70" href="#">Learn more</a></p>
            <a href="#" class="btn btn-tertiary inline-flex mt-2">Make a booking now</a>
        </div>
        <img class="block w-full h-auto" alt="" src="<?= $block->getViewFileUrl('images/promotion-bottom.png') ?>" width="344" height="245">
    </div>
</section>




<hr class="my-4"/><tt>Mobile - Cars In Garage with Upcoming Booking - Not eligible for benefits</tt><hr class="my-4"/>




<nav x-data="{index:0}" class="account-menu flex gap-2 justify-between bg-white p-2 -mx-4">
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==0)}">
        <svg class="w-8 h-8 mx-auto" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M20.625 20.25V10.8281C20.6233 10.7243 20.6009 10.6219 20.559 10.5269C20.5172 10.4319 20.4567 10.3462 20.3812 10.275L12.8812 3.45933C12.743 3.33284 12.5624 3.2627 12.375 3.2627C12.1876 3.2627 12.007 3.33284 11.8687 3.45933L4.36875 10.275C4.29328 10.3462 4.23283 10.4319 4.19097 10.5269C4.14911 10.6219 4.12668 10.7243 4.125 10.8281V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1.875 20.25H22.875" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14.625 20.25V15C14.625 14.8011 14.546 14.6103 14.4053 14.4697C14.2647 14.329 14.0739 14.25 13.875 14.25H10.875C10.6761 14.25 10.4853 14.329 10.3447 14.4697C10.204 14.6103 10.125 14.8011 10.125 15V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Home</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px]">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none" :class="{'bg-container active':(index==1)}">
            <path d="M16.125 2V6M8.125 2V6M3.125 10H21.125M5.125 4H19.125C20.2296 4 21.125 4.89543 21.125 6V20C21.125 21.1046 20.2296 22 19.125 22H5.125C4.02043 22 3.125 21.1046 3.125 20V6C3.125 4.89543 4.02043 4 5.125 4Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Bookings</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==2)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Benefits</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==3)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M2.375 11.25H23.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21.125 20.25H18.875C18.6761 20.25 18.4853 20.171 18.3447 20.0303C18.204 19.8897 18.125 19.6989 18.125 19.5V17.25H7.625V19.5C7.625 19.6989 7.54598 19.8897 7.40533 20.0303C7.26468 20.171 7.07391 20.25 6.875 20.25H4.625C4.42609 20.25 4.23532 20.171 4.09467 20.0303C3.95402 19.8897 3.875 19.6989 3.875 19.5V11.25L6.67813 4.95C6.73629 4.81672 6.83195 4.70322 6.95346 4.62333C7.07497 4.54343 7.21708 4.50058 7.3625 4.5H18.3875C18.5329 4.50058 18.675 4.54343 18.7965 4.62333C18.918 4.70322 19.0137 4.81672 19.0719 4.95L21.875 11.25V19.5C21.875 19.6989 21.796 19.8897 21.6553 20.0303C21.5147 20.171 21.3239 20.25 21.125 20.25Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Garage</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==4)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M12.625 15C15.9387 15 18.625 12.3137 18.625 9C18.625 5.68629 15.9387 3 12.625 3C9.31129 3 6.625 5.68629 6.625 9C6.625 12.3137 9.31129 15 12.625 15Z" stroke="#525455" stroke-width="1.4" stroke-miterlimit="10"/>
            <path d="M3.53125 20.25C4.45275 18.6536 5.77828 17.3278 7.37458 16.4061C8.97088 15.4844 10.7817 14.9991 12.625 14.9991C14.4683 14.9991 16.2791 15.4844 17.8754 16.4061C19.4717 17.3278 20.7972 18.6536 21.7188 20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Profile</span>
    </a>
</nav>

<header class="bg-accent -mx-4 -mb-4 px-4 pt-6 pb-10">
    <h2 class="text-white text-2xl font-bold mb-6">Welcome back, Sarah</h2>
    <div class="card py-6 pl-4 pr-6 flex gap-4 items-center bg-accent-darker relative">
        <div class="card-tag rounded-full bg-tag text-white text-sm min-h-6 px-3 absolute left-4 -top-3 flex items-center justify-center">
            CAR 555
        </div>
        <div class="card-content flex-1">
            <a href="#" class="text-white text-sm">✅ Logbook Service booked for May 15th</a>
        </div>
        <button class="card-closer w-5 h-5 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.625 4.375L4.375 15.625" stroke="#FFFFFF" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M15.625 15.625L4.375 4.375" stroke="#FFFFFF" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </button>
    </div>
</header>

<section class="upcoming-booking rounded-t-lg bg-white -mx-4 p-4 py-6">
    <h2 class="font-bold text-[22px] mb-4">Upcoming Bookings</h2>
    <div class="card border shadow-none">
        <h2 class="font-bold mb-1">Logbook Service, Tyre Fitting</h2>
        <div class="card-actions flex gap-2 items-center justify-between mb-4">
            <span class="inline-flex items-center rounded-full bg-tag-yellow text-sm px-2 min-h-6">In 1 day</span>
            <a class="inline-flex items-center gap-1 text-sm underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.25 3.125H3.75C3.40482 3.125 3.125 3.40482 3.125 3.75V16.25C3.125 16.5952 3.40482 16.875 3.75 16.875H16.25C16.5952 16.875 16.875 16.5952 16.875 16.25V3.75C16.875 3.40482 16.5952 3.125 16.25 3.125Z" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M13.75 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M6.25 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M3.125 6.875H16.875" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M12.1875 11.875H7.8125" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M10 9.6875V14.0625" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                Add to Calendar
            </a>
        </div>
        <div class="card border shadow-none p-0 mb-4 grid grid-cols-[1fr_3fr]">
            <div class="card-car-image pl-4 py-3 pr-2 flex items-center justify-center">
                <img class="block" alt="" width="75" height="44" src="https://new-mycar.ddev.site/static/version1748487756/frontend/webqem/mycar/en_AU/images/card-car-placeholder.jpg">
            </div>
            <div class="card-car-details pr-4 py-3 pl-2">
                <b class="block font-semibold text-sm">Ford Territory 4SP Auto</b>
                <span class="block text-sm">4.0L 6CYL Petrol 2009</span>
                <span class="block text-link text-xs mt-2">84-Months Old</span>
            </div>
            <div x-data="{open: false}" class="card-car-inclusions px-4 py-3 col-span-2 border-t">
                <button class="accordion-toggle w-full text-left text-sm font-semibold bg-no-repeat bg-right bg-size-[16px] bg-[image:var(--icon-plus)]" @click="open = !open" :class="{'bg-[image:var(--icon-plus)]': !open, 'bg-[image:var(--icon-minus)]': open}">
                    Your service inclusions
                </button>
                <div class="transition-all text-sm overflow-y-hidden max-h-0 pt-0" :class="{'overflow-y-auto max-h-[60vh] pt-4': open, 'overflow-y-hidden max-h-0 pt-0': !open}">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                </div>
            </div>
        </div>
        <h3 class="text-sm font-semibold mb-1">South Melbourne</h3>
        <address class="not-italic text-sm mb-4">248 Sturt Street (Corner of Miles Street) South Melbourne VIC 3205</address>
        <date class="flex gap-2 mb-4 text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M16.25 3.125H3.75C3.40482 3.125 3.125 3.40482 3.125 3.75V16.25C3.125 16.5952 3.40482 16.875 3.75 16.875H16.25C16.5952 16.875 16.875 16.5952 16.875 16.25V3.75C16.875 3.40482 16.5952 3.125 16.25 3.125Z" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M13.75 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M6.25 1.875V4.375" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M3.125 6.875H16.875" stroke="#525455" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            April 12 2024, 8:30am - 3:30pm
        </date>
        <p class="mb-4">
            For cancellations or changes, call the store
        </p>
        <a class="btn btn-secondary" href="tel:0292128912">Call (02) 9212 8912</a>
    </div>
    <p class="text-center text-sm my-5">
        <a class="underline text-link" href="#">View all booking activity</a>
    </p>
</section>

<section class="preferred-stores bg-white -mx-4 px-4 py-4">
    <h2 class="font-bold text-xl mb-3">Benefits</h2>
    <div class="card border shadow-none p-4">
        <h3 class="font-bold text-lg mb-4">Activated</h3>
        <p class="text-sm mb-2">None</p>
        <a class="btn btn-secondary py-1 min-h-[36px]" href="#">View all Benefits</a>
    </div>
</section>

<section class="preferred-stores bg-white -mx-4 px-4 py-4">
    <h2 class="font-bold text-xl mb-3">Preferred stores</h2>

    <div x-data="{active:true}" class="card border shadow-none grid grid-cols-[40px_1fr_auto] grid-rows-[1fr_auto] gap-x-3 mb-4">
        <svg class="w-[40px] self-center" :class="{'opacity-40':!active}" viewBox="15 15 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M60.0609 21.6001H24.6223C23.7142 21.6001 22.9299 22.2526 22.7312 23.1699L19.1992 36.34C19.1992 39.6641 21.7973 42.357 25.0042 42.357C28.2111 42.357 30.8091 39.6641 30.8091 36.34C30.8091 39.6641 33.4072 42.357 36.6141 42.357C39.821 42.357 42.419 39.6641 42.419 36.34C42.419 39.6641 42.2642 39.6641 42.2642 36.34C42.2642 39.6641 44.8622 42.357 48.0692 42.357C51.2761 42.357 53.8741 39.6641 53.8741 36.34C53.8741 39.6641 56.4721 42.357 59.6791 42.357C62.886 42.357 65.484 39.6641 65.484 36.34L61.9494 23.1699C61.7534 22.2526 60.969 21.6001 60.0609 21.6001Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M22.1543 42.3569V65.5349H50.7238" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M62.8839 47.9113V41.8408" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M58.5595 68.8819C64.3504 68.8819 69.0448 64.1875 69.0448 58.3967C69.0448 52.6058 64.3504 47.9114 58.5595 47.9114C52.7686 47.9114 48.0742 52.6058 48.0742 58.3967C48.0742 64.1875 52.7686 68.8819 58.5595 68.8819Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="#00A6E9"></path>
            <path d="M64.4522 58.3714C64.4522 55.3235 61.9814 52.8528 58.9336 52.8528" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M66.2843 66.1222L74.0103 73.8482" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.6836 64.6446C29.6837 59.494 29.6838 54.3433 29.6839 49.1926H53.4826" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.6836 54.7804H48.073" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.6836 60.2301H48.073" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <div class="description">
            <h3 class="font-semibold">Preferred store</h3>
            <p class="text-sm" :class="{'opacity-60':!active}">Freestanding |  2.5kms away</p>
        </div>
        <div class="change">
            <a class="text-sm text-link underline underline-offset-4" :class="{'hidden':!active}" href="#">Change</a>
        </div>
        <footer class="col-span-3">
            <a href="#" class="btn py-1 min-h-[36px] w-full mt-3" :class="{'btn-primary':active, 'btn-secondary':!active}">Details</a>
        </footer>
    </div>

    <div x-data="{active:false}" class="card border shadow-none grid grid-cols-[40px_1fr_auto] grid-rows-[1fr_auto] gap-x-3 mb-4">
        <svg class="w-[40px] self-center" :class="{'opacity-40':!active}" viewBox="0 0 50 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7421 24.759C16.7847 24.759 19.2497 27.224 19.2497 30.2667C19.2497 33.3094 16.7847 35.7744 13.7421 35.7744C10.6994 35.7744 8.23438 33.3094 8.23438 30.2667C8.23438 27.224 10.6994 24.759 13.7421 24.759Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M34.8241 24.759C37.8668 24.759 40.3318 27.224 40.3318 30.2667C40.3318 33.3094 37.8668 35.7744 34.8241 35.7744C31.7814 35.7744 29.3164 33.3094 29.3164 30.2667C29.3164 28.0795 30.5902 26.1908 32.4366 25.3016C33.1588 24.9539 33.9686 24.759 34.8241 24.759Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M29.3159 31.9913L18.9258 32.1026" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M8.66425 32.1025H4.67179C2.64496 32.1025 1 30.4576 1 28.4307V23.4822" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M1.02734 23.7232V17.6797H23.9936V6.3999L32.1021 6.3999C33.4925 6.3999 34.7653 7.18566 35.3871 8.42918L39.8691 17.4153H41.0468C45.1029 17.4153 48.3904 20.7027 48.3904 24.7588V31.9911H40.139" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M28.3789 17.3531C32.6803 17.3958 37.385 17.3744 41.6864 17.4172L28.3789 17.3531Z" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.22794 17.2972C2.25162 15.5975 1 13.0787 1 10.2677C1 5.14947 5.14947 1 10.2677 1C15.386 1 19.5355 5.14947 19.5355 10.2677C19.5355 13.1065 18.259 15.6472 16.2488 17.3472" fill="#00A6E9"></path>
            <path d="M4.22794 17.2972C2.25162 15.5975 1 13.0787 1 10.2677C1 5.14947 5.14947 1 10.2677 1C15.386 1 19.5355 5.14947 19.5355 10.2677C19.5355 13.1065 18.259 15.6472 16.2488 17.3472" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1016 6.6001C12.0346 6.6001 13.6016 8.16706 13.6016 10.1001C13.6016 12.0331 12.0346 13.6001 10.1016 13.6001C8.16853 13.6001 6.60156 12.0331 6.60156 10.1001C6.60156 8.16706 8.16853 6.6001 10.1016 6.6001Z" fill="white" stroke="#222222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <div class="description">
            <h3 class="font-semibold">Mobile Van </h3>
            <p class="text-sm" :class="{'opacity-60':!active}">No preference selected yet</p>
        </div>
        <div class="change">
            <a class="text-sm text-link underline underline-offset-4" :class="{'hidden':!active}" href="#">Change</a>
        </div>
        <footer class="col-span-3">
            <a href="#" class="btn py-1 min-h-[36px] w-full mt-3" :class="{'btn-primary':active, 'btn-secondary':!active}">Select</a>
        </footer>
    </div>
</section>

<section class="promotions bg-white -mx-4 px-4 py-4">
    <h2 class="text-xl font-bold mb-3">Promotions</h2>

    <div class="promotions-card rounded-lg bg-accent-lightest text-white p-4 grid grid-cols-[1fr_auto] gap-2 mb-6">
        <div>
            <h2 class="text-xl font-bold mb-2 leading-[1.3]">Need a replacement car?</h2>
            <p class="text-xs font-bold mb-2 text-accent-darker">$125 toward your first hire with Uber Carshare</p>
            <p class="text-xs text-accent-darker">*T&Cs apply</p>
        </div>
        <img class="flex-auto w-[60px] h-auto" alt="Uber Carshare" src="<?= $block->getViewFileUrl('images/logo-uber-carshare.png') ?>" width="736" height="400"/>
        <div class="col-span-2 rounded-lg bg-white text-body px-4 py-2 flex items-center justify-between gap-2">
            <span class="text-sm font-semibold uppercase">SD333188!</span>
            <a class="btn btn-secondary py-1 min-h-[36px]" href="#">Redeem</a>
        </div>
    </div>

    <div class="promotions-card rounded-lg bg-accent-lightest text-white font-semibold">
        <div class="py-6 px-4">
            <h2 class="text-[32px] font-bold mt-2 mb-4 leading-[1.3]">Ready to save on servicing?</h2>
            <p class="mb-4 text-lg">For a limited time, you can save $100 off a logbook service  - thanks to Linkt Rewards.</p>
            <p class="mb-4">*T&amp;Cs apply, redeemable on booking confirmation. <a class="underline hover:opacity-70" href="#">Learn more</a></p>
            <a href="#" class="btn btn-tertiary inline-flex mt-2">Make a booking now</a>
        </div>
        <img class="block w-full h-auto" alt="" src="<?= $block->getViewFileUrl('images/promotion-bottom.png') ?>" width="344" height="245">
    </div>
</section>




<hr class="my-4"/><tt>Mobile- Garage -Add Car Pop-up</tt><hr class="my-4"/>




<section x-data="{ showModal:false, stateSelected:false }">
    <button @click="showModal=true" class="btn">Show popup</button>
    <div id="login-register-popup" x-show="showModal" class="fixed inset-0 z-50 overflow-y-auto" @keydown.escape.window="showModal=false" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="flex items-start md:items-center justify-center min-h-screen">
            <div class="modal-overlay fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="showModal=false"></div>
            <form @submit.prevent="{}" class="modal-content bg-white overflow-hidden w-full md:max-w-md min-h-[calc(100vh-7rem)] md:min-h-0 top-[7rem] md:top-0 z-10 relative grid grid-rows-[auto_1fr_auto]" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-y-4" x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="modal-header bg-white flex items-center justify-center min-h-header-mobile shadow-header">
                    <h2 class="text-lg font-semibold">Add your car</h2>
                    <button type="button" class="absolute top-4 right-4 text-gray-400 hover:text-gray-500" @click="showModal=false">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M15.625 4.375L4.375 15.625" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M15.625 15.625L4.375 4.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body px-4 py-6 text-center">
                    <label class="field relative mb-4">
                        <span class="form-label w-full text-base justify-center" for="rego">Enter your car rego</span>
                        <input class="form-input w-full text-base text-center" type="text" name="rego" placeholder=" " />
                    </label>
                    <label class="field mb-4">
                        <span class="form-label">Select State</span>
                        <select class="form-select w-full" x-ref="stateSelect" x-on:change="stateSelected=($refs.stateSelect.selectedIndex>0)" :class="{'text-primary-lighter':!stateSelected}">
                            <option>Select State</option>
                            <option>NSW</option>
                        </select>
                    </label>
                    <a href="#" class="text-base text-sm font-semibold underline underline-offset-4">Find my car by make, model and year</a>
                </div>
                <div class="modal-actions p-4">
                    <button class="btn btn-primary w-full">Find my car</button>
                </div>
            </form>
        </div>
    </div>
</section>



<hr class="my-4"/><tt>Mobile- Garage -Confirm Car</tt><hr class="my-4"/>



<section x-data="{ showModal:false }">
    <button @click="showModal=true" class="btn">Show popup</button>
    <div id="login-register-popup" x-show="showModal" class="fixed inset-0 z-50 overflow-y-auto" @keydown.escape.window="showModal=false" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="flex items-start md:items-center justify-center min-h-screen">
            <div class="modal-overlay fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="showModal=false"></div>
            <form @submit.prevent="{}" class="modal-content bg-white overflow-hidden w-full md:max-w-md min-h-[calc(100vh-7rem)] md:min-h-0 top-[7rem] md:top-0 z-10 relative grid grid-rows-[auto_1fr_auto]" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-y-4" x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="modal-header bg-white flex items-center justify-center min-h-header-mobile shadow-header">
                    <h2 class="text-lg font-semibold">Confirm car</h2>
                    <button type="button" class="absolute top-4 right-4 text-gray-400 hover:text-gray-500" @click="showModal=false">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M15.625 4.375L4.375 15.625" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M15.625 15.625L4.375 4.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body px-4 py-6 text-center">
                    <img class="inline-block mb-6" alt="" width="160" height="96" src="<?= $block->getViewFileUrl('images/select-car-placeholder.jpg') ?>"/>
                    <h3 class="text-lg font-bold mb-2">Ford Territory</h3>
                    <p class="text-sm mb-2">4SP Auto 4.0L 6CYL Petrol 2009</p>
                    <p class="text-sm text-link mb-4">84 Months Old</p>
                    <a href="#" class="text-base text-sm font-semibold underline underline-offset-4">Not right? Find car by make, model and year</a>
                </div>
                <div class="modal-actions p-4">
                    <button class="btn btn-primary w-full">Add Car to Garage</button>
                </div>
            </form>
        </div>
    </div>
</section>



<hr class="my-4"/><tt>Mobile - Garage - Add car by make/model/year </tt><hr class="my-4"/>



<section x-data="{ showModal:false, selections:[] }">
    <button @click="showModal=true" class="btn">Show popup</button>
    <div id="login-register-popup" x-show="showModal" class="fixed inset-0 z-50 overflow-y-auto" @keydown.escape.window="showModal=false" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="flex items-start md:items-center justify-center min-h-screen">
            <div class="modal-overlay fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="showModal=false"></div>
            <form @submit.prevent="{}" class="modal-content bg-white overflow-hidden w-full md:max-w-md min-h-[calc(100vh-7rem)] md:min-h-0 top-[7rem] md:top-0 z-10 relative grid grid-rows-[auto_1fr_auto]" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-y-4" x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="modal-header bg-white flex items-center justify-center min-h-header-mobile shadow-header">
                    <h2 class="text-lg font-semibold">Add your car</h2>
                    <button type="button" class="absolute top-4 right-4 text-gray-400 hover:text-gray-500" @click="showModal=false">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M15.625 4.375L4.375 15.625" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M15.625 15.625L4.375 4.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body px-4 py-6 text-center">
                    <label class="field mb-4">
                        <span class="form-label">Make</span>
                        <select class="form-select w-full" x-ref="makeSelect" x-on:change="let index = $refs.makeSelect.selectedIndex; if(index>0) { selections[0]=index } else { selections.length=0 }" :class="{'text-primary-lighter':($refs.makeSelect.selectedIndex<1)}"><option>Make</option><option>Lorem</option></select>
                    </label>
                    <label class="field mb-4">
                        <span class="form-label">Model</span>
                        <select class="form-select w-full" x-ref="modelSelect" x-on:change="let index = $refs.modelSelect.selectedIndex; if(index>0) { selections[1]=index } else { selections.length=1 }" :class="{'text-primary-lighter':($refs.modelSelect.selectedIndex<1)}" x-bind:disabled="selections.length<1"><option>Model</option><option>Ipsum</option></select>
                    </label>
                    <label class="field mb-4">
                        <span class="form-label">Year</span>
                        <select class="form-select w-full" x-ref="yearSelect" x-on:change="let index = $refs.yearSelect.selectedIndex; if(index>0) { selections[2]=index } else { selections.length=2 }" :class="{'text-primary-lighter':($refs.yearSelect.selectedIndex<1)}" x-bind:disabled="selections.length<2"><option>Year</option><option>Dolor</option></select>
                    </label>
                    <label class="field mb-4">
                        <span class="form-label">Series</span>
                        <select class="form-select w-full" x-ref="seriesSelect" x-on:change="let index = $refs.seriesSelect.selectedIndex; if(index>0) { selections[3]=index } else { selections.length=3 }" :class="{'text-primary-lighter':($refs.seriesSelect.selectedIndex<1)}" x-bind:disabled="selections.length<3"><option>Series</option><option>Sit</option></select>
                    </label>
                    <a href="#" class="text-base text-sm font-semibold underline underline-offset-4">Not right? Find car by make, model and year</a>
                </div>
                <div class="modal-actions p-4">
                    <button class="btn btn-primary w-full">Find my car</button>
                </div>
            </form>
        </div>
    </div>
</section>




<hr class="my-4"/><tt>Mobile- Garage -Confirm Car</tt><hr class="my-4"/>




<section x-data="{ showModal:false }">
    <button @click="showModal=true" class="btn">Show popup</button>
    <div id="login-register-popup" x-show="showModal" class="fixed inset-0 z-50 overflow-y-auto" @keydown.escape.window="showModal=false" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="flex items-start md:items-center justify-center min-h-screen">
            <div class="modal-overlay fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="showModal=false"></div>
            <form @submit.prevent="{}" class="modal-content bg-white overflow-hidden w-full md:max-w-md min-h-[calc(100vh-7rem)] md:min-h-0 top-[7rem] md:top-0 z-10 relative grid grid-rows-[auto_1fr_auto]" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-y-4" x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="modal-header bg-white flex items-center justify-center min-h-header-mobile shadow-header">
                    <h2 class="text-lg font-semibold">Confirm car</h2>
                    <button type="button" class="absolute top-4 right-4 text-gray-400 hover:text-gray-500" @click="showModal=false">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M15.625 4.375L4.375 15.625" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M15.625 15.625L4.375 4.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body px-4 py-6 text-center">
                    <img class="inline-block mb-6" alt="" width="160" height="96" src="<?= $block->getViewFileUrl('images/select-car-placeholder.jpg') ?>"/>
                    <h3 class="text-lg font-bold mb-2">Ford Territory</h3>
                    <p class="text-sm mb-2">4SP Auto 4.0L 6CYL Petrol 2009</p>
                    <p class="text-sm text-link mb-4">84 Months Old</p>
                    <a href="#" class="text-base text-sm font-semibold underline underline-offset-4">Not right? Find car by rego</a>
                </div>
                <div class="modal-actions p-4">
                    <button class="btn btn-primary w-full">Add Car to Garage</button>
                </div>
            </form>
        </div>
    </div>
</section>




<hr class="my-4"/><tt>Mobile - Garage - populated garage</tt><hr class="my-4"/>

<nav x-data="{index:3}" class="account-menu flex gap-2 justify-between bg-white p-2 -mx-4">
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==0)}">
        <svg class="w-8 h-8 mx-auto" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M20.625 20.25V10.8281C20.6233 10.7243 20.6009 10.6219 20.559 10.5269C20.5172 10.4319 20.4567 10.3462 20.3812 10.275L12.8812 3.45933C12.743 3.33284 12.5624 3.2627 12.375 3.2627C12.1876 3.2627 12.007 3.33284 11.8687 3.45933L4.36875 10.275C4.29328 10.3462 4.23283 10.4319 4.19097 10.5269C4.14911 10.6219 4.12668 10.7243 4.125 10.8281V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1.875 20.25H22.875" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14.625 20.25V15C14.625 14.8011 14.546 14.6103 14.4053 14.4697C14.2647 14.329 14.0739 14.25 13.875 14.25H10.875C10.6761 14.25 10.4853 14.329 10.3447 14.4697C10.204 14.6103 10.125 14.8011 10.125 15V20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Home</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px]">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none" :class="{'bg-container active':(index==1)}">
            <path d="M16.125 2V6M8.125 2V6M3.125 10H21.125M5.125 4H19.125C20.2296 4 21.125 4.89543 21.125 6V20C21.125 21.1046 20.2296 22 19.125 22H5.125C4.02043 22 3.125 21.1046 3.125 20V6C3.125 4.89543 4.02043 4 5.125 4Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Bookings</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==2)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Benefits</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==3)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M2.375 11.25H23.375" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21.125 20.25H18.875C18.6761 20.25 18.4853 20.171 18.3447 20.0303C18.204 19.8897 18.125 19.6989 18.125 19.5V17.25H7.625V19.5C7.625 19.6989 7.54598 19.8897 7.40533 20.0303C7.26468 20.171 7.07391 20.25 6.875 20.25H4.625C4.42609 20.25 4.23532 20.171 4.09467 20.0303C3.95402 19.8897 3.875 19.6989 3.875 19.5V11.25L6.67813 4.95C6.73629 4.81672 6.83195 4.70322 6.95346 4.62333C7.07497 4.54343 7.21708 4.50058 7.3625 4.5H18.3875C18.5329 4.50058 18.675 4.54343 18.7965 4.62333C18.918 4.70322 19.0137 4.81672 19.0719 4.95L21.875 11.25V19.5C21.875 19.6989 21.796 19.8897 21.6553 20.0303C21.5147 20.171 21.3239 20.25 21.125 20.25Z" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Garage</span>
    </a>
    <a href="#" class="flex flex-1 flex-col gap-1 items-center justify-center rounded-lg text-center min-h-[60px] hover:underline" :class="{'bg-container active':(index==4)}">
        <svg class="w-8 h-8 mx-auto mb-1" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
            <path d="M12.625 15C15.9387 15 18.625 12.3137 18.625 9C18.625 5.68629 15.9387 3 12.625 3C9.31129 3 6.625 5.68629 6.625 9C6.625 12.3137 9.31129 15 12.625 15Z" stroke="#525455" stroke-width="1.4" stroke-miterlimit="10"/>
            <path d="M3.53125 20.25C4.45275 18.6536 5.77828 17.3278 7.37458 16.4061C8.97088 15.4844 10.7817 14.9991 12.625 14.9991C14.4683 14.9991 16.2791 15.4844 17.8754 16.4061C19.4717 17.3278 20.7972 18.6536 21.7188 20.25" stroke="#525455" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="block text-xs">Profile</span>
    </a>
</nav>

<section class="my-garage masthead-generic bg-white -mx-4 px-4 py-6">
    <div class="card bg-white padding-4">
        <h2>My Garage</h2>
        <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
        <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
        <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
        <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
        <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
        <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
    </div>
</section>












<br/><br/><br/><br/><br/><br/><br/>




<!-- MVC: END - temporary staging ground for garage template -->




<h2 class="mb-6 text-2xl block-title">
    <?= $escaper->escapeHtml(__('Account Information')) ?>
</h2>
<div class="flex flex-wrap justify-between -m-4">
    <div class="w-full p-4 lg:w-1/2">
        <div class="flex flex-col h-full p-8 sm:flex-row card">
            <div
                class="inline-flex items-center justify-center shrink-0 w-16 h-16 mb-4
                    rounded-full sm:mr-8 sm:mb-0 bg-container-darker text-secondary">
                <?= $heroicons->userHtml('', 32, 32, ['aria-hidden' => 'true']) ?>
            </div>
            <div class="grow">
                <h3 class="mb-3 text-lg font-medium text-gray-900 title-font">
                    <span><?= $escaper->escapeHtml(__('Contact Information')) ?></span>
                </h3>
                <p>
                    <?= $escaper->escapeHtml($block->getName()) ?><br>
                    <?= ''//$escaper->escapeHtml($block->getCustomer()->getCompany()) ?><br>
                    <?= $escaper->escapeHtml($block->getCustomer()->getEmail()) ?><br>
                </p>
                <?= $block->getChildHtml('customer.account.dashboard.info.extra'); ?>
                <a
                    class="inline-flex items-center w-full mt-3 md:text-sm text-secondary hover:text-secondary-darker"
                    href="<?= $escaper->escapeUrl($block->getUrl('customer/account/edit')) ?>"
                    aria-label="<?= $escaper->escapeHtml(__('Edit contact information')) ?>"
                >
                    <span><?= $escaper->escapeHtml(__('Edit')) ?></span>
                    <?= $heroicons->arrowRightHtml('ml-2', 16, 16, ['aria-hidden' => 'true']); ?>
                </a>
                <a
                    class="inline-flex items-center w-full md:text-sm text-secondary hover:text-secondary-darker"
                    href="<?= $escaper->escapeUrl($block->getChangePasswordUrl()) ?>"
                >
                    <?= $escaper->escapeHtml(__('Change Password')) ?>
                    <?= $heroicons->arrowRightHtml('ml-2', 16, 16, ['aria-hidden' => 'true']); ?>
                </a>
            </div>
        </div>
    </div>
    <?php if ($block->isNewsletterEnabled()): ?>
        <div class="w-full p-4 lg:w-1/2">
            <div class="flex flex-col h-full p-8 border border-gray-200 sm:flex-row card">
                <div
                    class="inline-flex items-center justify-center shrink-0 w-16 h-16 mb-4
                        rounded-full sm:mr-8 sm:mb-0 bg-container-darker text-secondary">
                    <?= $heroicons->mailHtml('', 32, 32, ['aria-hidden' => 'true']) ?>
                </div>
                <div class="grow">
                    <h3 class="mb-3 text-lg font-medium text-gray-900 title-font">
                        <?= $escaper->escapeHtml(__('Newsletters')) ?>
                    </h3>
                    <p>
                        <?php if ($block->getIsSubscribed()): ?>
                            <?= $escaper->escapeHtml(__('You are subscribed to "General Subscription".')) ?>
                        <?php else: ?>
                            <?= $escaper->escapeHtml(__('You aren\'t subscribed to our newsletter.')) ?>
                        <?php endif; ?>
                    </p>
                    <a
                        class="mt-3 w-full inline-flex items-center md:text-sm text-secondary hover:text-secondary-darker"
                        href="<?= $escaper->escapeUrl($block->getUrl('newsletter/manage')) ?>"
                        aria-label="<?= $escaper->escapeHtml(__('Edit newsletters')) ?>"
                    >
                        <span><?= $escaper->escapeHtml(__('Edit')) ?></span>
                        <?= $heroicons->arrowRightHtml('ml-2', 16, 16, ['aria-hidden' => 'true']); ?>
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?= $block->getChildHtml('additional_blocks'); ?>
</div>
